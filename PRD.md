# Token兑换项目产品需求文档 (PRD)

## 项目概述

本项目实现一个代币兑换机制，用户可以将A代币兑换为B代币，其中A和B是同一个币种，但B代币的数量是A代币的扩大版本（通过兑换率计算）。

## 核心业务流程

### 1. 用户兑换操作
- **钱包连接**: 支持MetaMask、OKX、TP等主流钱包
- **输入兑换数量**: 用户输入要兑换的A代币数量
- **自动计算**: 系统根据兑换率自动计算B代币数量
  ```
  B代币数量 = A代币数量 × 兑换率
  ```
- **确认兑换**: 用户提交兑换请求并签署交易

### 2. 链上执行流程
- **A代币销毁**: 用户A代币转入兑换合约后立即销毁
- **B代币发放**: 合约从预分配的B代币池中发放对应数量给用户

### 3. 超期处理机制
- **兑换期限**: 60天兑换期
- **自动归集**: 兑换期结束后，未领取的B代币自动归集至项目方地址

## 技术实现要求

### 1. 代币安全机制
- **A代币销毁**: 转账后立即销毁（需代币合约支持burn函数）
- **B代币存储**: 
  - 分散存储在3个独立合约地址（降低单点风险）
  - 代币分批转入，避免一次性大量转账

### 2. 权限控制
- **项目方权限**: 仅项目方地址可调用`reclaimUnclaimed()`函数
- **合约锁定**: 兑换期结束后锁定合约写操作

### 3. 防误操作保护
- **前端校验**: 
  - 输入值实时校验（最小值/余额不足提示）
  - 显示预估Gas费用
- **用户体验**: 提供清晰的错误提示和操作指引

### 4. 代币转账限制
- **流动性池拦截**: 实时拦截向流动性池的转账
- **黑名单机制**: 
  - 可动态更新黑名单（覆盖新创建的DEX池）
  - 不影响普通用户间转账

## 测试用例

### 功能测试
1. **正常兑换流程**: 验证A代币销毁和B代币发放的完整流程
2. **超期回收机制**: 测试60天后B代币自动归集功能
3. **多地址分配**: 验证B代币在3个独立合约间的分配逻辑
4. **边界测试**: 
   - 极小数量值测试
   - 极大数量值测试
   - 零数量测试

### 安全测试
1. **权限测试**: 验证非项目方无法调用回收函数
2. **时间锁定**: 验证兑换期结束后合约写操作被锁定
3. **重入攻击**: 测试合约重入攻击防护
4. **溢出测试**: 验证数值溢出保护

### 集成测试
1. **钱包集成**: 测试各种钱包的连接和交易签署
2. **Gas优化**: 验证交易Gas费用在合理范围内
3. **网络兼容**: 测试在不同网络上的部署和运行

## 技术架构

### 智能合约
- **兑换合约**: 处理A到B的兑换逻辑
- **代币合约**: A代币和B代币的ERC20实现
- **管理合约**: 处理权限控制和超期回收

### 前端应用
- **钱包连接**: 支持多种钱包的集成
- **兑换界面**: 用户友好的兑换操作界面
- **状态管理**: 实时显示兑换状态和余额
web 端

1. 用户操作
  - 用户连接钱包（支持MetaMask、okx、tp等主流钱包）
  - 输入要兑换的A代币数量
  - 系统自动计算B代币数量（B数量 = A数量 × 兑换率）
  - 用户提交兑换请求，签署交易


2. 交易历史
  - 用户最近的兑换记录

## 风险控制

1. **技术风险**: 通过多轮测试和安全审计降低
2. **操作风险**: 设置合理的权限控制和操作限制
3. **市场风险**: 通过兑换率机制控制代币流通
4. **安全风险**: 实施多重安全防护措施

主网是Arb
测试网是 Arb goerli
