name: CI

on:
  push:
  pull_request:
  workflow_dispatch:

env:
  FOUNDRY_PROFILE: ci

jobs:
  check:
    name: Foundry project
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Cache Foundry libraries
        uses: actions/cache@v4
        with:
          path: ~/.foundry/share/forge/lib
          key: ${{ runner.os }}-foundry-libs-${{ hashFiles('foundry.toml') }}
          restore-keys: |
            ${{ runner.os }}-foundry-libs-

      - name: Install dependencies
        run: forge install

      - name: Show Forge version
        run: |
          forge --version

      - name: Run Forge fmt
        run: |
          forge fmt --check
        id: fmt

      - name: Run Forge build
        run: |
          forge build --sizes
        id: build

      - name: Run Forge tests
        run: |
          forge test -vvv
        id: test
