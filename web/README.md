# Bitroot Token Exchange

A decentralized token exchange application for converting BRT tokens to BRT(one) tokens with a 60-day exchange period.

## Features

- **Token Exchange**: Convert BRT tokens to BRT(one) tokens at a 1:9523.8 ratio
- **Multi-Wallet Support**: MetaMask, OKX, TokenPocket, WalletConnect, and Coinbase Wallet
- **Countdown Timer**: Real-time countdown showing remaining exchange period
- **Transaction History**: View all exchange transactions on-chain with automated indexing
- **Debug Interface**: Comprehensive debugging and monitoring tools
- **Arbitrum Support**: Supports both Arbitrum mainnet and Goerli testnet
- **Modern UI**: Clean, responsive design with dark theme

## Tech Stack

- **Frontend**: Next.js 15.3.4, React 19, TypeScript
- **Styling**: Tailwind CSS 4
- **Web3**: Wagmi 2.15.6, Viem 2.31.7
- **Database**: SQLite with better-sqlite3
- **Package Manager**: Bun
- **Blockchain**: Bitroot Test Network (1337), Local Foundry (31337)

## Prerequisites

- [Bun](https://bun.sh/) installed
- A Web3 wallet (MetaMask, OKX, etc.)
- Access to supported networks

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd redeemer/web
```

2. Install dependencies:
```bash
bun install
```

3. Create environment file:
```bash
cp env.example .env.local
```

4. Update environment variables (optional - defaults are configured):
```env
# All configurations are now managed in src/config/contracts.ts
# No environment variables needed for basic functionality
```

## Development

Start the development server:
```bash
bun run dev
```

The application will be available at `http://localhost:3000`

## Transaction History Service

The application includes an automated transaction indexing service with comprehensive debugging:

### Features
- **Multi-network support** - Monitors all configured networks simultaneously
- **Smart synchronization** - Batch processing with intelligent frequency control
- **Configurable start blocks** - Per-network configuration in `src/config/contracts.ts`
- **Debug logging** - Detailed emoji-coded logs for easy troubleshooting
- **Status monitoring** - Real-time status and statistics via API

### Configuration

Network configurations are managed in `src/config/contracts.ts`:

```typescript
export const contractAddresses = {
  1337: {
    tokenExchange: '0x...',
    rpcUrl: 'https://test-rpc.bitroot.co/',
    startBlock: 100000, // Indexing starts from this block
  },
  31337: {
    tokenExchange: '0x...',
    rpcUrl: 'http://127.0.0.1:8545',
    startBlock: 1,
  },
}
```

### Manual Control & Testing

**Quick Test:**
```bash
./test-indexer.sh
```

**Individual Commands:**

Start indexer:
```bash
curl -X POST http://localhost:3000/api/indexer/start
```

Check status:
```bash
curl http://localhost:3000/api/indexer/status | jq
```

Force sync:
```bash
curl -X POST http://localhost:3000/api/transactions/refresh
```

Reset indexer:
```bash
curl -X POST http://localhost:3000/api/indexer/reset \
  -H "Content-Type: application/json" \
  -d '{"startBlock": 1, "clearTransactions": true}'
```

### API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/transactions?userAddress=0x...` | GET | Get user's transaction history |
| `/api/transactions?userAddress=` | GET | Get all transactions (empty userAddress) |
| `/api/transactions/refresh` | POST | Trigger immediate sync |
| `/api/indexer/start` | POST | Start indexer service |
| `/api/indexer/status` | GET | Get indexer status and statistics |
| `/api/indexer/reset` | POST | Reset indexer state and optionally clear data |

### Debug Information

The indexer provides detailed logging with emoji indicators:
- 📡 RPC client initialization
- 🔍 Network synchronization
- 📊 Block information
- 🎯 Event fetching
- 📋 Event processing
- ✅ Success operations
- ❌ Error conditions
- ⏰ Scheduled operations

## Build

Build for production:
```bash
bun run build
bun run start
```

## Smart Contracts

The application interacts with TokenExchange contracts deployed on:

- **Bitroot Test Network (1337)**: `******************************************`
- **Local Foundry (31337)**: `******************************************`

### Key Contract Functions

- `exchange(uint256 amount)`: Exchange BRT tokens for BRT(one) tokens
- Exchange rate: 1 BRT = 9523.8 BRT(one)
- Exchange period: 60 days from deployment

## Usage

1. **Connect Wallet**: Click "Connect Wallet" and select your preferred wallet
2. **Enter Amount**: Input the amount of BRT tokens to exchange
3. **Review**: Check exchange rate and estimated gas fees
4. **Approve**: Approve BRT token spending (if needed)
5. **Exchange**: Execute the token exchange
6. **History**: View your transaction history in the history section

## Troubleshooting

### No Transaction History?

1. Check indexer status: `curl http://localhost:3000/api/indexer/status`
2. Start indexer if stopped: `curl -X POST http://localhost:3000/api/indexer/start`
3. Check network configuration in `src/config/contracts.ts`
4. Verify start block is correct for your network
5. Force sync: `curl -X POST http://localhost:3000/api/transactions/refresh`

### Reset and Re-sync

```bash
# Reset to specific block and clear all data
curl -X POST http://localhost:3000/api/indexer/reset \
  -H "Content-Type: application/json" \
  -d '{"startBlock": 1, "clearTransactions": true}'

# Start indexer again
curl -X POST http://localhost:3000/api/indexer/start
```

## Testing with Playwright

Run end-to-end tests:
```bash
bun run test:e2e
```

## Security Features

- **Reentrancy Protection**: Smart contracts use OpenZeppelin's ReentrancyGuard
- **Access Control**: Owner-only functions for contract management
- **Time-based Restrictions**: 60-day exchange period enforcement
- **Balance Validation**: Frontend validates user balances before transactions
- **Automated Indexing**: Reliable transaction history without depending on external services
- **Error Handling**: Comprehensive error handling and logging

## Docker Deployment

Build and run with Docker:

```bash
docker build -t bitroot-exchange .
docker run -d --rm --name token-exchange \
  -p 8100:3000 \
  -v $(pwd)/data:/app/data \
  bitroot-exchange:latest
```

The volume mount ensures transaction data persists across container restarts.