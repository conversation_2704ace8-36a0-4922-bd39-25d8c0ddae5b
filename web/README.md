# Bitroot Token Exchange

A decentralized token exchange application for converting BRT tokens to BRT(one) tokens with a 60-day exchange period.

## Features

- **Token Exchange**: Convert BRT tokens to BRT(one) tokens at a 1:2000 ratio
- **Multi-Wallet Support**: MetaMask, OKX, TokenPocket, WalletConnect, and Coinbase Wallet
- **Countdown Timer**: Real-time countdown showing remaining exchange period
- **Transaction History**: View all exchange transactions on-chain
- **Arbitrum Support**: Supports both Arbitrum mainnet and Goerli testnet
- **Modern UI**: Clean, responsive design with dark theme

## Tech Stack

- **Frontend**: Next.js 15.3.4, React 19, TypeScript
- **Styling**: Tailwind CSS 4
- **Web3**: Wagmi 2.15.6, Viem 2.31.7
- **Package Manager**: Bun
- **Blockchain**: Arbitrum (mainnet & testnet)

## Prerequisites

- [Bun](https://bun.sh/) installed
- A Web3 wallet (MetaMask, OKX, etc.)
- Access to Arbitrum network

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd redeemer/web
```

2. Install dependencies:
```bash
bun install
```

3. Create environment file:
```bash
cp .env.example .env.local
```

4. Update environment variables:
```env
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your-project-id
NEXT_PUBLIC_TOKEN_A_ADDRESS=0x...
NEXT_PUBLIC_TOKEN_B_ADDRESS=0x...
NEXT_PUBLIC_TOKEN_EXCHANGE_ADDRESS=0x...
```

## Development

Start the development server:
```bash
bun run dev
```

The application will be available at `http://localhost:3000`

## Build

Build for production:
```bash
bun run build
bun run start
```

## Smart Contracts

The application interacts with three main contracts:

1. **Token A (BRT)**: The original token to be exchanged
2. **Token B (BRT-one)**: The new token received after exchange
3. **TokenExchange**: Handles the exchange logic with burning and minting

### Key Contract Functions

- `exchange(uint256 amount)`: Exchange BRT tokens for BRT(one) tokens
- `getExchangeRate()`: Get current exchange rate
- `getExchangeEndTime()`: Get exchange period end time
- `isExchangePeriodEnded()`: Check if exchange period has ended

## Usage

1. **Connect Wallet**: Click "Connect Wallet" and select your preferred wallet
2. **Enter Amount**: Input the amount of BRT tokens to exchange
3. **Review**: Check exchange rate and estimated gas fees
4. **Approve**: Approve BRT token spending (if needed)
5. **Exchange**: Execute the token exchange
6. **History**: View your transaction history in the history section

## Network Configuration

The app supports:
- **Arbitrum One** (Chain ID: 42161)
- **Arbitrum Goerli** (Chain ID: 421613)
- **Local Development** (Foundry)

## Security Features

- **Reentrancy Protection**: Smart contracts use OpenZeppelin's ReentrancyGuard
- **Access Control**: Owner-only functions for contract management
- **Time-based Restrictions**: 60-day exchange period enforcement
- **Balance Validation**: Frontend validates user balances before transactions
