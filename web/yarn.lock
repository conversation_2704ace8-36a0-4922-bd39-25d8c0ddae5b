# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@adraffy/ens-normalize@npm:^1.10.1, @adraffy/ens-normalize@npm:^1.11.0":
  version: 1.11.0
  resolution: "@adraffy/ens-normalize@npm:1.11.0"
  checksum: b2911269e3e0ec6396a2e5433a99e0e1f9726befc6c167994448cd0e53dbdd0be22b4835b4f619558b568ed9aa7312426b8fa6557a13999463489daa88169ee5
  languageName: node
  linkType: hard

"@alloc/quick-lru@npm:^5.2.0":
  version: 5.2.0
  resolution: "@alloc/quick-lru@npm:5.2.0"
  checksum: bdc35758b552bcf045733ac047fb7f9a07c4678b944c641adfbd41f798b4b91fffd0fdc0df2578d9b0afc7b4d636aa6e110ead5d6281a2adc1ab90efd7f057f8
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.3.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: d3ad7b89d973df059c4e8e6d7c972cbeb1bb2f18f002a3bd04ae0707da214cb06cc06929b65aa2313b9347463df2914772298bae8b1d7973f246bb3f2ab3e8f0
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.21.0, @babel/runtime@npm:^7.26.0":
  version: 7.27.6
  resolution: "@babel/runtime@npm:7.27.6"
  checksum: 3f7b879df1823c0926bd5dbc941c62f5d60faa790c1aab9758c04799e1f04ee8d93553be9ec059d4e5882f19fe03cbe8933ee4f46212dced0f6d8205992c9c9a
  languageName: node
  linkType: hard

"@coinbase/wallet-sdk@npm:4.3.3":
  version: 4.3.3
  resolution: "@coinbase/wallet-sdk@npm:4.3.3"
  dependencies:
    "@noble/hashes": ^1.4.0
    clsx: ^1.2.1
    eventemitter3: ^5.0.1
    preact: ^10.24.2
  checksum: 2c0bf1deaea48927b3e1659f10ec74a5e560ed5a4adff6c79c457c31d79bf62d022cd1a28d1c3d4c2cc78fc8b7d06614b7dfca0c099d65c6702b254bba1ebae6
  languageName: node
  linkType: hard

"@ecies/ciphers@npm:^0.2.3":
  version: 0.2.3
  resolution: "@ecies/ciphers@npm:0.2.3"
  peerDependencies:
    "@noble/ciphers": ^1.0.0
  checksum: fdf01fd5e687a74802df60d21ff9be5aa6cdf692dcf671186e94eb17e9c260a4652b64c7127b277af20c35d3ccc733003cefb2fdaee93e8b516fa71d317d503f
  languageName: node
  linkType: hard

"@emnapi/core@npm:^1.4.3":
  version: 1.4.3
  resolution: "@emnapi/core@npm:1.4.3"
  dependencies:
    "@emnapi/wasi-threads": 1.0.2
    tslib: ^2.4.0
  checksum: 1c757d380b3cecec637a2eccfb31b770b995060f695d1e15b29a86e2038909a24152947ef6e4b6586759e6716148ff17f40e51367d1b79c9a3e1b6812537bdf4
  languageName: node
  linkType: hard

"@emnapi/runtime@npm:^1.4.3":
  version: 1.4.3
  resolution: "@emnapi/runtime@npm:1.4.3"
  dependencies:
    tslib: ^2.4.0
  checksum: ff2074809638ed878e476ece370c6eae7e6257bf029a581bb7a290488d8f2a08c420a65988c7f03bfc6bb689218f0cd995d2f935bd182150b357fc2341142f4f
  languageName: node
  linkType: hard

"@emnapi/wasi-threads@npm:1.0.2, @emnapi/wasi-threads@npm:^1.0.2":
  version: 1.0.2
  resolution: "@emnapi/wasi-threads@npm:1.0.2"
  dependencies:
    tslib: ^2.4.0
  checksum: c289cd3d0e26f11de23429a4abc7f99927917c0871d5a22637cbb75170f2b58d3a42e80d76dea89d054e529f79e35cdc953324819a7f990305d0db2897fa5fab
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.7.0":
  version: 4.7.0
  resolution: "@eslint-community/eslint-utils@npm:4.7.0"
  dependencies:
    eslint-visitor-keys: ^3.4.3
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: b177e3b75c0b8d0e5d71f1c532edb7e40b31313db61f0c879f9bf19c3abb2783c6c372b5deb2396dab4432f2946b9972122ac682e77010376c029dfd0149c681
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.12.1":
  version: 4.12.1
  resolution: "@eslint-community/regexpp@npm:4.12.1"
  checksum: 0d628680e204bc316d545b4993d3658427ca404ae646ce541fcc65306b8c712c340e5e573e30fb9f85f4855c0c5f6dca9868931f2fcced06417fbe1a0c6cd2d6
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.21.0":
  version: 0.21.0
  resolution: "@eslint/config-array@npm:0.21.0"
  dependencies:
    "@eslint/object-schema": ^2.1.6
    debug: ^4.3.1
    minimatch: ^3.1.2
  checksum: 84d3ae7cb755af94dc158a74389f4c560757b13f2bb908f598f927b87b70a38e8152015ea2e9557c1b4afc5130ee1356f6cad682050d67aae0468bbef98bc3a8
  languageName: node
  linkType: hard

"@eslint/config-helpers@npm:^0.3.0":
  version: 0.3.0
  resolution: "@eslint/config-helpers@npm:0.3.0"
  checksum: d4fe8242ef580806ddaa88309f4bb2d3e6be5524cc6d6197675106c6d048f766a3f9cdc2e8e33bbc97a123065792cac8314fc85ac2b3cf72610e8df59301d63a
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.14.0":
  version: 0.14.0
  resolution: "@eslint/core@npm:0.14.0"
  dependencies:
    "@types/json-schema": ^7.0.15
  checksum: d68b8282b6f38c5145234f812f18f491d12d716240875591bd54bf5ac32858d979bdf6d38e521997a6e01f2c4223a3e66049714151da7278d0a95ff15b5d46c8
  languageName: node
  linkType: hard

"@eslint/core@npm:^0.15.1":
  version: 0.15.1
  resolution: "@eslint/core@npm:0.15.1"
  dependencies:
    "@types/json-schema": ^7.0.15
  checksum: 9215f00466d60764453466604443a491b0ea8263c148836fef723354d6ef1d550991e931d3df2780c99cee2cab14c4f41f97d5341ab12a8443236c961bb6f664
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3, @eslint/eslintrc@npm:^3.3.1":
  version: 3.3.1
  resolution: "@eslint/eslintrc@npm:3.3.1"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^10.0.1
    globals: ^14.0.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: 8241f998f0857abf5a615072273b90b1244d75c1c45d217c6a8eb444c6e12bbb5506b4879c14fb262eb72b7d8e3d2f0542da2db1a7f414a12496ebb790fb4d62
  languageName: node
  linkType: hard

"@eslint/js@npm:9.30.0":
  version: 9.30.0
  resolution: "@eslint/js@npm:9.30.0"
  checksum: 17bffce324fa2670ece4d9d4bd94481b38d00e1bcae5f3f8448e07af04e707a8a003b6c66328bd140f1d6cc70549e092ec1d63569c592b7898424023ba5d2e38
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.6":
  version: 2.1.6
  resolution: "@eslint/object-schema@npm:2.1.6"
  checksum: e32e565319f6544d36d3fa69a3e163120722d12d666d1a4525c9a6f02e9b54c29d9b1f03139e25d7e759e08dda8da433590bc23c09db8d511162157ef1b86a4c
  languageName: node
  linkType: hard

"@eslint/plugin-kit@npm:^0.3.1":
  version: 0.3.3
  resolution: "@eslint/plugin-kit@npm:0.3.3"
  dependencies:
    "@eslint/core": ^0.15.1
    levn: ^0.4.1
  checksum: c9dc7b83ed011dce35ccc66dc53aaaa87e9fb2bd7c8a11231f7624334d82c9a53552e4b1a1cb60b74073fcc49a2661be874e503aae14cf2f6ac6b1c7faeb7080
  languageName: node
  linkType: hard

"@ethereumjs/common@npm:^3.2.0":
  version: 3.2.0
  resolution: "@ethereumjs/common@npm:3.2.0"
  dependencies:
    "@ethereumjs/util": ^8.1.0
    crc-32: ^1.2.0
  checksum: cb9cc11f5c868cb577ba611cebf55046e509218bbb89b47ccce010776dafe8256d70f8f43fab238aec74cf71f62601cd5842bc03a83261200802de365732a14b
  languageName: node
  linkType: hard

"@ethereumjs/rlp@npm:^4.0.1":
  version: 4.0.1
  resolution: "@ethereumjs/rlp@npm:4.0.1"
  bin:
    rlp: bin/rlp
  checksum: 30db19c78faa2b6ff27275ab767646929207bb207f903f09eb3e4c273ce2738b45f3c82169ddacd67468b4f063d8d96035f2bf36f02b6b7e4d928eefe2e3ecbc
  languageName: node
  linkType: hard

"@ethereumjs/tx@npm:^4.1.2, @ethereumjs/tx@npm:^4.2.0":
  version: 4.2.0
  resolution: "@ethereumjs/tx@npm:4.2.0"
  dependencies:
    "@ethereumjs/common": ^3.2.0
    "@ethereumjs/rlp": ^4.0.1
    "@ethereumjs/util": ^8.1.0
    ethereum-cryptography: ^2.0.0
  checksum: 87a3f5f2452cfbf6712f8847525a80c213210ed453c211c793c5df801fe35ecef28bae17fadd222fcbdd94277478a47e52d2b916a90a6b30cda21f1e0cdaee42
  languageName: node
  linkType: hard

"@ethereumjs/util@npm:^8.1.0":
  version: 8.1.0
  resolution: "@ethereumjs/util@npm:8.1.0"
  dependencies:
    "@ethereumjs/rlp": ^4.0.1
    ethereum-cryptography: ^2.0.0
    micro-ftch: ^0.3.1
  checksum: 9ae5dee8f12b0faf81cd83f06a41560e79b0ba96a48262771d897a510ecae605eb6d84f687da001ab8ccffd50f612ae50f988ef76e6312c752897f462f3ac08d
  languageName: node
  linkType: hard

"@humanfs/core@npm:^0.19.1":
  version: 0.19.1
  resolution: "@humanfs/core@npm:0.19.1"
  checksum: 611e0545146f55ddfdd5c20239cfb7911f9d0e28258787c4fc1a1f6214250830c9367aaaeace0096ed90b6739bee1e9c52ad5ba8adaf74ab8b449119303babfe
  languageName: node
  linkType: hard

"@humanfs/node@npm:^0.16.6":
  version: 0.16.6
  resolution: "@humanfs/node@npm:0.16.6"
  dependencies:
    "@humanfs/core": ^0.19.1
    "@humanwhocodes/retry": ^0.3.0
  checksum: f9cb52bb235f8b9c6fcff43a7e500669a38f8d6ce26593404a9b56365a1644e0ed60c720dc65ff6a696b1f85f3563ab055bb554ec8674f2559085ba840e47710
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.1
  resolution: "@humanwhocodes/retry@npm:0.3.1"
  checksum: 7e5517bb51dbea3e02ab6cacef59a8f4b0ca023fc4b0b8cbc40de0ad29f46edd50b897c6e7fba79366a0217e3f48e2da8975056f6c35cfe19d9cc48f1d03c1dd
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.4.2":
  version: 0.4.3
  resolution: "@humanwhocodes/retry@npm:0.4.3"
  checksum: d423455b9d53cf01f778603404512a4246fb19b83e74fe3e28c70d9a80e9d4ae147d2411628907ca983e91a855a52535859a8bb218050bc3f6dbd7a553b7b442
  languageName: node
  linkType: hard

"@img/sharp-darwin-arm64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-darwin-arm64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-darwin-arm64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-darwin-arm64":
      optional: true
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-darwin-x64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-darwin-x64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-darwin-x64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-darwin-x64":
      optional: true
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-arm64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-darwin-arm64@npm:1.1.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-libvips-darwin-x64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-darwin-x64@npm:1.1.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-arm64@npm:1.1.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-arm@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-arm@npm:1.1.0"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-ppc64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-ppc64@npm:1.1.0"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-s390x@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-s390x@npm:1.1.0"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linux-x64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linux-x64@npm:1.1.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-arm64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linuxmusl-arm64@npm:1.1.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-libvips-linuxmusl-x64@npm:1.1.0":
  version: 1.1.0
  resolution: "@img/sharp-libvips-linuxmusl-x64@npm:1.1.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linux-arm64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linux-arm64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linux-arm64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-arm@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linux-arm@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linux-arm": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linux-arm":
      optional: true
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-s390x@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linux-s390x@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linux-s390x": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linux-s390x":
      optional: true
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linux-x64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linux-x64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linux-x64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linux-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-arm64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linuxmusl-arm64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linuxmusl-arm64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-linuxmusl-x64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-linuxmusl-x64@npm:0.34.2"
  dependencies:
    "@img/sharp-libvips-linuxmusl-x64": 1.1.0
  dependenciesMeta:
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@img/sharp-wasm32@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-wasm32@npm:0.34.2"
  dependencies:
    "@emnapi/runtime": ^1.4.3
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@img/sharp-win32-arm64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-win32-arm64@npm:0.34.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@img/sharp-win32-ia32@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-win32-ia32@npm:0.34.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@img/sharp-win32-x64@npm:0.34.2":
  version: 0.34.2
  resolution: "@img/sharp-win32-x64@npm:0.34.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: ^7.0.4
  checksum: 5d36d289960e886484362d9eb6a51d1ea28baed5f5d0140bbe62b99bac52eaf06cc01c2bc0d3575977962f84f6b2c4387b043ee632216643d4787b0999465bf2
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.10
  resolution: "@jridgewell/gen-mapping@npm:0.3.10"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.5.0
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: 0cddd9adda499f1e10ce6597b4ba337b6742350fa3caf643d2d572a9d4fbf87dbf4ec0cdfc22ada11991b2ac61599a3af35ee7e9ef5d21e5a841ab71038238af
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.2
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.2"
  checksum: cc346f1058b29da2ec84c7ae5aeecb8c58601c03a67bf62397ffaf13b6fcac7dc56fade0b873216400fa204a3e8b23f5f2facdea87d53250bbefd140ad08b67a
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.24":
  version: 0.3.27
  resolution: "@jridgewell/trace-mapping@npm:0.3.27"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 7fcdee16c4da1f869ecd0a4465f28cc1eb51dbd88cbedde8ca114357f0faa03b31c7e997f5889cd061a3bab4262f616c5a915ab83d047022a87a2daf715a2069
  languageName: node
  linkType: hard

"@lit-labs/ssr-dom-shim@npm:^1.2.0":
  version: 1.3.0
  resolution: "@lit-labs/ssr-dom-shim@npm:1.3.0"
  checksum: c2003e8bb6d39c15b359450da0d5ea8970f7e684127c10abb26066afd8818785a6e43374fa52d25ac93e9714db670ca8a9b2befc9f3426d6e52eef4a592a79d4
  languageName: node
  linkType: hard

"@lit/reactive-element@npm:^2.1.0":
  version: 2.1.0
  resolution: "@lit/reactive-element@npm:2.1.0"
  dependencies:
    "@lit-labs/ssr-dom-shim": ^1.2.0
  checksum: fb36e9f64bf582358fa1efa5a80aa41b73d00974bf7e35d2f1ed5a4237b33ffa0410cfde702f785d19c29ed3026c3a900fc984016c6b1f36007197f07ca97d26
  languageName: node
  linkType: hard

"@metamask/eth-json-rpc-provider@npm:^1.0.0":
  version: 1.0.1
  resolution: "@metamask/eth-json-rpc-provider@npm:1.0.1"
  dependencies:
    "@metamask/json-rpc-engine": ^7.0.0
    "@metamask/safe-event-emitter": ^3.0.0
    "@metamask/utils": ^5.0.1
  checksum: ff97648b002d2889bd020c03abc26137cf068df3280e46144b5333c1b294f35f5099361343825f900ef20b9dcb6819495830b7a99eb1cbfbd671e5b11c0dfde1
  languageName: node
  linkType: hard

"@metamask/json-rpc-engine@npm:^7.0.0":
  version: 7.3.3
  resolution: "@metamask/json-rpc-engine@npm:7.3.3"
  dependencies:
    "@metamask/rpc-errors": ^6.2.1
    "@metamask/safe-event-emitter": ^3.0.0
    "@metamask/utils": ^8.3.0
  checksum: 7bab8b4d2341a6243ba451bc58283f0a6905b09f7257857859848a51a795444ca6899b1a6908b15f8ed236fb574ab85a630c9cb28d127ab52c4630e496c16006
  languageName: node
  linkType: hard

"@metamask/json-rpc-engine@npm:^8.0.1, @metamask/json-rpc-engine@npm:^8.0.2":
  version: 8.0.2
  resolution: "@metamask/json-rpc-engine@npm:8.0.2"
  dependencies:
    "@metamask/rpc-errors": ^6.2.1
    "@metamask/safe-event-emitter": ^3.0.0
    "@metamask/utils": ^8.3.0
  checksum: c240d298ad503d93922a94a62cf59f0344b6d6644a523bc8ea3c0f321bea7172b89f2747a5618e2861b2e8152ae5086b76f391a10e4566529faa50b8850c051d
  languageName: node
  linkType: hard

"@metamask/json-rpc-middleware-stream@npm:^7.0.1":
  version: 7.0.2
  resolution: "@metamask/json-rpc-middleware-stream@npm:7.0.2"
  dependencies:
    "@metamask/json-rpc-engine": ^8.0.2
    "@metamask/safe-event-emitter": ^3.0.0
    "@metamask/utils": ^8.3.0
    readable-stream: ^3.6.2
  checksum: ff11ad3ff0ec27530efc53c4e6543661648f437dacdd58797449307e20dbc428b479cd8d1e9767797268b98d0445bd6f1986820a8c855faeef01d5c03b55323b
  languageName: node
  linkType: hard

"@metamask/object-multiplex@npm:^2.0.0":
  version: 2.1.0
  resolution: "@metamask/object-multiplex@npm:2.1.0"
  dependencies:
    once: ^1.4.0
    readable-stream: ^3.6.2
  checksum: e119f695e89eb20c3174f8ac6d74587498d85cff92c37e83e167cb758b3d3147d5b5e1a997d6198d430ebcf2cede6265bf5d4513fe96dbb2d82bbc6167752caa
  languageName: node
  linkType: hard

"@metamask/onboarding@npm:^1.0.1":
  version: 1.0.1
  resolution: "@metamask/onboarding@npm:1.0.1"
  dependencies:
    bowser: ^2.9.0
  checksum: c5a6b13760d8c761733fd5edcd3984b2951fb22b34ecebc27104224de7d2582065b8b7edc5b1dafafb76e73a55144d251bc08d540620dde7f1ebfb5f3520b050
  languageName: node
  linkType: hard

"@metamask/providers@npm:16.1.0":
  version: 16.1.0
  resolution: "@metamask/providers@npm:16.1.0"
  dependencies:
    "@metamask/json-rpc-engine": ^8.0.1
    "@metamask/json-rpc-middleware-stream": ^7.0.1
    "@metamask/object-multiplex": ^2.0.0
    "@metamask/rpc-errors": ^6.2.1
    "@metamask/safe-event-emitter": ^3.1.1
    "@metamask/utils": ^8.3.0
    detect-browser: ^5.2.0
    extension-port-stream: ^3.0.0
    fast-deep-equal: ^3.1.3
    is-stream: ^2.0.0
    readable-stream: ^3.6.2
    webextension-polyfill: ^0.10.0
  checksum: 85e40140f342a38112c3d7cee436751a2be4c575cc4f815ab48a73b549abc2d756bf4a10e4b983e91dbd38076601f992531edb6d8d674aebceae32ef7e299275
  languageName: node
  linkType: hard

"@metamask/rpc-errors@npm:^6.2.1":
  version: 6.4.0
  resolution: "@metamask/rpc-errors@npm:6.4.0"
  dependencies:
    "@metamask/utils": ^9.0.0
    fast-safe-stringify: ^2.0.6
  checksum: d0c77097f4d6ff0bafc4e4c915285c4320bdd119ef79f1833ec208deaeeb755500efefbb422f39210801b1061963449431d2e19715a5eb3d06ce0b5c150a75a1
  languageName: node
  linkType: hard

"@metamask/safe-event-emitter@npm:^2.0.0":
  version: 2.0.0
  resolution: "@metamask/safe-event-emitter@npm:2.0.0"
  checksum: 8b717ac5d53df0027c05509f03d0534700b5898dd1c3a53fb2dc4c0499ca5971b14aae67f522d09eb9f509e77f50afa95fdb3eda1afbff8b071c18a3d2905e93
  languageName: node
  linkType: hard

"@metamask/safe-event-emitter@npm:^3.0.0, @metamask/safe-event-emitter@npm:^3.1.1":
  version: 3.1.2
  resolution: "@metamask/safe-event-emitter@npm:3.1.2"
  checksum: 8ef7579f9317eb5c94ecf3e6abb8d13b119af274b678805eac76abe4c0667bfdf539f385e552bb973e96333b71b77aa7c787cb3fce9cd5fb4b00f1dbbabf880d
  languageName: node
  linkType: hard

"@metamask/sdk-communication-layer@npm:0.32.0":
  version: 0.32.0
  resolution: "@metamask/sdk-communication-layer@npm:0.32.0"
  dependencies:
    bufferutil: ^4.0.8
    date-fns: ^2.29.3
    debug: ^4.3.4
    utf-8-validate: ^5.0.2
    uuid: ^8.3.2
  peerDependencies:
    cross-fetch: ^4.0.0
    eciesjs: "*"
    eventemitter2: ^6.4.9
    readable-stream: ^3.6.2
    socket.io-client: ^4.5.1
  checksum: 4981d97d1d1b7aa48176fbee8ca0dae8ab4df27e9434c380c28c5e2022ff3b775e283a08cc3512c27e76e4e54d906797cbe3b70670a0c9763632bc471776ced1
  languageName: node
  linkType: hard

"@metamask/sdk-install-modal-web@npm:0.32.0":
  version: 0.32.0
  resolution: "@metamask/sdk-install-modal-web@npm:0.32.0"
  dependencies:
    "@paulmillr/qr": ^0.2.1
  checksum: 78f8d6db286853524466ed667962a611415f7677938f5a0dab4cf66ca2a801f46b5c6764731c7f492878ff6293fa0ddd8cb3a0e0a582793a2504354273f8c34d
  languageName: node
  linkType: hard

"@metamask/sdk@npm:0.32.0":
  version: 0.32.0
  resolution: "@metamask/sdk@npm:0.32.0"
  dependencies:
    "@babel/runtime": ^7.26.0
    "@metamask/onboarding": ^1.0.1
    "@metamask/providers": 16.1.0
    "@metamask/sdk-communication-layer": 0.32.0
    "@metamask/sdk-install-modal-web": 0.32.0
    "@paulmillr/qr": ^0.2.1
    bowser: ^2.9.0
    cross-fetch: ^4.0.0
    debug: ^4.3.4
    eciesjs: ^0.4.11
    eth-rpc-errors: ^4.0.3
    eventemitter2: ^6.4.9
    obj-multiplex: ^1.0.0
    pump: ^3.0.0
    readable-stream: ^3.6.2
    socket.io-client: ^4.5.1
    tslib: ^2.6.0
    util: ^0.12.4
    uuid: ^8.3.2
  checksum: 2b05e1316667471f47d3f03deae5fa6c493d6511406e034edc2aab003d5d9f12b1344f5ec37027bcda26ac1dd752b9d9923c925d17d8df3305f4f89574c5ed44
  languageName: node
  linkType: hard

"@metamask/superstruct@npm:^3.0.0, @metamask/superstruct@npm:^3.1.0":
  version: 3.2.1
  resolution: "@metamask/superstruct@npm:3.2.1"
  checksum: 194e4afc4df89f347e4dd16db8f8dfcbf7990ff82169c3bd43b98ecff2f1ef09488b987af612cc1ea2689826e8460bb2b01e1a3a340383420115b3a90aa68465
  languageName: node
  linkType: hard

"@metamask/utils@npm:^5.0.1":
  version: 5.0.2
  resolution: "@metamask/utils@npm:5.0.2"
  dependencies:
    "@ethereumjs/tx": ^4.1.2
    "@types/debug": ^4.1.7
    debug: ^4.3.4
    semver: ^7.3.8
    superstruct: ^1.0.3
  checksum: eca82e42911b2840deb4f32f0f215c5ffd14d22d68afbbe92d3180e920e509e310777b15eab29def3448f3535b66596ceb4c23666ec846adacc8e1bb093ff882
  languageName: node
  linkType: hard

"@metamask/utils@npm:^8.3.0":
  version: 8.5.0
  resolution: "@metamask/utils@npm:8.5.0"
  dependencies:
    "@ethereumjs/tx": ^4.2.0
    "@metamask/superstruct": ^3.0.0
    "@noble/hashes": ^1.3.1
    "@scure/base": ^1.1.3
    "@types/debug": ^4.1.7
    debug: ^4.3.4
    pony-cause: ^2.1.10
    semver: ^7.5.4
    uuid: ^9.0.1
  checksum: e8eac1c796c3f6b623be3c2736e8682248620f666b180f5c12ce56ee09587d4e28b6811862139a05c7a1bec91415f10ccf0516f3cdf342f88b0189d2a057c24b
  languageName: node
  linkType: hard

"@metamask/utils@npm:^9.0.0":
  version: 9.3.0
  resolution: "@metamask/utils@npm:9.3.0"
  dependencies:
    "@ethereumjs/tx": ^4.2.0
    "@metamask/superstruct": ^3.1.0
    "@noble/hashes": ^1.3.1
    "@scure/base": ^1.1.3
    "@types/debug": ^4.1.7
    debug: ^4.3.4
    pony-cause: ^2.1.10
    semver: ^7.5.4
    uuid: ^9.0.1
  checksum: f720b0f7bdd46054aa88d15a9702e1de6d7200a1ca1d4f6bc48761b039f1bbffb46ac88bc87fe79e66128c196d424f3b9ef071b3cb4b40139223786d56da35e0
  languageName: node
  linkType: hard

"@napi-rs/wasm-runtime@npm:^0.2.11":
  version: 0.2.11
  resolution: "@napi-rs/wasm-runtime@npm:0.2.11"
  dependencies:
    "@emnapi/core": ^1.4.3
    "@emnapi/runtime": ^1.4.3
    "@tybys/wasm-util": ^0.9.0
  checksum: 7c614625784ab467cc7b36b4d7384854891469d0ddce8ca831d28b2abdf8cb3f014d8e8a181c98000719effb46950ab9134b245ab9a8044ad7a7da725b40f858
  languageName: node
  linkType: hard

"@next/env@npm:15.3.4":
  version: 15.3.4
  resolution: "@next/env@npm:15.3.4"
  checksum: 7579ecd55d3da8bf8ccfc9e781b3da5a9ae35e20c3b86a9ff769e8590125622ff6abb9c4d6c057df1466e1223056a0eddc8edb834f6a25f355734cbcf56a5be2
  languageName: node
  linkType: hard

"@next/eslint-plugin-next@npm:15.3.4":
  version: 15.3.4
  resolution: "@next/eslint-plugin-next@npm:15.3.4"
  dependencies:
    fast-glob: 3.3.1
  checksum: b2d8b62aa182b2cb759dbde3d4f08b2db7504e3d91a1fd4118f9ccbae74966037e369122051b2cbfb6d92a58e0da9852485e02500d9af97adff9672627b31b76
  languageName: node
  linkType: hard

"@next/swc-darwin-arm64@npm:15.3.4":
  version: 15.3.4
  resolution: "@next/swc-darwin-arm64@npm:15.3.4"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-darwin-x64@npm:15.3.4":
  version: 15.3.4
  resolution: "@next/swc-darwin-x64@npm:15.3.4"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-gnu@npm:15.3.4":
  version: 15.3.4
  resolution: "@next/swc-linux-arm64-gnu@npm:15.3.4"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-arm64-musl@npm:15.3.4":
  version: 15.3.4
  resolution: "@next/swc-linux-arm64-musl@npm:15.3.4"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-linux-x64-gnu@npm:15.3.4":
  version: 15.3.4
  resolution: "@next/swc-linux-x64-gnu@npm:15.3.4"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@next/swc-linux-x64-musl@npm:15.3.4":
  version: 15.3.4
  resolution: "@next/swc-linux-x64-musl@npm:15.3.4"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@next/swc-win32-arm64-msvc@npm:15.3.4":
  version: 15.3.4
  resolution: "@next/swc-win32-arm64-msvc@npm:15.3.4"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@next/swc-win32-x64-msvc@npm:15.3.4":
  version: 15.3.4
  resolution: "@next/swc-win32-x64-msvc@npm:15.3.4"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@noble/ciphers@npm:1.2.1":
  version: 1.2.1
  resolution: "@noble/ciphers@npm:1.2.1"
  checksum: 843bd81a2b17cac7045c4ecc511c1e88f42e51f5df2635efdbd30fd318afe78d88c732772773a8412d5057560d23746a6aea6dd255af1a49fb17928ef23f6c22
  languageName: node
  linkType: hard

"@noble/ciphers@npm:^1.3.0":
  version: 1.3.0
  resolution: "@noble/ciphers@npm:1.3.0"
  checksum: 19722c35475df9bc78db60d261d0b5ef8a6d722561efc2135453f943eaa421b492195dc666e3e4df2b755bca3739e04f04b9c660198559f5dd05d3cfbf1b9e92
  languageName: node
  linkType: hard

"@noble/curves@npm:1.4.2, @noble/curves@npm:~1.4.0":
  version: 1.4.2
  resolution: "@noble/curves@npm:1.4.2"
  dependencies:
    "@noble/hashes": 1.4.0
  checksum: c475a83c4263e2c970eaba728895b9b5d67e0ca880651e9c6e3efdc5f6a4f07ceb5b043bf71c399fc80fada0b8706e69d0772bffdd7b9de2483b988973a34cba
  languageName: node
  linkType: hard

"@noble/curves@npm:1.8.0":
  version: 1.8.0
  resolution: "@noble/curves@npm:1.8.0"
  dependencies:
    "@noble/hashes": 1.7.0
  checksum: 88198bc5b8049358dfcc6c5e121125744fb81c703299127800f38f868a41697bc26bef8f88dc38f1939f4e0133b8db5f24337164eca7421a6a9480ee711f5e1b
  languageName: node
  linkType: hard

"@noble/curves@npm:1.8.1":
  version: 1.8.1
  resolution: "@noble/curves@npm:1.8.1"
  dependencies:
    "@noble/hashes": 1.7.1
  checksum: 4143f1248ed57c1ae46dfef5c692a91383e5830420b9c72d3ff1061aa9ebbf8999297da6d2aed8a9716fef8e6b1f5a45737feeab02abf55ca2a4f514bf9339ec
  languageName: node
  linkType: hard

"@noble/curves@npm:1.9.2, @noble/curves@npm:^1.6.0, @noble/curves@npm:^1.9.1, @noble/curves@npm:~1.9.0":
  version: 1.9.2
  resolution: "@noble/curves@npm:1.9.2"
  dependencies:
    "@noble/hashes": 1.8.0
  checksum: bac582aefe951032cb04ed7627f139c3351ddfefd2625a25fe7f7a8043e7d781be4fad320d4ae75e31fa5d7e05ba643f16139877375130fd3cff86d81512e0f2
  languageName: node
  linkType: hard

"@noble/curves@npm:~1.8.1":
  version: 1.8.2
  resolution: "@noble/curves@npm:1.8.2"
  dependencies:
    "@noble/hashes": 1.7.2
  checksum: f26fd77b4d78fe26dba2754cbcaddee5da23a711a0c9778ee57764eb0084282d97659d9b0a760718f42493adf68665dbffdca9d6213950f03f079d09c465c096
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.4.0, @noble/hashes@npm:~1.4.0":
  version: 1.4.0
  resolution: "@noble/hashes@npm:1.4.0"
  checksum: 8ba816ae26c90764b8c42493eea383716396096c5f7ba6bea559993194f49d80a73c081f315f4c367e51bd2d5891700bcdfa816b421d24ab45b41cb03e4f3342
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.7.0":
  version: 1.7.0
  resolution: "@noble/hashes@npm:1.7.0"
  checksum: c06949ead7f5771a74f6fc9a346c7519212b3484c5b7916c8cad6b1b0e5f5f6c997ac3a43c0884ef8b99cfc55fac89058eefb29b6aad1cb41f436c748b316a1c
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.7.1":
  version: 1.7.1
  resolution: "@noble/hashes@npm:1.7.1"
  checksum: 4f1b56428a10323feef17e4f437c9093556cb18db06f94d254043fadb69c3da8475f96eb3f8322d41e8670117d7486475a8875e68265c2839f60fd03edd6a616
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.7.2, @noble/hashes@npm:~1.7.1":
  version: 1.7.2
  resolution: "@noble/hashes@npm:1.7.2"
  checksum: f9e3c2e62c2850073f8d6ac30cc33b03a25cae859eb2209b33ae90ed3d1e003cb2a1ddacd2aacd6b7c98a5ad70795a234ccce04b0526657cd8020ce4ffdb491f
  languageName: node
  linkType: hard

"@noble/hashes@npm:1.8.0, @noble/hashes@npm:^1.3.1, @noble/hashes@npm:^1.4.0, @noble/hashes@npm:^1.5.0, @noble/hashes@npm:^1.8.0, @noble/hashes@npm:~1.8.0":
  version: 1.8.0
  resolution: "@noble/hashes@npm:1.8.0"
  checksum: c94e98b941963676feaba62475b1ccfa8341e3f572adbb3b684ee38b658df44100187fa0ef4220da580b13f8d27e87d5492623c8a02ecc61f23fb9960c7918f5
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@nolyfill/is-core-module@npm:1.0.39":
  version: 1.0.39
  resolution: "@nolyfill/is-core-module@npm:1.0.39"
  checksum: 0d6e098b871eca71d875651288e1f0fa770a63478b0b50479c99dc760c64175a56b5b04f58d5581bbcc6b552b8191ab415eada093d8df9597ab3423c8cac1815
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: e8fc25d536250ed3e669813b36e8c6d805628b472353c57afd8c4fde0fcfcf3dda4ffe22f7af8c9070812ec2e7a03fb41d7151547cef3508efe661a5a3add20f
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: ^7.3.5
  checksum: 68951c589e9a4328698a35fd82fe71909a257d6f2ede0434d236fa55634f0fbcad9bb8755553ce5849bd25ee6f019f4d435921ac715c853582c4a7f5983c8d4a
  languageName: node
  linkType: hard

"@paulmillr/qr@npm:^0.2.1":
  version: 0.2.1
  resolution: "@paulmillr/qr@npm:0.2.1"
  checksum: 8a7b882f74f472759b0e5911c9c902a29c5232609373af4c5775625d9aad4ebda635d84c25be27e694144ba73d8e4204e72c3b9b59e9a375ec1d19f034a2d2ad
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@reown/appkit-common@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-common@npm:1.7.8"
  dependencies:
    big.js: 6.2.2
    dayjs: 1.11.13
    viem: ">=2.29.0"
  checksum: 6d643d1e93b0709b90dddc66a3e0391d577e3cd84cd0e7d6da895e982058e5e0ea96f518f8d103d74d037589082523fd339057689a99cd5cd98cbb37b61a29ea
  languageName: node
  linkType: hard

"@reown/appkit-controllers@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-controllers@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": 1.7.8
    "@reown/appkit-wallet": 1.7.8
    "@walletconnect/universal-provider": 2.21.0
    valtio: 1.13.2
    viem: ">=2.29.0"
  checksum: 1da42acf43e16cd01bda6a431b9ef5a7aaf3b702d178766bba7eb1aa8e9d74258262ff94be3095fd04c25044ed072c323bcb7606a9038401239a0c31c1894212
  languageName: node
  linkType: hard

"@reown/appkit-pay@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-pay@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": 1.7.8
    "@reown/appkit-controllers": 1.7.8
    "@reown/appkit-ui": 1.7.8
    "@reown/appkit-utils": 1.7.8
    lit: 3.3.0
    valtio: 1.13.2
  checksum: 5785088196b08b0067e48734804ed2256a31622d47fac240896f070bc096c080c76be9a4bb45fe05fc9ad1105ef4537b9cd1525aa6ce44dc9eab6dc697bab6cd
  languageName: node
  linkType: hard

"@reown/appkit-polyfills@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-polyfills@npm:1.7.8"
  dependencies:
    buffer: 6.0.3
  checksum: f47887c27d2a58e39c9344710a805c41fd4db7032a40bbfb628f5da2724576201c79c68e5030126c410cee5bb3c480d8670cceb4610dd39c5955e54ca4f453d3
  languageName: node
  linkType: hard

"@reown/appkit-scaffold-ui@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-scaffold-ui@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": 1.7.8
    "@reown/appkit-controllers": 1.7.8
    "@reown/appkit-ui": 1.7.8
    "@reown/appkit-utils": 1.7.8
    "@reown/appkit-wallet": 1.7.8
    lit: 3.3.0
  checksum: b835d4a8762d631cdeb41b4fdfc7aaff9b386d5808ca4f8f78c9733a0a9cbc7c1f05f527db42172330d439b7d22fa880b12e02a80cbe320ccfbe5cb91eab1ba7
  languageName: node
  linkType: hard

"@reown/appkit-ui@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-ui@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": 1.7.8
    "@reown/appkit-controllers": 1.7.8
    "@reown/appkit-wallet": 1.7.8
    lit: 3.3.0
    qrcode: 1.5.3
  checksum: 21464449cff886f952e68accb13c9b73e94bcf04974612f3afc48cd4f3c9e63ecef5d240d6ba1fd26e4ab29ad6bb2304c7b326528674acd8490b346b9449a39e
  languageName: node
  linkType: hard

"@reown/appkit-utils@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-utils@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": 1.7.8
    "@reown/appkit-controllers": 1.7.8
    "@reown/appkit-polyfills": 1.7.8
    "@reown/appkit-wallet": 1.7.8
    "@walletconnect/logger": 2.1.2
    "@walletconnect/universal-provider": 2.21.0
    valtio: 1.13.2
    viem: ">=2.29.0"
  peerDependencies:
    valtio: 1.13.2
  checksum: 54ed191019815d20c4b817ecc81b49b888ac65ae0632ff724eb51033f3cd72fd40461568e69168a2003c858c80d1f4a555ca449cfef6c0cbbd7ae66c2bd8111e
  languageName: node
  linkType: hard

"@reown/appkit-wallet@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit-wallet@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": 1.7.8
    "@reown/appkit-polyfills": 1.7.8
    "@walletconnect/logger": 2.1.2
    zod: 3.22.4
  checksum: 4b1caaae2ca188f56298c1d835b3d8e89d00889c38a7964ac7e7c4e8b97cf06e5bcfb224a5076e8d9a0ac926d4fcda30733d7ca4a04f15c11b83c55a632964d4
  languageName: node
  linkType: hard

"@reown/appkit@npm:1.7.8":
  version: 1.7.8
  resolution: "@reown/appkit@npm:1.7.8"
  dependencies:
    "@reown/appkit-common": 1.7.8
    "@reown/appkit-controllers": 1.7.8
    "@reown/appkit-pay": 1.7.8
    "@reown/appkit-polyfills": 1.7.8
    "@reown/appkit-scaffold-ui": 1.7.8
    "@reown/appkit-ui": 1.7.8
    "@reown/appkit-utils": 1.7.8
    "@reown/appkit-wallet": 1.7.8
    "@walletconnect/types": 2.21.0
    "@walletconnect/universal-provider": 2.21.0
    bs58: 6.0.0
    valtio: 1.13.2
    viem: ">=2.29.0"
  checksum: 1f74d86988fb8ad6b449588572dfeb64c186c3ae5ca3617b2794f3d566dcc66d2154d9f105fc2d9f560557e0fca62240bff49ec6fbeb2c012b456363e8d16600
  languageName: node
  linkType: hard

"@rtsao/scc@npm:^1.1.0":
  version: 1.1.0
  resolution: "@rtsao/scc@npm:1.1.0"
  checksum: 17d04adf404e04c1e61391ed97bca5117d4c2767a76ae3e879390d6dec7b317fcae68afbf9e98badee075d0b64fa60f287729c4942021b4d19cd01db77385c01
  languageName: node
  linkType: hard

"@rushstack/eslint-patch@npm:^1.10.3":
  version: 1.12.0
  resolution: "@rushstack/eslint-patch@npm:1.12.0"
  checksum: 186788a93e2f141f622696091a593727fe7964d4925236a308e29754e29dcb182377f8d292ae954d227fb0574433863af055c0156593a40fd525e88b76e891ec
  languageName: node
  linkType: hard

"@safe-global/safe-apps-provider@npm:0.18.6":
  version: 0.18.6
  resolution: "@safe-global/safe-apps-provider@npm:0.18.6"
  dependencies:
    "@safe-global/safe-apps-sdk": ^9.1.0
    events: ^3.3.0
  checksum: af7e054f5170c8bec6feddf6a3cc09277a93219f164c4d0b49cdaef5c7e725ba2e414df17b2b1df85fbab10a8d8fad66c63f76ce3dfe042ee37aefb246edab6d
  languageName: node
  linkType: hard

"@safe-global/safe-apps-sdk@npm:9.1.0, @safe-global/safe-apps-sdk@npm:^9.1.0":
  version: 9.1.0
  resolution: "@safe-global/safe-apps-sdk@npm:9.1.0"
  dependencies:
    "@safe-global/safe-gateway-typescript-sdk": ^3.5.3
    viem: ^2.1.1
  checksum: e56c3fe83f52667b370072807468b011e9f3e6d690126af4cc5b13ee1544dd5a91b4b3e962d45d2dab065fc4401ef57c350896a9f43c70a9fb3269249f265d72
  languageName: node
  linkType: hard

"@safe-global/safe-gateway-typescript-sdk@npm:^3.5.3":
  version: 3.23.1
  resolution: "@safe-global/safe-gateway-typescript-sdk@npm:3.23.1"
  checksum: 43cb1789f852fa5319ad11004cb94fb31fba8e5997f272ae25836c076e9ab0d4f296dccbc812409ab658bb3887dae4b4819ff4b5afc2c82ca3c63621be0a1635
  languageName: node
  linkType: hard

"@scure/base@npm:^1.1.3, @scure/base@npm:~1.2.2, @scure/base@npm:~1.2.4, @scure/base@npm:~1.2.5":
  version: 1.2.6
  resolution: "@scure/base@npm:1.2.6"
  checksum: 1058cb26d5e4c1c46c9cc0ae0b67cc66d306733baf35d6ebdd8ddaba242b80c3807b726e3b48cb0411bb95ec10d37764969063ea62188f86ae9315df8ea6b325
  languageName: node
  linkType: hard

"@scure/base@npm:~1.1.6":
  version: 1.1.9
  resolution: "@scure/base@npm:1.1.9"
  checksum: 120820a37dfe9dfe4cab2b7b7460552d08e67dee8057ed5354eb68d8e3440890ae983ce3bee957d2b45684950b454a2b6d71d5ee77c1fd3fddc022e2a510337f
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.4.0":
  version: 1.4.0
  resolution: "@scure/bip32@npm:1.4.0"
  dependencies:
    "@noble/curves": ~1.4.0
    "@noble/hashes": ~1.4.0
    "@scure/base": ~1.1.6
  checksum: eff491651cbf2bea8784936de75af5fc020fc1bbb9bcb26b2cfeefbd1fb2440ebfaf30c0733ca11c0ae1e272a2ef4c3c34ba5c9fb3e1091c3285a4272045b0c6
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.6.2":
  version: 1.6.2
  resolution: "@scure/bip32@npm:1.6.2"
  dependencies:
    "@noble/curves": ~1.8.1
    "@noble/hashes": ~1.7.1
    "@scure/base": ~1.2.2
  checksum: e7586619f8a669e522267ce71a90b2d00c3a91da658f1f50e54072cf9f432ba26d2bb4d3d91a5d06932ab96612b8bd038bc31d885bbc048cebfb6509c4a790fc
  languageName: node
  linkType: hard

"@scure/bip32@npm:1.7.0, @scure/bip32@npm:^1.5.0, @scure/bip32@npm:^1.7.0":
  version: 1.7.0
  resolution: "@scure/bip32@npm:1.7.0"
  dependencies:
    "@noble/curves": ~1.9.0
    "@noble/hashes": ~1.8.0
    "@scure/base": ~1.2.5
  checksum: c83adca5a74ec5c4ded8ba93900d0065e4767c4759cf24c2674923aef01d45ba56f171574e3519f2341be99f53a333f01b674eb6cfeb6fa8379607c6d1bc90b5
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.3.0":
  version: 1.3.0
  resolution: "@scure/bip39@npm:1.3.0"
  dependencies:
    "@noble/hashes": ~1.4.0
    "@scure/base": ~1.1.6
  checksum: dbb0b27df753eb6c6380010b25cc9a9ea31f9cb08864fc51e69e5880ff7e2b8f85b72caea1f1f28af165e83b72c48dd38617e43fc632779d025b50ba32ea759e
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.5.4":
  version: 1.5.4
  resolution: "@scure/bip39@npm:1.5.4"
  dependencies:
    "@noble/hashes": ~1.7.1
    "@scure/base": ~1.2.4
  checksum: 744f302559ad05ee6ea4928572ac8f0b5443e8068fd53234c9c2e158814e910a043c54f0688d05546decadd2ff66e0d0c76355d10e103a28cb8f44efe140857a
  languageName: node
  linkType: hard

"@scure/bip39@npm:1.6.0, @scure/bip39@npm:^1.4.0, @scure/bip39@npm:^1.6.0":
  version: 1.6.0
  resolution: "@scure/bip39@npm:1.6.0"
  dependencies:
    "@noble/hashes": ~1.8.0
    "@scure/base": ~1.2.5
  checksum: 96d46420780473d6c6c9700254a0eceec60302f61d7f9d7f29024e90c7acff3e8e40a5ee52dfaf104db539a10462e531996aaf9e69f082b8540b0a25870545fc
  languageName: node
  linkType: hard

"@socket.io/component-emitter@npm:~3.1.0":
  version: 3.1.2
  resolution: "@socket.io/component-emitter@npm:3.1.2"
  checksum: 89888f00699eb34e3070624eb7b8161fa29f064aeb1389a48f02195d55dd7c52a504e52160016859f6d6dffddd54324623cdd47fd34b3d46f9ed96c18c456edc
  languageName: node
  linkType: hard

"@swc/counter@npm:0.1.3":
  version: 0.1.3
  resolution: "@swc/counter@npm:0.1.3"
  checksum: df8f9cfba9904d3d60f511664c70d23bb323b3a0803ec9890f60133954173047ba9bdeabce28cd70ba89ccd3fd6c71c7b0bd58be85f611e1ffbe5d5c18616598
  languageName: node
  linkType: hard

"@swc/helpers@npm:0.5.15":
  version: 0.5.15
  resolution: "@swc/helpers@npm:0.5.15"
  dependencies:
    tslib: ^2.8.0
  checksum: 1a9e0dbb792b2d1e0c914d69c201dbc96af3a0e6e6e8cf5a7f7d6a5d7b0e8b762915cd4447acb6b040e2ecc1ed49822875a7239f99a2d63c96c3c3407fb6fccf
  languageName: node
  linkType: hard

"@tailwindcss/node@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/node@npm:4.1.11"
  dependencies:
    "@ampproject/remapping": ^2.3.0
    enhanced-resolve: ^5.18.1
    jiti: ^2.4.2
    lightningcss: 1.30.1
    magic-string: ^0.30.17
    source-map-js: ^1.2.1
    tailwindcss: 4.1.11
  checksum: b6485dc0302473daa8ae7f71c3a1412e2789aa38feeb0364ff8b6a3664215fb958a6bdbf5bd3923be3a7c5baa5c5c80eb5527b8c7fc667ef652405a416d91dbc
  languageName: node
  linkType: hard

"@tailwindcss/oxide-android-arm64@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-android-arm64@npm:4.1.11"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-darwin-arm64@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-darwin-arm64@npm:4.1.11"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-darwin-x64@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-darwin-x64@npm:4.1.11"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-freebsd-x64@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-freebsd-x64@npm:4.1.11"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-arm-gnueabihf@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-linux-arm-gnueabihf@npm:4.1.11"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-arm64-gnu@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-linux-arm64-gnu@npm:4.1.11"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-arm64-musl@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-linux-arm64-musl@npm:4.1.11"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-x64-gnu@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-linux-x64-gnu@npm:4.1.11"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@tailwindcss/oxide-linux-x64-musl@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-linux-x64-musl@npm:4.1.11"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@tailwindcss/oxide-wasm32-wasi@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-wasm32-wasi@npm:4.1.11"
  dependencies:
    "@emnapi/core": ^1.4.3
    "@emnapi/runtime": ^1.4.3
    "@emnapi/wasi-threads": ^1.0.2
    "@napi-rs/wasm-runtime": ^0.2.11
    "@tybys/wasm-util": ^0.9.0
    tslib: ^2.8.0
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@tailwindcss/oxide-win32-arm64-msvc@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-win32-arm64-msvc@npm:4.1.11"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@tailwindcss/oxide-win32-x64-msvc@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide-win32-x64-msvc@npm:4.1.11"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@tailwindcss/oxide@npm:4.1.11":
  version: 4.1.11
  resolution: "@tailwindcss/oxide@npm:4.1.11"
  dependencies:
    "@tailwindcss/oxide-android-arm64": 4.1.11
    "@tailwindcss/oxide-darwin-arm64": 4.1.11
    "@tailwindcss/oxide-darwin-x64": 4.1.11
    "@tailwindcss/oxide-freebsd-x64": 4.1.11
    "@tailwindcss/oxide-linux-arm-gnueabihf": 4.1.11
    "@tailwindcss/oxide-linux-arm64-gnu": 4.1.11
    "@tailwindcss/oxide-linux-arm64-musl": 4.1.11
    "@tailwindcss/oxide-linux-x64-gnu": 4.1.11
    "@tailwindcss/oxide-linux-x64-musl": 4.1.11
    "@tailwindcss/oxide-wasm32-wasi": 4.1.11
    "@tailwindcss/oxide-win32-arm64-msvc": 4.1.11
    "@tailwindcss/oxide-win32-x64-msvc": 4.1.11
    detect-libc: ^2.0.4
    tar: ^7.4.3
  dependenciesMeta:
    "@tailwindcss/oxide-android-arm64":
      optional: true
    "@tailwindcss/oxide-darwin-arm64":
      optional: true
    "@tailwindcss/oxide-darwin-x64":
      optional: true
    "@tailwindcss/oxide-freebsd-x64":
      optional: true
    "@tailwindcss/oxide-linux-arm-gnueabihf":
      optional: true
    "@tailwindcss/oxide-linux-arm64-gnu":
      optional: true
    "@tailwindcss/oxide-linux-arm64-musl":
      optional: true
    "@tailwindcss/oxide-linux-x64-gnu":
      optional: true
    "@tailwindcss/oxide-linux-x64-musl":
      optional: true
    "@tailwindcss/oxide-wasm32-wasi":
      optional: true
    "@tailwindcss/oxide-win32-arm64-msvc":
      optional: true
    "@tailwindcss/oxide-win32-x64-msvc":
      optional: true
  checksum: 2bfe3bd53e4c50a0aa32571e5a18f2199530207ffa909fcb22f3f49bab43ae7997e44d5f37b2c2f41513e7f926838447beff24d63baca77cbff2fe82bc259492
  languageName: node
  linkType: hard

"@tailwindcss/postcss@npm:^4":
  version: 4.1.11
  resolution: "@tailwindcss/postcss@npm:4.1.11"
  dependencies:
    "@alloc/quick-lru": ^5.2.0
    "@tailwindcss/node": 4.1.11
    "@tailwindcss/oxide": 4.1.11
    postcss: ^8.4.41
    tailwindcss: 4.1.11
  checksum: d22201025bfdd574939021e40075f11670086b6f56db929513fbc165f11a74711f55de4a904ce0ec5110272377b88430c1a8aa080bce6c4434034203cd6e6a8c
  languageName: node
  linkType: hard

"@tanstack/query-core@npm:5.81.5":
  version: 5.81.5
  resolution: "@tanstack/query-core@npm:5.81.5"
  checksum: 0072364aa08627663ec92ecbe5bfd89b989bb9e613801f7751c09905fe4bdbf1e88220db37a490fe06cbf19e061b8c440c85ff0366c83753ee4070ec9cf565b9
  languageName: node
  linkType: hard

"@tanstack/react-query@npm:^5.81.5":
  version: 5.81.5
  resolution: "@tanstack/react-query@npm:5.81.5"
  dependencies:
    "@tanstack/query-core": 5.81.5
  peerDependencies:
    react: ^18 || ^19
  checksum: 0847149af07068b4cfed3df70c1028bcba54a6e22e4e26da31dc8e4b0c5237bae7004f6ea01f13fe867a08bd6195a158128c76d7e2b6b690453fbce6c6742bb3
  languageName: node
  linkType: hard

"@tybys/wasm-util@npm:^0.9.0":
  version: 0.9.0
  resolution: "@tybys/wasm-util@npm:0.9.0"
  dependencies:
    tslib: ^2.4.0
  checksum: 8d44c64e64e39c746e45b5dff7b534716f20e1f6e8fc206f8e4c8ac454ec0eb35b65646e446dd80745bc898db37a4eca549a936766d447c2158c9c43d44e7708
  languageName: node
  linkType: hard

"@types/debug@npm:^4.1.7":
  version: 4.1.12
  resolution: "@types/debug@npm:4.1.12"
  dependencies:
    "@types/ms": "*"
  checksum: 47876a852de8240bfdaf7481357af2b88cb660d30c72e73789abf00c499d6bc7cd5e52f41c915d1b9cd8ec9fef5b05688d7b7aef17f7f272c2d04679508d1053
  languageName: node
  linkType: hard

"@types/estree@npm:^1.0.6":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: bd93e2e415b6f182ec4da1074e1f36c480f1d26add3e696d54fb30c09bc470897e41361c8fd957bf0985024f8fbf1e6e2aff977d79352ef7eb93a5c6dcff6c11
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 97ed0cb44d4070aecea772b7b2e2ed971e10c81ec87dd4ecc160322ffa55ff330dace1793489540e3e318d90942064bb697cc0f8989391797792d919737b3b98
  languageName: node
  linkType: hard

"@types/json5@npm:^0.0.29":
  version: 0.0.29
  resolution: "@types/json5@npm:0.0.29"
  checksum: e60b153664572116dfea673c5bda7778dbff150498f44f998e34b5886d8afc47f16799280e4b6e241c0472aef1bc36add771c569c68fc5125fc2ae519a3eb9ac
  languageName: node
  linkType: hard

"@types/ms@npm:*":
  version: 2.1.0
  resolution: "@types/ms@npm:2.1.0"
  checksum: 532d2ebb91937ccc4a89389715e5b47d4c66e708d15942fe6cc25add6dc37b2be058230a327dd50f43f89b8b6d5d52b74685a9e8f70516edfc9bdd6be910eff4
  languageName: node
  linkType: hard

"@types/node@npm:^20":
  version: 20.19.2
  resolution: "@types/node@npm:20.19.2"
  dependencies:
    undici-types: ~6.21.0
  checksum: bbff606094c24c96cb3ed9a72d0d18473cf59864db70a4fc85189a9c41072fe9eeeff29601ce086c62255f335e0b6a7c8d8095c75dfe0da0c2daef37486543b5
  languageName: node
  linkType: hard

"@types/react-dom@npm:^19":
  version: 19.1.6
  resolution: "@types/react-dom@npm:19.1.6"
  peerDependencies:
    "@types/react": ^19.0.0
  checksum: b5b20b7f0797f34c5a11915b74dcf8b3b7a9da9fea90279975ce6f150ca5d31bb069dbb0838638a5e9e168098aa4bb4a6f61d078efa1bbb55d7f0bdfe47bb142
  languageName: node
  linkType: hard

"@types/react@npm:^19":
  version: 19.1.8
  resolution: "@types/react@npm:19.1.8"
  dependencies:
    csstype: ^3.0.2
  checksum: 17e0c74d9c01214938fa805aaa8b97925bf3c5514e88fdf94bec42c0a6d4abbc63d4e30255db176f46fd7f0aa89f8085b9b2b2fa5abaffbbf7e5009386ada892
  languageName: node
  linkType: hard

"@types/trusted-types@npm:^2.0.2":
  version: 2.0.7
  resolution: "@types/trusted-types@npm:2.0.7"
  checksum: 8e4202766a65877efcf5d5a41b7dd458480b36195e580a3b1085ad21e948bc417d55d6f8af1fd2a7ad008015d4117d5fdfe432731157da3c68678487174e4ba3
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0":
  version: 8.35.0
  resolution: "@typescript-eslint/eslint-plugin@npm:8.35.0"
  dependencies:
    "@eslint-community/regexpp": ^4.10.0
    "@typescript-eslint/scope-manager": 8.35.0
    "@typescript-eslint/type-utils": 8.35.0
    "@typescript-eslint/utils": 8.35.0
    "@typescript-eslint/visitor-keys": 8.35.0
    graphemer: ^1.4.0
    ignore: ^7.0.0
    natural-compare: ^1.4.0
    ts-api-utils: ^2.1.0
  peerDependencies:
    "@typescript-eslint/parser": ^8.35.0
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: ab739a50ba81ffaa0a22d5c1b1ec23abb2e8e19f07ff0a34967510c8a03537e2bc67095a8de15d2c5f42a6fdef1f71b3b5985273373f37d7b06b92373ba2b597
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0":
  version: 8.35.0
  resolution: "@typescript-eslint/parser@npm:8.35.0"
  dependencies:
    "@typescript-eslint/scope-manager": 8.35.0
    "@typescript-eslint/types": 8.35.0
    "@typescript-eslint/typescript-estree": 8.35.0
    "@typescript-eslint/visitor-keys": 8.35.0
    debug: ^4.3.4
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: f9c2843cb0f487fab0b0550ea3a63baaf7ef2b6f0ffbef149f9b09c45833e64713143f9e6b62be89a4e317191e0985455b3841d4cfa24fcc0596ae4d7697dfd0
  languageName: node
  linkType: hard

"@typescript-eslint/project-service@npm:8.35.0":
  version: 8.35.0
  resolution: "@typescript-eslint/project-service@npm:8.35.0"
  dependencies:
    "@typescript-eslint/tsconfig-utils": ^8.35.0
    "@typescript-eslint/types": ^8.35.0
    debug: ^4.3.4
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: af8beab1f4bec3a4cc2a0a7a0e1acbb8eb47ca96cda84eafb6b7416ba44e0e41be854f99a0e00154f67caa70dfcda905ef0abef93470ec4832d8a3277916f5db
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:8.35.0":
  version: 8.35.0
  resolution: "@typescript-eslint/scope-manager@npm:8.35.0"
  dependencies:
    "@typescript-eslint/types": 8.35.0
    "@typescript-eslint/visitor-keys": 8.35.0
  checksum: b7514df5688498e33f2b27a821925b5e1120884f0603b23cc39f02fc151985a72d1ec75af0cc1ec422bd4ec22f178d02c9461078fe666d4709806eb91522cd82
  languageName: node
  linkType: hard

"@typescript-eslint/tsconfig-utils@npm:8.35.0, @typescript-eslint/tsconfig-utils@npm:^8.35.0":
  version: 8.35.0
  resolution: "@typescript-eslint/tsconfig-utils@npm:8.35.0"
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 4160928313ccbe8b169a009b9c1220826c7df7aab427f960c31f3b838931bc7a121ebee8040118481e4528e2e3cf1b26da047c6ac1d802ecff2ef7206026ea6b
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:8.35.0":
  version: 8.35.0
  resolution: "@typescript-eslint/type-utils@npm:8.35.0"
  dependencies:
    "@typescript-eslint/typescript-estree": 8.35.0
    "@typescript-eslint/utils": 8.35.0
    debug: ^4.3.4
    ts-api-utils: ^2.1.0
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 2bb3fc3cd6be3bcfe33d9361e002ae04bc712bf0df899b0dac5b533222149ecdd93a69f54665bfab5868f7ea6a31194fbe739d3a617857484dcdbfb2c9207046
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:8.35.0, @typescript-eslint/types@npm:^8.35.0":
  version: 8.35.0
  resolution: "@typescript-eslint/types@npm:8.35.0"
  checksum: d8f40b49eafc3530a0cdd3b353ffbf0b3f5f1b938c25cf0400b977881860c4687450d9df43033a43c593686f47c1cee0924e97a43afdd817bfda9ae7351005bf
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:8.35.0":
  version: 8.35.0
  resolution: "@typescript-eslint/typescript-estree@npm:8.35.0"
  dependencies:
    "@typescript-eslint/project-service": 8.35.0
    "@typescript-eslint/tsconfig-utils": 8.35.0
    "@typescript-eslint/types": 8.35.0
    "@typescript-eslint/visitor-keys": 8.35.0
    debug: ^4.3.4
    fast-glob: ^3.3.2
    is-glob: ^4.0.3
    minimatch: ^9.0.4
    semver: ^7.6.0
    ts-api-utils: ^2.1.0
  peerDependencies:
    typescript: ">=4.8.4 <5.9.0"
  checksum: 3849c88af2c8bd9e62d0410a2a558dd8a6ee72927cbc696c95ac6d3172ba727763b13401f43c67df23138fde1aea81bf6134c9aa56ce018d282c9dc0be2fb8ca
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:8.35.0":
  version: 8.35.0
  resolution: "@typescript-eslint/utils@npm:8.35.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.7.0
    "@typescript-eslint/scope-manager": 8.35.0
    "@typescript-eslint/types": 8.35.0
    "@typescript-eslint/typescript-estree": 8.35.0
  peerDependencies:
    eslint: ^8.57.0 || ^9.0.0
    typescript: ">=4.8.4 <5.9.0"
  checksum: 60383869836da221d79a5ca64891baa1feff6b365721f8c9985a1a384e6fb709c5b9d003c9b92bd407d67be629095f3b2d6713a72cfb0241388e1074a11e4607
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:8.35.0":
  version: 8.35.0
  resolution: "@typescript-eslint/visitor-keys@npm:8.35.0"
  dependencies:
    "@typescript-eslint/types": 8.35.0
    eslint-visitor-keys: ^4.2.1
  checksum: 0c5996d24aaf13fd21725ffba7375a24addc006bc8ef8d60ccf60e12383a37e25ce0014e3ec20717acd0c75eaa0e8e087e6cae4afc6333c5ad05e85152e4d732
  languageName: node
  linkType: hard

"@unrs/resolver-binding-android-arm-eabi@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-android-arm-eabi@npm:1.9.2"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-android-arm64@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-android-arm64@npm:1.9.2"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-arm64@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-darwin-arm64@npm:1.9.2"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-darwin-x64@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-darwin-x64@npm:1.9.2"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-freebsd-x64@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-freebsd-x64@npm:1.9.2"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-arm-gnueabihf@npm:1.9.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm-musleabihf@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-arm-musleabihf@npm:1.9.2"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-gnu@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-arm64-gnu@npm:1.9.2"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-arm64-musl@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-arm64-musl@npm:1.9.2"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-ppc64-gnu@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-ppc64-gnu@npm:1.9.2"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-gnu@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-riscv64-gnu@npm:1.9.2"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-riscv64-musl@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-riscv64-musl@npm:1.9.2"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-s390x-gnu@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-s390x-gnu@npm:1.9.2"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-gnu@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-x64-gnu@npm:1.9.2"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@unrs/resolver-binding-linux-x64-musl@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-linux-x64-musl@npm:1.9.2"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@unrs/resolver-binding-wasm32-wasi@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-wasm32-wasi@npm:1.9.2"
  dependencies:
    "@napi-rs/wasm-runtime": ^0.2.11
  conditions: cpu=wasm32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-arm64-msvc@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-win32-arm64-msvc@npm:1.9.2"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-ia32-msvc@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-win32-ia32-msvc@npm:1.9.2"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@unrs/resolver-binding-win32-x64-msvc@npm:1.9.2":
  version: 1.9.2
  resolution: "@unrs/resolver-binding-win32-x64-msvc@npm:1.9.2"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@wagmi/connectors@npm:5.8.5":
  version: 5.8.5
  resolution: "@wagmi/connectors@npm:5.8.5"
  dependencies:
    "@coinbase/wallet-sdk": 4.3.3
    "@metamask/sdk": 0.32.0
    "@safe-global/safe-apps-provider": 0.18.6
    "@safe-global/safe-apps-sdk": 9.1.0
    "@walletconnect/ethereum-provider": 2.21.1
    cbw-sdk: "npm:@coinbase/wallet-sdk@3.9.3"
  peerDependencies:
    "@wagmi/core": 2.17.3
    typescript: ">=5.0.4"
    viem: 2.x
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: c4237b265da17c4861173bc50a0ab545c34f3d6dd9c2716787ec98d36896c9617a2cccb5ec02e7f9c807305c0040db0a472e34bc7bcd6044e9bf1364a061409b
  languageName: node
  linkType: hard

"@wagmi/core@npm:2.17.3":
  version: 2.17.3
  resolution: "@wagmi/core@npm:2.17.3"
  dependencies:
    eventemitter3: 5.0.1
    mipd: 0.0.7
    zustand: 5.0.0
  peerDependencies:
    "@tanstack/query-core": ">=5.0.0"
    typescript: ">=5.0.4"
    viem: 2.x
  peerDependenciesMeta:
    "@tanstack/query-core":
      optional: true
    typescript:
      optional: true
  checksum: 94441fede60a78b6479534b611b08dad7325980bb7f22320a8b358afb52c1b1069c186a5fd3cab4438de6f541a6aa4c8ef94b8b9c417ebbefa344bbe8dc1bd33
  languageName: node
  linkType: hard

"@walletconnect/core@npm:2.21.0":
  version: 2.21.0
  resolution: "@walletconnect/core@npm:2.21.0"
  dependencies:
    "@walletconnect/heartbeat": 1.2.2
    "@walletconnect/jsonrpc-provider": 1.0.14
    "@walletconnect/jsonrpc-types": 1.0.4
    "@walletconnect/jsonrpc-utils": 1.0.8
    "@walletconnect/jsonrpc-ws-connection": 1.0.16
    "@walletconnect/keyvaluestorage": 1.1.1
    "@walletconnect/logger": 2.1.2
    "@walletconnect/relay-api": 1.0.11
    "@walletconnect/relay-auth": 1.1.0
    "@walletconnect/safe-json": 1.0.2
    "@walletconnect/time": 1.0.2
    "@walletconnect/types": 2.21.0
    "@walletconnect/utils": 2.21.0
    "@walletconnect/window-getters": 1.0.1
    es-toolkit: 1.33.0
    events: 3.3.0
    uint8arrays: 3.1.0
  checksum: befd35b7a140af49d470020fa3b88a6ff83a3e10a6c82b6a434f376b5a87c4f0a827186d5322d16b942c404ff691bdf769ec29171a0c3db474a654ddb5d4b0a6
  languageName: node
  linkType: hard

"@walletconnect/core@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/core@npm:2.21.1"
  dependencies:
    "@walletconnect/heartbeat": 1.2.2
    "@walletconnect/jsonrpc-provider": 1.0.14
    "@walletconnect/jsonrpc-types": 1.0.4
    "@walletconnect/jsonrpc-utils": 1.0.8
    "@walletconnect/jsonrpc-ws-connection": 1.0.16
    "@walletconnect/keyvaluestorage": 1.1.1
    "@walletconnect/logger": 2.1.2
    "@walletconnect/relay-api": 1.0.11
    "@walletconnect/relay-auth": 1.1.0
    "@walletconnect/safe-json": 1.0.2
    "@walletconnect/time": 1.0.2
    "@walletconnect/types": 2.21.1
    "@walletconnect/utils": 2.21.1
    "@walletconnect/window-getters": 1.0.1
    es-toolkit: 1.33.0
    events: 3.3.0
    uint8arrays: 3.1.0
  checksum: 2ac8f4dca65b51bc449e8677b491d47a64a792929e2d624a8fbe153aac40d84706c16252904ae0050595b4d637f81e4b92ff70e4b063f048e1e069848531ed5a
  languageName: node
  linkType: hard

"@walletconnect/environment@npm:^1.0.1":
  version: 1.0.1
  resolution: "@walletconnect/environment@npm:1.0.1"
  dependencies:
    tslib: 1.14.1
  checksum: a18731d857bdca73910147e59992cef3c6e292c37ab3d3013307bd706f06cb216aa804f0f48b25a78df6493ad8127e633629f4b50acb4f69d3765d6ac0524f68
  languageName: node
  linkType: hard

"@walletconnect/ethereum-provider@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/ethereum-provider@npm:2.21.1"
  dependencies:
    "@reown/appkit": 1.7.8
    "@walletconnect/jsonrpc-http-connection": 1.0.8
    "@walletconnect/jsonrpc-provider": 1.0.14
    "@walletconnect/jsonrpc-types": 1.0.4
    "@walletconnect/jsonrpc-utils": 1.0.8
    "@walletconnect/keyvaluestorage": 1.1.1
    "@walletconnect/sign-client": 2.21.1
    "@walletconnect/types": 2.21.1
    "@walletconnect/universal-provider": 2.21.1
    "@walletconnect/utils": 2.21.1
    events: 3.3.0
  checksum: 97af3b10f6c7fcd8d86bb9c445983e2736b4f590c9170d9075e6345815a6c4a515461b004064aa5c8555c35b2603fd9027e883fe126a7af9ac1d73c9d226d4e2
  languageName: node
  linkType: hard

"@walletconnect/events@npm:1.0.1, @walletconnect/events@npm:^1.0.1":
  version: 1.0.1
  resolution: "@walletconnect/events@npm:1.0.1"
  dependencies:
    keyvaluestorage-interface: ^1.0.0
    tslib: 1.14.1
  checksum: d28aa4dcc981bdaf38f0aeed979731ca793cead7e7a4ee730a9146d99d89db09a86c8e3192ed860638283276961c0723ba00cf3b8776f0692b36ec7df6c01be4
  languageName: node
  linkType: hard

"@walletconnect/heartbeat@npm:1.2.2":
  version: 1.2.2
  resolution: "@walletconnect/heartbeat@npm:1.2.2"
  dependencies:
    "@walletconnect/events": ^1.0.1
    "@walletconnect/time": ^1.0.2
    events: ^3.3.0
  checksum: 720341f24dae64acc836015d694b4337a0d1cbc628a3f6ee556771278465cae61366fb0e5af93f9823b06a6f4e23013f3986d6dad2a58c2db4b7c991a73c646d
  languageName: node
  linkType: hard

"@walletconnect/jsonrpc-http-connection@npm:1.0.8":
  version: 1.0.8
  resolution: "@walletconnect/jsonrpc-http-connection@npm:1.0.8"
  dependencies:
    "@walletconnect/jsonrpc-utils": ^1.0.6
    "@walletconnect/safe-json": ^1.0.1
    cross-fetch: ^3.1.4
    events: ^3.3.0
  checksum: 2b7c49aca54af2ec37f7bb493062e0bb522ec262889890aa00c133a85dd39beffcfcdb73252600446383a9e6db360cffdc6c50ef7c9b68aae7f5606c8781bdbc
  languageName: node
  linkType: hard

"@walletconnect/jsonrpc-provider@npm:1.0.14":
  version: 1.0.14
  resolution: "@walletconnect/jsonrpc-provider@npm:1.0.14"
  dependencies:
    "@walletconnect/jsonrpc-utils": ^1.0.8
    "@walletconnect/safe-json": ^1.0.2
    events: ^3.3.0
  checksum: db8f931f93285520c51939603108f5cfe2a90a651d12744766d14471db3a488d2964ece5bfedc6cc93832ecd008cd37e7e1b1a950d9ef3385106ee052b936573
  languageName: node
  linkType: hard

"@walletconnect/jsonrpc-types@npm:1.0.4, @walletconnect/jsonrpc-types@npm:^1.0.2, @walletconnect/jsonrpc-types@npm:^1.0.3":
  version: 1.0.4
  resolution: "@walletconnect/jsonrpc-types@npm:1.0.4"
  dependencies:
    events: ^3.3.0
    keyvaluestorage-interface: ^1.0.0
  checksum: 99ea5f9f3b0c5892ff874de87dee62cf4fc345124177db1e6e5eaf48b85e2ea3833f0157beca43c51047444938e8eda6362fa8069b33e11d39e1050e7ef6e821
  languageName: node
  linkType: hard

"@walletconnect/jsonrpc-utils@npm:1.0.8, @walletconnect/jsonrpc-utils@npm:^1.0.6, @walletconnect/jsonrpc-utils@npm:^1.0.8":
  version: 1.0.8
  resolution: "@walletconnect/jsonrpc-utils@npm:1.0.8"
  dependencies:
    "@walletconnect/environment": ^1.0.1
    "@walletconnect/jsonrpc-types": ^1.0.3
    tslib: 1.14.1
  checksum: f43a85dfce8150c3e3d1f009e8d8241ab8e10b026ea435f0918edf4db6b3a17586ba9d9c54a93cc61e4d3c685611e5bd5954fc377a581af503acd38e6d84c2ef
  languageName: node
  linkType: hard

"@walletconnect/jsonrpc-ws-connection@npm:1.0.16":
  version: 1.0.16
  resolution: "@walletconnect/jsonrpc-ws-connection@npm:1.0.16"
  dependencies:
    "@walletconnect/jsonrpc-utils": ^1.0.6
    "@walletconnect/safe-json": ^1.0.2
    events: ^3.3.0
    ws: ^7.5.1
  checksum: 8d1b551d69f8a5b27894d2b37cfd28d407634a95acc920db127daa4a20999676780ce157ba44614e3c048acfe8adc494592bd49f314c1601e6daf60e2bbae385
  languageName: node
  linkType: hard

"@walletconnect/keyvaluestorage@npm:1.1.1":
  version: 1.1.1
  resolution: "@walletconnect/keyvaluestorage@npm:1.1.1"
  dependencies:
    "@walletconnect/safe-json": ^1.0.1
    idb-keyval: ^6.2.1
    unstorage: ^1.9.0
  peerDependencies:
    "@react-native-async-storage/async-storage": 1.x
  peerDependenciesMeta:
    "@react-native-async-storage/async-storage":
      optional: true
  checksum: 7f85cb83963153417745367742070ccb78e03bd62adb549de57a7d5fae7bcfbd9a8f42b2f445ca76a3817ffacacc69d85bbf67757c3616ee7b3525f2f8a0faea
  languageName: node
  linkType: hard

"@walletconnect/logger@npm:2.1.2":
  version: 2.1.2
  resolution: "@walletconnect/logger@npm:2.1.2"
  dependencies:
    "@walletconnect/safe-json": ^1.0.2
    pino: 7.11.0
  checksum: a2bb88b76d95ec5a95279dcc919f1d044d17be8fdda98a01665a607561b445bb56f2245a280933fb19aa7d41d41b688d0ffdb434ac56c46163ad2eb5338f389a
  languageName: node
  linkType: hard

"@walletconnect/relay-api@npm:1.0.11":
  version: 1.0.11
  resolution: "@walletconnect/relay-api@npm:1.0.11"
  dependencies:
    "@walletconnect/jsonrpc-types": ^1.0.2
  checksum: 9fcddf055de01c04b9fa59035e8c6e31d523743c848d266f528009048aeadaa1b4d9b544bdcb6928e7a69f738d5f0352d1cdebbaa34b1346b937942cb5f6f144
  languageName: node
  linkType: hard

"@walletconnect/relay-auth@npm:1.1.0":
  version: 1.1.0
  resolution: "@walletconnect/relay-auth@npm:1.1.0"
  dependencies:
    "@noble/curves": 1.8.0
    "@noble/hashes": 1.7.0
    "@walletconnect/safe-json": ^1.0.1
    "@walletconnect/time": ^1.0.2
    uint8arrays: ^3.0.0
  checksum: 0081309d341ceab39bd4fc69cd0d92112a2df4ab3e9abab3ba8c03f6bdf3dddd556bdb4e4e091f02f54d02d0a3948be039e6792e213226e85718aab7dde1aea2
  languageName: node
  linkType: hard

"@walletconnect/safe-json@npm:1.0.2, @walletconnect/safe-json@npm:^1.0.1, @walletconnect/safe-json@npm:^1.0.2":
  version: 1.0.2
  resolution: "@walletconnect/safe-json@npm:1.0.2"
  dependencies:
    tslib: 1.14.1
  checksum: fee03fcc70adb5635ab9419ea6ec6555aa2467bef650ad3b9526451c3a5cf247836db0f3ae3bb435d2e585d99e50c2ebe7dc9c429cfa3df900cf3fe4bd06d37f
  languageName: node
  linkType: hard

"@walletconnect/sign-client@npm:2.21.0":
  version: 2.21.0
  resolution: "@walletconnect/sign-client@npm:2.21.0"
  dependencies:
    "@walletconnect/core": 2.21.0
    "@walletconnect/events": 1.0.1
    "@walletconnect/heartbeat": 1.2.2
    "@walletconnect/jsonrpc-utils": 1.0.8
    "@walletconnect/logger": 2.1.2
    "@walletconnect/time": 1.0.2
    "@walletconnect/types": 2.21.0
    "@walletconnect/utils": 2.21.0
    events: 3.3.0
  checksum: e68375a367540b443c4571e0d85e02e914a650ca871c9c2381a9c6499ce27af7d6ef881669a034eb3fe7cdad85d7d6475bbb10ccd87ffc57c73d6e999f87d1b9
  languageName: node
  linkType: hard

"@walletconnect/sign-client@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/sign-client@npm:2.21.1"
  dependencies:
    "@walletconnect/core": 2.21.1
    "@walletconnect/events": 1.0.1
    "@walletconnect/heartbeat": 1.2.2
    "@walletconnect/jsonrpc-utils": 1.0.8
    "@walletconnect/logger": 2.1.2
    "@walletconnect/time": 1.0.2
    "@walletconnect/types": 2.21.1
    "@walletconnect/utils": 2.21.1
    events: 3.3.0
  checksum: 261f4b2f0d17afd9de5652e47312a023126c1330493382068b8f7d42dba6f43d930d6f1d4f61544ec94a05961d666aa182ae9e3dd08814653d52072e0f293c32
  languageName: node
  linkType: hard

"@walletconnect/time@npm:1.0.2, @walletconnect/time@npm:^1.0.2":
  version: 1.0.2
  resolution: "@walletconnect/time@npm:1.0.2"
  dependencies:
    tslib: 1.14.1
  checksum: e3fc0113ca9e7ecedfc65f9e1517196682d5ffcda60750f51073b8d704719a17fea75da8b242c804bfa5b994707723043892a2db3cc86988b190b7b8711fe3c0
  languageName: node
  linkType: hard

"@walletconnect/types@npm:2.21.0":
  version: 2.21.0
  resolution: "@walletconnect/types@npm:2.21.0"
  dependencies:
    "@walletconnect/events": 1.0.1
    "@walletconnect/heartbeat": 1.2.2
    "@walletconnect/jsonrpc-types": 1.0.4
    "@walletconnect/keyvaluestorage": 1.1.1
    "@walletconnect/logger": 2.1.2
    events: 3.3.0
  checksum: 9991ebba37ed82cffa7c165054d5c22d05f5d504ba07986c6961d0cf6f67e1a8081725f19d74f48367a0b7072faa9db5a12e5a752a7377b5af64cbf536a30aff
  languageName: node
  linkType: hard

"@walletconnect/types@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/types@npm:2.21.1"
  dependencies:
    "@walletconnect/events": 1.0.1
    "@walletconnect/heartbeat": 1.2.2
    "@walletconnect/jsonrpc-types": 1.0.4
    "@walletconnect/keyvaluestorage": 1.1.1
    "@walletconnect/logger": 2.1.2
    events: 3.3.0
  checksum: fe76c5bbe28baaeabe308f0c2b82c15388f0609b0138d7f0148f520467660fc7920a1fdea52ce3cf2c830ba3699645492ba9fdd229ad6dda4cb400f4fd114ced
  languageName: node
  linkType: hard

"@walletconnect/universal-provider@npm:2.21.0":
  version: 2.21.0
  resolution: "@walletconnect/universal-provider@npm:2.21.0"
  dependencies:
    "@walletconnect/events": 1.0.1
    "@walletconnect/jsonrpc-http-connection": 1.0.8
    "@walletconnect/jsonrpc-provider": 1.0.14
    "@walletconnect/jsonrpc-types": 1.0.4
    "@walletconnect/jsonrpc-utils": 1.0.8
    "@walletconnect/keyvaluestorage": 1.1.1
    "@walletconnect/logger": 2.1.2
    "@walletconnect/sign-client": 2.21.0
    "@walletconnect/types": 2.21.0
    "@walletconnect/utils": 2.21.0
    es-toolkit: 1.33.0
    events: 3.3.0
  checksum: 2d73cf259ab4518d005c0af8d49c6a9142397e212e74fe966263a3843160f3b4be8b1d6e11a8421341dc0595bde443703d619a0e4bc3b731299263d4b43182cd
  languageName: node
  linkType: hard

"@walletconnect/universal-provider@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/universal-provider@npm:2.21.1"
  dependencies:
    "@walletconnect/events": 1.0.1
    "@walletconnect/jsonrpc-http-connection": 1.0.8
    "@walletconnect/jsonrpc-provider": 1.0.14
    "@walletconnect/jsonrpc-types": 1.0.4
    "@walletconnect/jsonrpc-utils": 1.0.8
    "@walletconnect/keyvaluestorage": 1.1.1
    "@walletconnect/logger": 2.1.2
    "@walletconnect/sign-client": 2.21.1
    "@walletconnect/types": 2.21.1
    "@walletconnect/utils": 2.21.1
    es-toolkit: 1.33.0
    events: 3.3.0
  checksum: fe75754137f779da299888abfe167c96e7dda5263c11c2c217f8c46107beef4f4c8e38eba445e65a2beb21ba7b491bca86c0ce12596da6b0400faad9468ca40c
  languageName: node
  linkType: hard

"@walletconnect/utils@npm:2.21.0":
  version: 2.21.0
  resolution: "@walletconnect/utils@npm:2.21.0"
  dependencies:
    "@noble/ciphers": 1.2.1
    "@noble/curves": 1.8.1
    "@noble/hashes": 1.7.1
    "@walletconnect/jsonrpc-utils": 1.0.8
    "@walletconnect/keyvaluestorage": 1.1.1
    "@walletconnect/relay-api": 1.0.11
    "@walletconnect/relay-auth": 1.1.0
    "@walletconnect/safe-json": 1.0.2
    "@walletconnect/time": 1.0.2
    "@walletconnect/types": 2.21.0
    "@walletconnect/window-getters": 1.0.1
    "@walletconnect/window-metadata": 1.0.1
    bs58: 6.0.0
    detect-browser: 5.3.0
    query-string: 7.1.3
    uint8arrays: 3.1.0
    viem: 2.23.2
  checksum: 3cd7ad9a8714ec955422832f384d21d0591ac130d0953ed45b34884b05489943a0fff324eaaf916eb058b334e42e338feb421aeca8f7900e470c56ef2a27faf0
  languageName: node
  linkType: hard

"@walletconnect/utils@npm:2.21.1":
  version: 2.21.1
  resolution: "@walletconnect/utils@npm:2.21.1"
  dependencies:
    "@noble/ciphers": 1.2.1
    "@noble/curves": 1.8.1
    "@noble/hashes": 1.7.1
    "@walletconnect/jsonrpc-utils": 1.0.8
    "@walletconnect/keyvaluestorage": 1.1.1
    "@walletconnect/relay-api": 1.0.11
    "@walletconnect/relay-auth": 1.1.0
    "@walletconnect/safe-json": 1.0.2
    "@walletconnect/time": 1.0.2
    "@walletconnect/types": 2.21.1
    "@walletconnect/window-getters": 1.0.1
    "@walletconnect/window-metadata": 1.0.1
    bs58: 6.0.0
    detect-browser: 5.3.0
    query-string: 7.1.3
    uint8arrays: 3.1.0
    viem: 2.23.2
  checksum: 53e4f31f64ed1d9dadc385f11788ff8a9094b76f72d405a207bb7a22d4a2a1d65412da59e31b504bed13a8b3edb1821868bf64c5ec69c86a4ffced6b7946e413
  languageName: node
  linkType: hard

"@walletconnect/window-getters@npm:1.0.1, @walletconnect/window-getters@npm:^1.0.1":
  version: 1.0.1
  resolution: "@walletconnect/window-getters@npm:1.0.1"
  dependencies:
    tslib: 1.14.1
  checksum: fae312c4e1be5574d97f071de58e6aa0d0296869761499caf9d4a9a5fd2643458af32233a2120521b00873a599ff88457d405bd82ced5fb5bd6dc3191c07a3e5
  languageName: node
  linkType: hard

"@walletconnect/window-metadata@npm:1.0.1":
  version: 1.0.1
  resolution: "@walletconnect/window-metadata@npm:1.0.1"
  dependencies:
    "@walletconnect/window-getters": ^1.0.1
    tslib: 1.14.1
  checksum: e82aea7195c6fe95c00e87bb38051c5549838c2e8302da94f1afa48206f79f0b620166c9820f847494505d282d1568e2086a1561b0493d2d0a1fa115f9106aef
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: e70b209f5f408dd3a3bbd0eec4b10a2ffd64704a4a3821d0969d84928cc490a8eb60f85b78a95622c1841113edac10161c62e52f5e7d0027aa26786a8136e02e
  languageName: node
  linkType: hard

"abitype@npm:1.0.8, abitype@npm:^1.0.6, abitype@npm:^1.0.8":
  version: 1.0.8
  resolution: "abitype@npm:1.0.8"
  peerDependencies:
    typescript: ">=5.0.4"
    zod: ^3 >=3.22.0
  peerDependenciesMeta:
    typescript:
      optional: true
    zod:
      optional: true
  checksum: 104bc2f6820ced8d2cb61521916f7f22c0981a846216f5b6144f69461265f7da137a4ae108bf4b84cd8743f2dd1e9fdadffc0f95371528e15a59e0a369e08438
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: ^5.0.0
  checksum: 170bdba9b47b7e65906a28c8ce4f38a7a369d78e2271706f020849c1bfe0ee2067d4261df8bbb66eb84f79208fd5b710df759d64191db58cfba7ce8ef9c54b75
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn@npm:^8.15.0":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 309c6b49aedf1a2e34aaf266de06de04aab6eb097c02375c66fdeb0f64556a6a823540409914fb364d9a11bc30d79d485a2eba29af47992d3490e9886c4391c3
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 87bb7ee54f5ecf0ccbfcba0b07473885c43ecd76cb29a8db17d6137a19d9f9cd443a2a7c5fd8a3f24d58ad8145f9eb49116344a66b107e1aeab82cf2383f4753
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 495834a53b0856c02acd40446f7130cb0f8284f4a39afdab20d5dc42b2e198b1196119fe887beed8f9055c4ff2055e3b2f6d4641d0be018cdfb64fedf6fc1aac
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"anymatch@npm:^3.1.3":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"aria-query@npm:^5.3.2":
  version: 5.3.2
  resolution: "aria-query@npm:5.3.2"
  checksum: d971175c85c10df0f6d14adfe6f1292409196114ab3c62f238e208b53103686f46cc70695a4f775b73bc65f6a09b6a092fd963c4f3a5a7d690c8fc5094925717
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1, array-buffer-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-buffer-byte-length@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    is-array-buffer: ^3.0.5
  checksum: 0ae3786195c3211b423e5be8dd93357870e6fb66357d81da968c2c39ef43583ef6eece1f9cb1caccdae4806739c65dea832b44b8593414313cd76a89795fca63
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6, array-includes@npm:^3.1.8, array-includes@npm:^3.1.9":
  version: 3.1.9
  resolution: "array-includes@npm:3.1.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    define-properties: ^1.2.1
    es-abstract: ^1.24.0
    es-object-atoms: ^1.1.1
    get-intrinsic: ^1.3.0
    is-string: ^1.1.1
    math-intrinsics: ^1.1.0
  checksum: b58dc526fe415252e50319eaf88336e06e75aa673e3b58d252414739a4612dbe56e7b613fdcc7c90561dc9cf9202bbe5ca029ccd8c08362746459475ae5a8f3e
  languageName: node
  linkType: hard

"array.prototype.findlast@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlast@npm:1.2.5"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-shim-unscopables: ^1.0.2
  checksum: 83ce4ad95bae07f136d316f5a7c3a5b911ac3296c3476abe60225bc4a17938bf37541972fcc37dd5adbc99cbb9c928c70bbbfc1c1ce549d41a415144030bb446
  languageName: node
  linkType: hard

"array.prototype.findlastindex@npm:^1.2.6":
  version: 1.2.6
  resolution: "array.prototype.findlastindex@npm:1.2.6"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    define-properties: ^1.2.1
    es-abstract: ^1.23.9
    es-errors: ^1.3.0
    es-object-atoms: ^1.1.1
    es-shim-unscopables: ^1.1.0
  checksum: bd2665bd51f674d4e1588ce5d5848a8adb255f414070e8e652585598b801480516df2c6cef2c60b6ea1a9189140411c49157a3f112d52e9eabb4e9fc80936ea6
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1, array.prototype.flat@npm:^1.3.3":
  version: 1.3.3
  resolution: "array.prototype.flat@npm:1.3.3"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-shim-unscopables: ^1.0.2
  checksum: 5d5a7829ab2bb271a8d30a1c91e6271cef0ec534593c0fe6d2fb9ebf8bb62c1e5326e2fddcbbcbbe5872ca04f5e6b54a1ecf092e0af704fb538da9b2bfd95b40
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.2, array.prototype.flatmap@npm:^1.3.3":
  version: 1.3.3
  resolution: "array.prototype.flatmap@npm:1.3.3"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-shim-unscopables: ^1.0.2
  checksum: 11b4de09b1cf008be6031bb507d997ad6f1892e57dc9153583de6ebca0f74ea403fffe0f203461d359de05048d609f3f480d9b46fed4099652d8b62cc972f284
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.4":
  version: 1.1.4
  resolution: "array.prototype.tosorted@npm:1.1.4"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.3
    es-errors: ^1.3.0
    es-shim-unscopables: ^1.0.2
  checksum: e4142d6f556bcbb4f393c02e7dbaea9af8f620c040450c2be137c9cbbd1a17f216b9c688c5f2c08fbb038ab83f55993fa6efdd9a05881d84693c7bcb5422127a
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.4":
  version: 1.0.4
  resolution: "arraybuffer.prototype.slice@npm:1.0.4"
  dependencies:
    array-buffer-byte-length: ^1.0.1
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    is-array-buffer: ^3.0.4
  checksum: b1d1fd20be4e972a3779b1569226f6740170dca10f07aa4421d42cefeec61391e79c557cda8e771f5baefe47d878178cd4438f60916ce831813c08132bced765
  languageName: node
  linkType: hard

"ast-types-flow@npm:^0.0.8":
  version: 0.0.8
  resolution: "ast-types-flow@npm:0.0.8"
  checksum: 0a64706609a179233aac23817837abab614f3548c252a2d3d79ea1e10c74aa28a0846e11f466cf72771b6ed8713abc094dcf8c40c3ec4207da163efa525a94a8
  languageName: node
  linkType: hard

"async-function@npm:^1.0.0":
  version: 1.0.0
  resolution: "async-function@npm:1.0.0"
  checksum: 9102e246d1ed9b37ac36f57f0a6ca55226876553251a31fc80677e71471f463a54c872dc78d5d7f80740c8ba624395cccbe8b60f7b690c4418f487d8e9fd1106
  languageName: node
  linkType: hard

"async-mutex@npm:^0.2.6":
  version: 0.2.6
  resolution: "async-mutex@npm:0.2.6"
  dependencies:
    tslib: ^2.0.0
  checksum: f50102e0c57f6a958528cff7dff13da070897f17107b42274417a7248905b927b6e51c3387f8aed1f5cd6005b0e692d64a83a0789be602e4e7e7da4afe08b889
  languageName: node
  linkType: hard

"atomic-sleep@npm:^1.0.0":
  version: 1.0.0
  resolution: "atomic-sleep@npm:1.0.0"
  checksum: b95275afb2f80732f22f43a60178430c468906a415a7ff18bcd0feeebc8eec3930b51250aeda91a476062a90e07132b43a1794e8d8ffcf9b650e8139be75fa36
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: ^1.0.0
  checksum: 1aa3ffbfe6578276996de660848b6e95669d9a95ad149e3dd0c0cda77db6ee1dbd9d1dd723b65b6d277b882dd0c4b91a654ae9d3cf9e1254b7e93e4908d78fd3
  languageName: node
  linkType: hard

"axe-core@npm:^4.10.0":
  version: 4.10.3
  resolution: "axe-core@npm:4.10.3"
  checksum: e89fa5bcad9216f2de29bbdf95d6211d8c5b1025cbdcf56b6695c18b2e9a1eebd0b997a0141334169f6f062fc68fd39a5b97f86348d9f5be05958eade5c1ec78
  languageName: node
  linkType: hard

"axobject-query@npm:^4.1.0":
  version: 4.1.0
  resolution: "axobject-query@npm:4.1.0"
  checksum: 7d1e87bf0aa7ae7a76cd39ab627b7c48fda3dc40181303d9adce4ba1d5b5ce73b5e5403ee6626ec8e91090448c887294d6144e24b6741a976f5be9347e3ae1df
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base-x@npm:^5.0.0":
  version: 5.0.1
  resolution: "base-x@npm:5.0.1"
  checksum: 6e4f847ef842e0a71c6b6020a6ec482a2a5e727f5a98534dbfd5d5a4e8afbc0d1bdf1fd57174b3f0455d107f10a932c3c7710bec07e2878f80178607f8f605c8
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"big.js@npm:6.2.2":
  version: 6.2.2
  resolution: "big.js@npm:6.2.2"
  checksum: 3659092d155d01338f21a01a46a93aa343d25e83bce55700005a46eec27d90fe56abd3b3edde742f16fbc5fee31b4c572b6821a595c1c180392b60b469fcda54
  languageName: node
  linkType: hard

"bn.js@npm:^5.2.1":
  version: 5.2.2
  resolution: "bn.js@npm:5.2.2"
  checksum: 4384d35fef785c757eb050bc1f13d60dd8e37662ca72392ae6678b35cfa2a2ae8f0494291086294683a7d977609c7878ac3cff08ecca7f74c3ca73f3acbadbe8
  languageName: node
  linkType: hard

"bowser@npm:^2.9.0":
  version: 2.11.0
  resolution: "bowser@npm:2.11.0"
  checksum: 29c3f01f22e703fa6644fc3b684307442df4240b6e10f6cfe1b61c6ca5721073189ca97cdeedb376081148c8518e33b1d818a57f781d70b0b70e1f31fb48814f
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.12
  resolution: "brace-expansion@npm:1.1.12"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: 12cb6d6310629e3048cadb003e1aca4d8c9bb5c67c3c321bafdd7e7a50155de081f78ea3e0ed92ecc75a9015e784f301efc8132383132f4f7904ad1ac529c562
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: ^1.0.0
  checksum: 01dff195e3646bc4b0d27b63d9bab84d2ebc06121ff5013ad6e5356daa5a9d6b60fa26cf73c74797f2dc3fbec112af13578d51f75228c1112b26c790a87b0488
  languageName: node
  linkType: hard

"braces@npm:^3.0.3":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"bs58@npm:6.0.0":
  version: 6.0.0
  resolution: "bs58@npm:6.0.0"
  dependencies:
    base-x: ^5.0.0
  checksum: 820334f9513bba6195136dfc9dfbd1f5aded6c7864639f3ee7b63c2d9d6f9f2813b9949b1f6beb9c161237be2a461097444c2ff587c8c3b824fe18878fa22448
  languageName: node
  linkType: hard

"buffer@npm:6.0.3, buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.2.1
  checksum: 5ad23293d9a731e4318e420025800b42bf0d264004c0286c8cc010af7a270c7a0f6522e84f54b9ad65cbd6db20b8badbfd8d2ebf4f80fa03dab093b89e68c3f9
  languageName: node
  linkType: hard

"bufferutil@npm:^4.0.8":
  version: 4.0.9
  resolution: "bufferutil@npm:4.0.9"
  dependencies:
    node-gyp: latest
    node-gyp-build: ^4.3.0
  checksum: 51ce9ee19bc4b72c2eb9f9a231dd95e786ca5a00a6bdfcae83f1d5cd8169301c79245ce96913066a5a1bbe45c44e95bc5a1761a18798b835585c1a05af65b209
  languageName: node
  linkType: hard

"busboy@npm:1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: ^1.1.0
  checksum: 32801e2c0164e12106bf236291a00795c3c4e4b709ae02132883fe8478ba2ae23743b11c5735a0aae8afe65ac4b6ca4568b91f0d9fed1fdbc32ede824a73746e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": ^4.0.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^7.0.2
    ssri: ^12.0.0
    tar: ^7.4.3
    unique-filename: ^4.0.0
  checksum: e95684717de6881b4cdaa949fa7574e3171946421cd8291769dd3d2417dbf7abf4aa557d1f968cca83dcbc95bed2a281072b09abfc977c942413146ef7ed4525
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.0, call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
  checksum: b2863d74fcf2a6948221f65d95b91b4b2d90cfe8927650b506141e669f7d5de65cea191bf788838bc40d13846b7886c5bc5c84ab96c3adbcf88ad69a72fcdc6b
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.7, call-bind@npm:^1.0.8":
  version: 1.0.8
  resolution: "call-bind@npm:1.0.8"
  dependencies:
    call-bind-apply-helpers: ^1.0.0
    es-define-property: ^1.0.0
    get-intrinsic: ^1.2.4
    set-function-length: ^1.2.2
  checksum: aa2899bce917a5392fd73bd32e71799c37c0b7ab454e0ed13af7f6727549091182aade8bbb7b55f304a5bc436d543241c14090fb8a3137e9875e23f444f4f5a9
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2, call-bound@npm:^1.0.3, call-bound@npm:^1.0.4":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: ^1.0.2
    get-intrinsic: ^1.3.0
  checksum: 2f6399488d1c272f56306ca60ff696575e2b7f31daf23bc11574798c84d9f2759dceb0cb1f471a85b77f28962a7ac6411f51d283ea2e45319009a19b6ccab3b2
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase@npm:^5.0.0":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001579":
  version: 1.0.30001726
  resolution: "caniuse-lite@npm:1.0.30001726"
  checksum: 6148fc2c2f46ae1faf74af932aa768031355e2394164d7ccba0caafd30310c9b4f2c723d19d5ae5fab05a3238d1bed7e98037c84199d29d40768168e848135f5
  languageName: node
  linkType: hard

"cbw-sdk@npm:@coinbase/wallet-sdk@3.9.3":
  version: 3.9.3
  resolution: "@coinbase/wallet-sdk@npm:3.9.3"
  dependencies:
    bn.js: ^5.2.1
    buffer: ^6.0.3
    clsx: ^1.2.1
    eth-block-tracker: ^7.1.0
    eth-json-rpc-filters: ^6.0.0
    eventemitter3: ^5.0.1
    keccak: ^3.0.3
    preact: ^10.16.0
    sha.js: ^2.4.11
  checksum: c3ab1b30facbe43f6d0f7f4010e438f9c488b72f9dad768b60adbb0e4f6b057e7518e3d86c7859fdd15df187ef3f1d6212898eae4694a7d8ed0ceb05ef216eb9
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"chokidar@npm:^4.0.3":
  version: 4.0.3
  resolution: "chokidar@npm:4.0.3"
  dependencies:
    readdirp: ^4.0.1
  checksum: a8765e452bbafd04f3f2fad79f04222dd65f43161488bb6014a41099e6ca18d166af613d59a90771908c1c823efa3f46ba36b86ac50b701c20c1b9908c5fe36e
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: fd73a4bab48b79e66903fe1cafbdc208956f41ea4f856df883d0c7277b7ab29fd33ee65f93b2ec9192fc0169238f2f8307b7735d27c155821d886b84aa97aa8d
  languageName: node
  linkType: hard

"client-only@npm:0.0.1":
  version: 0.0.1
  resolution: "client-only@npm:0.0.1"
  checksum: 0c16bf660dadb90610553c1d8946a7fdfb81d624adea073b8440b7d795d5b5b08beb3c950c6a2cf16279365a3265158a236876d92bce16423c485c322d7dfaf8
  languageName: node
  linkType: hard

"cliui@npm:^6.0.0":
  version: 6.0.0
  resolution: "cliui@npm:6.0.0"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.0
    wrap-ansi: ^6.2.0
  checksum: 4fcfd26d292c9f00238117f39fc797608292ae36bac2168cfee4c85923817d0607fe21b3329a8621e01aedf512c99b7eaa60e363a671ffd378df6649fb48ae42
  languageName: node
  linkType: hard

"clsx@npm:^1.2.1":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 30befca8019b2eb7dbad38cff6266cf543091dae2825c856a62a8ccf2c3ab9c2907c4d12b288b73101196767f66812365400a227581484a05f968b0307cfaf12
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: ^1.0.0
    simple-swizzle: ^0.2.2
  checksum: c13fe7cff7885f603f49105827d621ce87f4571d78ba28ef4a3f1a104304748f620615e6bf065ecd2145d0d9dad83a3553f52bb25ede7239d18e9f81622f1cc5
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: ^2.0.1
    color-string: ^1.9.0
  checksum: 0579629c02c631b426780038da929cca8e8d80a40158b09811a0112a107c62e10e4aad719843b791b1e658ab4e800558f2e87ca4522c8b32349d497ecb6adeb4
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"cookie-es@npm:^1.2.2":
  version: 1.2.2
  resolution: "cookie-es@npm:1.2.2"
  checksum: 099050c30c967c89aa72d1d7984e87b3395f3e709cf148d297f436828ebfcc39033f5374d2efdc46d9b5e3eee50b1d59635432c252e57329fea7f09afeb4d055
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"crc-32@npm:^1.2.0":
  version: 1.2.2
  resolution: "crc-32@npm:1.2.2"
  bin:
    crc32: bin/crc32.njs
  checksum: ad2d0ad0cbd465b75dcaeeff0600f8195b686816ab5f3ba4c6e052a07f728c3e70df2e3ca9fd3d4484dc4ba70586e161ca5a2334ec8bf5a41bf022a6103ff243
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.1.4":
  version: 3.2.0
  resolution: "cross-fetch@npm:3.2.0"
  dependencies:
    node-fetch: ^2.7.0
  checksum: 8ded5ea35f705e81e569e7db244a3f96e05e95996ff51877c89b0c1ec1163c76bb5dad77d0f8fba6bb35a0abacb36403d7271dc586d8b1f636110ee7a8d959fd
  languageName: node
  linkType: hard

"cross-fetch@npm:^4.0.0":
  version: 4.1.0
  resolution: "cross-fetch@npm:4.1.0"
  dependencies:
    node-fetch: ^2.7.0
  checksum: c02fa85d59f83e50dbd769ee472c9cc984060c403ee5ec8654659f61a525c1a655eef1c7a35e365c1a107b4e72d76e786718b673d1cb3c97f61d4644cb0a9f9d
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 8d306efacaf6f3f60e0224c287664093fa9185680b2d195852ba9a863f85d02dcc737094c6e512175f8ee0161f9b87c73c6826034c2422e39de7d6569cf4503b
  languageName: node
  linkType: hard

"crossws@npm:^0.3.4":
  version: 0.3.5
  resolution: "crossws@npm:0.3.5"
  dependencies:
    uncrypto: ^0.1.3
  checksum: ca597f9b07d82c71fdeed033d6691c4ba2523c596089218ba2782fce26365c86d555e4ea9d6726d277a34087eac861b11bd4df30646dcfc1a1c867a174f2bf98
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 8db785cc92d259102725b3c694ec0c823f5619a84741b5c7991b8ad135dfaa66093038a1cc63e03361a6cd28d122be48f2106ae72334e067dd619a51f49eddf7
  languageName: node
  linkType: hard

"damerau-levenshtein@npm:^1.0.8":
  version: 1.0.8
  resolution: "damerau-levenshtein@npm:1.0.8"
  checksum: d240b7757544460ae0586a341a53110ab0a61126570ef2d8c731e3eab3f0cb6e488e2609e6a69b46727635de49be20b071688698744417ff1b6c1d7ccd03e0de
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-buffer@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-data-view: ^1.0.2
  checksum: 1e1cd509c3037ac0f8ba320da3d1f8bf1a9f09b0be09394b5e40781b8cc15ff9834967ba7c9f843a425b34f9fe14ce44cf055af6662c44263424c1eb8d65659b
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.2":
  version: 1.0.2
  resolution: "data-view-byte-length@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-data-view: ^1.0.2
  checksum: 3600c91ced1cfa935f19ef2abae11029e01738de8d229354d3b2a172bf0d7e4ed08ff8f53294b715569fdf72dfeaa96aa7652f479c0f60570878d88e7e8bddf6
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-offset@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: 8dd492cd51d19970876626b5b5169fbb67ca31ec1d1d3238ee6a71820ca8b80cafb141c485999db1ee1ef02f2cc3b99424c5eda8d59e852d9ebb79ab290eb5ee
  languageName: node
  linkType: hard

"date-fns@npm:^2.29.3":
  version: 2.30.0
  resolution: "date-fns@npm:2.30.0"
  dependencies:
    "@babel/runtime": ^7.21.0
  checksum: f7be01523282e9bb06c0cd2693d34f245247a29098527d4420628966a2d9aad154bd0e90a6b1cf66d37adcb769cd108cf8a7bd49d76db0fb119af5cdd13644f4
  languageName: node
  linkType: hard

"dayjs@npm:1.11.13":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: f388db88a6aa93956c1f6121644e783391c7b738b73dbc54485578736565c8931bdfba4bb94e9b1535c6e509c97d5deb918bbe1ae6b34358d994de735055cca9
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4, debug@npm:^4.4.0":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: a43826a01cda685ee4cec00fb2d3322eaa90ccadbef60d9287debc2a886be3e835d9199c80070ede75a409ee57828c4c6cd80e4b154f2843f0dc95a570dc0729
  languageName: node
  linkType: hard

"debug@npm:^3.2.7":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"debug@npm:~4.3.1, debug@npm:~4.3.2":
  version: 4.3.7
  resolution: "debug@npm:4.3.7"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 822d74e209cd910ef0802d261b150314bbcf36c582ccdbb3e70f0894823c17e49a50d3e66d96b633524263975ca16b6a833f3e3b7e030c157169a5fabac63160
  languageName: node
  linkType: hard

"decamelize@npm:^1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: ad8c51a7e7e0720c70ec2eeb1163b66da03e7616d7b98c9ef43cce2416395e84c1e9548dd94f5f6ffecfee9f8b94251fc57121a8b021f2ff2469b2bae247b8aa
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.2":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 95476a7d28f267292ce745eac3524a9079058bbb35767b76e3ee87d42e34cd0275d2eb19d9d08c3e167f97556e8a2872747f5e65cbebcac8b0c98d83e285f139
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.0.1
  checksum: 8068ee6cab694d409ac25936eb861eea704b7763f7f342adbdfe337fc27c78d7ae0eff2364b2917b58c508d723c7a074326d068eef2e45c4edcd85cf94d0313b
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: ^1.0.1
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"defu@npm:^6.1.4":
  version: 6.1.4
  resolution: "defu@npm:6.1.4"
  checksum: 40e3af6338f195ac1564f53d1887fa2d0429ac7e8c081204bc4d29191180059d3952b5f4e08fe5df8d59eb873aa26e9c88b56d4fac699673d4a372c93620b229
  languageName: node
  linkType: hard

"derive-valtio@npm:0.1.0":
  version: 0.1.0
  resolution: "derive-valtio@npm:0.1.0"
  peerDependencies:
    valtio: "*"
  checksum: 5266a53bbf4795c72f76922a19e8737e9ecfcbe738e7814108abc13fe048c8b82c48c40f5727c134681caec6f20a8a9d0713c9e1d33b84f755138e032c2d0bce
  languageName: node
  linkType: hard

"destr@npm:^2.0.3, destr@npm:^2.0.5":
  version: 2.0.5
  resolution: "destr@npm:2.0.5"
  checksum: e6d5b9e922f528527cd98035249b4d34077828debd2be448a33e268ac1f803bd9a53e7cf0f5184ef68a67573b7f0a6033a89913f61eadaf0e180de49b148606e
  languageName: node
  linkType: hard

"detect-browser@npm:5.3.0, detect-browser@npm:^5.2.0":
  version: 5.3.0
  resolution: "detect-browser@npm:5.3.0"
  checksum: dd6e08d55da1d9e0f22510ac79872078ae03d9dfa13c5e66c96baedc1c86567345a88f96949161f6be8f3e0fafa93bf179bdb1cd311b14f5f163112fcc70ab49
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.3, detect-libc@npm:^2.0.4":
  version: 2.0.4
  resolution: "detect-libc@npm:2.0.4"
  checksum: 3d186b7d4e16965e10e21db596c78a4e131f9eee69c0081d13b85e6a61d7448d3ba23fe7997648022bdfa3b0eb4cc3c289a44c8188df949445a20852689abef6
  languageName: node
  linkType: hard

"dijkstrajs@npm:^1.0.1":
  version: 1.0.3
  resolution: "dijkstrajs@npm:1.0.3"
  checksum: 82ff2c6633f235dd5e6bed04ec62cdfb1f327b4d7534557bd52f18991313f864ee50654543072fff4384a92b643ada4d5452f006b7098dbdfad6c8744a8c9e08
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: ^2.0.2
  checksum: a45e277f7feaed309fe658ace1ff286c6e2002ac515af0aaf37145b8baa96e49899638c7cd47dccf84c3d32abfc113246625b3ac8f552d1046072adee13b0dc8
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.0, dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-errors: ^1.3.0
    gopd: ^1.2.0
  checksum: 149207e36f07bd4941921b0ca929e3a28f1da7bd6b6ff8ff7f4e2f2e460675af4576eeba359c635723dc189b64cdd4787e0255897d5b135ccc5d15cb8685fc90
  languageName: node
  linkType: hard

"duplexify@npm:^4.1.2":
  version: 4.1.3
  resolution: "duplexify@npm:4.1.3"
  dependencies:
    end-of-stream: ^1.4.1
    inherits: ^2.0.3
    readable-stream: ^3.1.1
    stream-shift: ^1.0.2
  checksum: 9636a027345de3dd3c801594d01a7c73d9ce260019538beb1ee650bba7544e72f40a4d4902b52e1ab283dc32a06f210d42748773af02ff15e3064a9659deab7f
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"eciesjs@npm:^0.4.11":
  version: 0.4.15
  resolution: "eciesjs@npm:0.4.15"
  dependencies:
    "@ecies/ciphers": ^0.2.3
    "@noble/ciphers": ^1.3.0
    "@noble/curves": ^1.9.1
    "@noble/hashes": ^1.8.0
  checksum: 133a0ded69da0579d7082fe89b6a15225cf5cf503f653438355c2106cfc42576ab75835421f6a1ad099b9b802f389fc0077e72a951fd0e7dbc5c1e5220457ac9
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"encode-utf8@npm:^1.0.3":
  version: 1.0.3
  resolution: "encode-utf8@npm:1.0.3"
  checksum: 550224bf2a104b1d355458c8a82e9b4ea07f9fc78387bc3a49c151b940ad26473de8dc9e121eefc4e84561cb0b46de1e4cd2bc766f72ee145e9ea9541482817f
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0, end-of-stream@npm:^1.4.0, end-of-stream@npm:^1.4.1":
  version: 1.4.5
  resolution: "end-of-stream@npm:1.4.5"
  dependencies:
    once: ^1.4.0
  checksum: 1e0cfa6e7f49887544e03314f9dfc56a8cb6dde910cbb445983ecc2ff426fc05946df9d75d8a21a3a64f2cecfe1bf88f773952029f46756b2ed64a24e95b1fb8
  languageName: node
  linkType: hard

"engine.io-client@npm:~6.6.1":
  version: 6.6.3
  resolution: "engine.io-client@npm:6.6.3"
  dependencies:
    "@socket.io/component-emitter": ~3.1.0
    debug: ~4.3.1
    engine.io-parser: ~5.2.1
    ws: ~8.17.1
    xmlhttprequest-ssl: ~2.1.1
  checksum: 90aee334e8c699ab471d4eebc360afa0d59f763f47fee7ba6eaaf0610819ae46025a5f2205908ef031e82915a0590c481655cc84e429e43afaf8b12617c21221
  languageName: node
  linkType: hard

"engine.io-parser@npm:~5.2.1":
  version: 5.2.3
  resolution: "engine.io-parser@npm:5.2.3"
  checksum: a76d998b794ce8bbcade833064d949715781fdb9e9cf9b33ecf617d16355ddfd7772f12bb63aaec0f497d63266c6db441129c5aa24c60582270f810c696a6cf8
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.18.1":
  version: 5.18.2
  resolution: "enhanced-resolve@npm:5.18.2"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: af8c0f19cc414f69d7595507576db470c7435f550e7aa1d46292c40aaf1fe03c4d714c495bc31aa927f90887faa8880db428c8bca4dc1595844853ab2195ee25
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.5, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.3, es-abstract@npm:^1.23.5, es-abstract@npm:^1.23.6, es-abstract@npm:^1.23.9, es-abstract@npm:^1.24.0":
  version: 1.24.0
  resolution: "es-abstract@npm:1.24.0"
  dependencies:
    array-buffer-byte-length: ^1.0.2
    arraybuffer.prototype.slice: ^1.0.4
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    data-view-buffer: ^1.0.2
    data-view-byte-length: ^1.0.2
    data-view-byte-offset: ^1.0.1
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.1.1
    es-set-tostringtag: ^2.1.0
    es-to-primitive: ^1.3.0
    function.prototype.name: ^1.1.8
    get-intrinsic: ^1.3.0
    get-proto: ^1.0.1
    get-symbol-description: ^1.1.0
    globalthis: ^1.0.4
    gopd: ^1.2.0
    has-property-descriptors: ^1.0.2
    has-proto: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    internal-slot: ^1.1.0
    is-array-buffer: ^3.0.5
    is-callable: ^1.2.7
    is-data-view: ^1.0.2
    is-negative-zero: ^2.0.3
    is-regex: ^1.2.1
    is-set: ^2.0.3
    is-shared-array-buffer: ^1.0.4
    is-string: ^1.1.1
    is-typed-array: ^1.1.15
    is-weakref: ^1.1.1
    math-intrinsics: ^1.1.0
    object-inspect: ^1.13.4
    object-keys: ^1.1.1
    object.assign: ^4.1.7
    own-keys: ^1.0.1
    regexp.prototype.flags: ^1.5.4
    safe-array-concat: ^1.1.3
    safe-push-apply: ^1.0.0
    safe-regex-test: ^1.1.0
    set-proto: ^1.0.0
    stop-iteration-iterator: ^1.1.0
    string.prototype.trim: ^1.2.10
    string.prototype.trimend: ^1.0.9
    string.prototype.trimstart: ^1.0.8
    typed-array-buffer: ^1.0.3
    typed-array-byte-length: ^1.0.3
    typed-array-byte-offset: ^1.0.4
    typed-array-length: ^1.0.7
    unbox-primitive: ^1.1.0
    which-typed-array: ^1.1.19
  checksum: 06b3d605e56e3da9d16d4db2629a42dac1ca31f2961a41d15c860422a266115e865b43e82d6b9da81a0fabbbb65ebc12fb68b0b755bc9dbddacb6bf7450e96df
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0, es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 0512f4e5d564021c9e3a644437b0155af2679d10d80f21adaf868e64d30efdfbd321631956f20f42d655fedb2e3a027da479fad3fa6048f768eb453a80a5f80a
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-iterator-helpers@npm:1.2.1"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-abstract: ^1.23.6
    es-errors: ^1.3.0
    es-set-tostringtag: ^2.0.3
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.6
    globalthis: ^1.0.4
    gopd: ^1.2.0
    has-property-descriptors: ^1.0.2
    has-proto: ^1.2.0
    has-symbols: ^1.1.0
    internal-slot: ^1.1.0
    iterator.prototype: ^1.1.4
    safe-array-concat: ^1.1.3
  checksum: 952808dd1df3643d67ec7adf20c30b36e5eecadfbf36354e6f39ed3266c8e0acf3446ce9bc465e38723d613cb1d915c1c07c140df65bdce85da012a6e7bda62b
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: ^1.3.0
  checksum: 214d3767287b12f36d3d7267ef342bbbe1e89f899cfd67040309fc65032372a8e60201410a99a1645f2f90c1912c8c49c8668066f6bdd954bcd614dda2e3da97
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3, es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 789f35de4be3dc8d11fdcb91bc26af4ae3e6d602caa93299a8c45cf05d36cc5081454ae2a6d3afa09cceca214b76c046e4f8151e092e6fc7feeb5efb9e794fc6
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.2, es-shim-unscopables@npm:^1.1.0":
  version: 1.1.0
  resolution: "es-shim-unscopables@npm:1.1.0"
  dependencies:
    hasown: ^2.0.2
  checksum: 33cfb1ebcb2f869f0bf528be1a8660b4fe8b6cec8fc641f330e508db2284b58ee2980fad6d0828882d22858c759c0806076427a3673b6daa60f753e3b558ee15
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-to-primitive@npm:1.3.0"
  dependencies:
    is-callable: ^1.2.7
    is-date-object: ^1.0.5
    is-symbol: ^1.0.4
  checksum: 966965880356486cd4d1fe9a523deda2084c81b3702d951212c098f5f2ee93605d1b7c1840062efb48a07d892641c7ed1bc194db563645c0dd2b919cb6d65b93
  languageName: node
  linkType: hard

"es-toolkit@npm:1.33.0":
  version: 1.33.0
  resolution: "es-toolkit@npm:1.33.0"
  dependenciesMeta:
    "@trivago/prettier-plugin-sort-imports@4.3.0":
      unplugged: true
    prettier-plugin-sort-re-exports@0.0.1:
      unplugged: true
  checksum: 5b5bb168f9c0f46a74105d7608f441718a85f8e8fb4aae97688b6c0c37e0830b340b4f4598e861084aa3ad67c0eeb2c5ba50dc8c778268693ce75b6ad41c8b1d
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-config-next@npm:15.3.4":
  version: 15.3.4
  resolution: "eslint-config-next@npm:15.3.4"
  dependencies:
    "@next/eslint-plugin-next": 15.3.4
    "@rushstack/eslint-patch": ^1.10.3
    "@typescript-eslint/eslint-plugin": ^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0
    "@typescript-eslint/parser": ^5.4.2 || ^6.0.0 || ^7.0.0 || ^8.0.0
    eslint-import-resolver-node: ^0.3.6
    eslint-import-resolver-typescript: ^3.5.2
    eslint-plugin-import: ^2.31.0
    eslint-plugin-jsx-a11y: ^6.10.0
    eslint-plugin-react: ^7.37.0
    eslint-plugin-react-hooks: ^5.0.0
  peerDependencies:
    eslint: ^7.23.0 || ^8.0.0 || ^9.0.0
    typescript: ">=3.3.1"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 6c21254d3383b9158ff5f3b2881cc702bee3d2635b4326757965945691f6e65e25fdfef4f2964382fb4b2f52d9f03b929cb71d267709727df7365e7da80c8c3a
  languageName: node
  linkType: hard

"eslint-import-resolver-node@npm:^0.3.6, eslint-import-resolver-node@npm:^0.3.9":
  version: 0.3.9
  resolution: "eslint-import-resolver-node@npm:0.3.9"
  dependencies:
    debug: ^3.2.7
    is-core-module: ^2.13.0
    resolve: ^1.22.4
  checksum: 439b91271236b452d478d0522a44482e8c8540bf9df9bd744062ebb89ab45727a3acd03366a6ba2bdbcde8f9f718bab7fe8db64688aca75acf37e04eafd25e22
  languageName: node
  linkType: hard

"eslint-import-resolver-typescript@npm:^3.5.2":
  version: 3.10.1
  resolution: "eslint-import-resolver-typescript@npm:3.10.1"
  dependencies:
    "@nolyfill/is-core-module": 1.0.39
    debug: ^4.4.0
    get-tsconfig: ^4.10.0
    is-bun-module: ^2.0.0
    stable-hash: ^0.0.5
    tinyglobby: ^0.2.13
    unrs-resolver: ^1.6.2
  peerDependencies:
    eslint: "*"
    eslint-plugin-import: "*"
    eslint-plugin-import-x: "*"
  peerDependenciesMeta:
    eslint-plugin-import:
      optional: true
    eslint-plugin-import-x:
      optional: true
  checksum: 57acb58fe28257024236b52ebfe6a3d2e3970a88002e02e771ff327c850c76b2a6b90175b54a980e9efe4787ac09bafe53cb3ebabf3fd165d3ff2a80b2d7e50d
  languageName: node
  linkType: hard

"eslint-module-utils@npm:^2.12.1":
  version: 2.12.1
  resolution: "eslint-module-utils@npm:2.12.1"
  dependencies:
    debug: ^3.2.7
  peerDependenciesMeta:
    eslint:
      optional: true
  checksum: 2f074670d8c934687820a83140048776b28bbaf35fc37f35623f63cc9c438d496d11f0683b4feabb9a120435435d4a69604b1c6c567f118be2c9a0aba6760fc1
  languageName: node
  linkType: hard

"eslint-plugin-import@npm:^2.31.0":
  version: 2.32.0
  resolution: "eslint-plugin-import@npm:2.32.0"
  dependencies:
    "@rtsao/scc": ^1.1.0
    array-includes: ^3.1.9
    array.prototype.findlastindex: ^1.2.6
    array.prototype.flat: ^1.3.3
    array.prototype.flatmap: ^1.3.3
    debug: ^3.2.7
    doctrine: ^2.1.0
    eslint-import-resolver-node: ^0.3.9
    eslint-module-utils: ^2.12.1
    hasown: ^2.0.2
    is-core-module: ^2.16.1
    is-glob: ^4.0.3
    minimatch: ^3.1.2
    object.fromentries: ^2.0.8
    object.groupby: ^1.0.3
    object.values: ^1.2.1
    semver: ^6.3.1
    string.prototype.trimend: ^1.0.9
    tsconfig-paths: ^3.15.0
  peerDependencies:
    eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
  checksum: 8cd40595b5e4346d3698eb577014b4b6d0ba57b7b9edf975be4f052a89330ec202d0cc5c3861d37ebeafa151b6264821410243889b0c31710911a6b625bcf76b
  languageName: node
  linkType: hard

"eslint-plugin-jsx-a11y@npm:^6.10.0":
  version: 6.10.2
  resolution: "eslint-plugin-jsx-a11y@npm:6.10.2"
  dependencies:
    aria-query: ^5.3.2
    array-includes: ^3.1.8
    array.prototype.flatmap: ^1.3.2
    ast-types-flow: ^0.0.8
    axe-core: ^4.10.0
    axobject-query: ^4.1.0
    damerau-levenshtein: ^1.0.8
    emoji-regex: ^9.2.2
    hasown: ^2.0.2
    jsx-ast-utils: ^3.3.5
    language-tags: ^1.0.9
    minimatch: ^3.1.2
    object.fromentries: ^2.0.8
    safe-regex-test: ^1.0.3
    string.prototype.includes: ^2.0.1
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9
  checksum: 0cc861398fa26ada61ed5703eef5b335495fcb96253263dcd5e399488ff019a2636372021baacc040e3560d1a34bfcd5d5ad9f1754f44cd0509c956f7df94050
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^5.0.0":
  version: 5.2.0
  resolution: "eslint-plugin-react-hooks@npm:5.2.0"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0
  checksum: 5920736a78c0075488e7e30e04fbe5dba5b6b5a6c8c4b5742fdae6f9b8adf4ee387bc45dc6e03b4012865e6fd39d134da7b83a40f57c90cc9eecf80692824e3a
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:^7.37.0":
  version: 7.37.5
  resolution: "eslint-plugin-react@npm:7.37.5"
  dependencies:
    array-includes: ^3.1.8
    array.prototype.findlast: ^1.2.5
    array.prototype.flatmap: ^1.3.3
    array.prototype.tosorted: ^1.1.4
    doctrine: ^2.1.0
    es-iterator-helpers: ^1.2.1
    estraverse: ^5.3.0
    hasown: ^2.0.2
    jsx-ast-utils: ^2.4.1 || ^3.0.0
    minimatch: ^3.1.2
    object.entries: ^1.1.9
    object.fromentries: ^2.0.8
    object.values: ^1.2.1
    prop-types: ^15.8.1
    resolve: ^2.0.0-next.5
    semver: ^6.3.1
    string.prototype.matchall: ^4.0.12
    string.prototype.repeat: ^1.0.0
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
  checksum: 8675e7558e646e3c2fcb04bb60cfe416000b831ef0b363f0117838f5bfc799156113cb06058ad4d4b39fc730903b7360b05038da11093064ca37caf76b7cf2ca
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.4.0":
  version: 8.4.0
  resolution: "eslint-scope@npm:8.4.0"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: cf88f42cd5e81490d549dc6d350fe01e6fe420f9d9ea34f134bb359b030e3c4ef888d36667632e448937fe52449f7181501df48c08200e3d3b0fee250d05364e
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.2.1":
  version: 4.2.1
  resolution: "eslint-visitor-keys@npm:4.2.1"
  checksum: 3a77e3f99a49109f6fb2c5b7784bc78f9743b834d238cdba4d66c602c6b52f19ed7bcd0a5c5dbbeae3a8689fd785e76c001799f53d2228b278282cf9f699fff5
  languageName: node
  linkType: hard

"eslint@npm:^9":
  version: 9.30.0
  resolution: "eslint@npm:9.30.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.12.1
    "@eslint/config-array": ^0.21.0
    "@eslint/config-helpers": ^0.3.0
    "@eslint/core": ^0.14.0
    "@eslint/eslintrc": ^3.3.1
    "@eslint/js": 9.30.0
    "@eslint/plugin-kit": ^0.3.1
    "@humanfs/node": ^0.16.6
    "@humanwhocodes/module-importer": ^1.0.1
    "@humanwhocodes/retry": ^0.4.2
    "@types/estree": ^1.0.6
    "@types/json-schema": ^7.0.15
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.6
    debug: ^4.3.2
    escape-string-regexp: ^4.0.0
    eslint-scope: ^8.4.0
    eslint-visitor-keys: ^4.2.1
    espree: ^10.4.0
    esquery: ^1.5.0
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^8.0.0
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    json-stable-stringify-without-jsonify: ^1.0.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
  peerDependencies:
    jiti: "*"
  peerDependenciesMeta:
    jiti:
      optional: true
  bin:
    eslint: bin/eslint.js
  checksum: 19f4beb78f8f12dbb6960fbf767d775716c729db2a982c8043fbdbbb425e052252aae22a8f2d473de52c59c18c26532be1db46361b28398d762ad9bee6c9c202
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.4.0":
  version: 10.4.0
  resolution: "espree@npm:10.4.0"
  dependencies:
    acorn: ^8.15.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^4.2.1
  checksum: 5f9d0d7c81c1bca4bfd29a55270067ff9d575adb8c729a5d7f779c2c7b910bfc68ccf8ec19b29844b707440fc159a83868f22c8e87bbf7cbcb225ed067df6c85
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: 08ec4fe446d9ab27186da274d979558557fbdbbd10968fa9758552482720c54152a5640e08b9009e5a30706b66aba510692054d4129d32d0e12e05bbc0b96fb2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"eth-block-tracker@npm:^7.1.0":
  version: 7.1.0
  resolution: "eth-block-tracker@npm:7.1.0"
  dependencies:
    "@metamask/eth-json-rpc-provider": ^1.0.0
    "@metamask/safe-event-emitter": ^3.0.0
    "@metamask/utils": ^5.0.1
    json-rpc-random-id: ^1.0.1
    pify: ^3.0.0
  checksum: 1d019f261e0ef07387cd74538b160700caa35ba9859ab9d4e5137c48bf9c92822c3b4ade40f8a504f16cb813de4c317c5378d047625ddf04592e256be8842588
  languageName: node
  linkType: hard

"eth-json-rpc-filters@npm:^6.0.0":
  version: 6.0.1
  resolution: "eth-json-rpc-filters@npm:6.0.1"
  dependencies:
    "@metamask/safe-event-emitter": ^3.0.0
    async-mutex: ^0.2.6
    eth-query: ^2.1.2
    json-rpc-engine: ^6.1.0
    pify: ^5.0.0
  checksum: 216f7417417599a48273b08fb2894581175276fe21cb1c9ffa66e98a9c2a67bc0ac821ad2ca163fdb8e8de0960aea0d9c5e53aee9d5dcfec355abf020e9458c5
  languageName: node
  linkType: hard

"eth-query@npm:^2.1.2":
  version: 2.1.2
  resolution: "eth-query@npm:2.1.2"
  dependencies:
    json-rpc-random-id: ^1.0.0
    xtend: ^4.0.1
  checksum: 83daa0e28452c54722aec78cd24d036bad5b6e7c08035d98e10d4bea11f71662f12cab63ebd8a848d4df46ad316503d54ecccb41c9244d2ea8b29364b0a20201
  languageName: node
  linkType: hard

"eth-rpc-errors@npm:^4.0.2, eth-rpc-errors@npm:^4.0.3":
  version: 4.0.3
  resolution: "eth-rpc-errors@npm:4.0.3"
  dependencies:
    fast-safe-stringify: ^2.0.6
  checksum: 5fa31d1a10fdb340733b9a55e38e7687222c501052ca20743cef4d0c911a9bbcc0cad54aa6bf3e4b428604c071ff519803060e1cbc79ddb7c9257c11d407d32a
  languageName: node
  linkType: hard

"ethereum-cryptography@npm:^2.0.0":
  version: 2.2.1
  resolution: "ethereum-cryptography@npm:2.2.1"
  dependencies:
    "@noble/curves": 1.4.2
    "@noble/hashes": 1.4.0
    "@scure/bip32": 1.4.0
    "@scure/bip39": 1.3.0
  checksum: 1466e4c417b315a6ac67f95088b769fafac8902b495aada3c6375d827e5a7882f9e0eea5f5451600d2250283d9198b8a3d4d996e374e07a80a324e29136f25c6
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 1ffe3bb22a6d51bdeb6bf6f7cf97d2ff4a74b017ad12284cc9e6a279e727dc30a5de6bb613e5596ff4dc3e517841339ad09a7eec44266eccb1aa201a30448166
  languageName: node
  linkType: hard

"eventemitter2@npm:^6.4.9":
  version: 6.4.9
  resolution: "eventemitter2@npm:6.4.9"
  checksum: be59577c1e1c35509c7ba0e2624335c35bbcfd9485b8a977384c6cc6759341ea1a98d3cb9dbaa5cea4fff9b687e504504e3f9c2cc1674cf3bd8a43a7c74ea3eb
  languageName: node
  linkType: hard

"eventemitter3@npm:5.0.1, eventemitter3@npm:^5.0.1":
  version: 5.0.1
  resolution: "eventemitter3@npm:5.0.1"
  checksum: 543d6c858ab699303c3c32e0f0f47fc64d360bf73c3daf0ac0b5079710e340d6fe9f15487f94e66c629f5f82cd1a8678d692f3dbb6f6fcd1190e1b97fcad36f8
  languageName: node
  linkType: hard

"events@npm:3.3.0, events@npm:^3.3.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: f6f487ad2198aa41d878fa31452f1a3c00958f46e9019286ff4787c84aac329332ab45c9cdc8c445928fc6d7ded294b9e005a7fce9426488518017831b272780
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 7e191e3dd6edd8c56c88f2c8037c98fbb8034fe48778be53ed8cb30ccef371a061a4e999a469aab939b92f8f12698f3b426d52f4f76b7a20da5f9f98c3cbc862
  languageName: node
  linkType: hard

"extension-port-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "extension-port-stream@npm:3.0.0"
  dependencies:
    readable-stream: ^3.6.2 || ^4.4.2
    webextension-polyfill: ">=0.10.0 <1.0"
  checksum: 4f51d2258a96154c2d916a8a5425636a2b0817763e9277f7dc378d08b6f050c90d185dbde4313d27cf66ad99d4b3116479f9f699c40358c64cccfa524d2b55bf
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:3.3.1":
  version: 3.3.1
  resolution: "fast-glob@npm:3.3.1"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: b6f3add6403e02cf3a798bfbb1183d0f6da2afd368f27456010c0bc1f9640aea308243d4cb2c0ab142f618276e65ecb8be1661d7c62a7b4e5ba774b9ce5432e5
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.8
  checksum: 0704d7b85c0305fd2cef37777337dfa26230fdd072dce9fb5c82a4b03156f3ffb8ed3e636033e65d45d2a5805a4e475825369a27404c0307f2db0c8eb3366fbd
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fast-redact@npm:^3.0.0":
  version: 3.5.0
  resolution: "fast-redact@npm:3.5.0"
  checksum: ef03f0d1849da074a520a531ad299bf346417b790a643931ab4e01cb72275c8d55b60dc8512fb1f1818647b696790edefaa96704228db9f012da935faa1940af
  languageName: node
  linkType: hard

"fast-safe-stringify@npm:^2.0.6":
  version: 2.1.1
  resolution: "fast-safe-stringify@npm:2.1.1"
  checksum: a851cbddc451745662f8f00ddb622d6766f9bd97642dabfd9a405fb0d646d69fc0b9a1243cbf67f5f18a39f40f6fa821737651ff1bceeba06c9992ca2dc5bd3d
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: ^1.0.4
  checksum: 7691d1794fb84ad0ec2a185f10e00f0e1713b894e2c9c4d42f0bc0ba5f8c00e6e655a202074ca0b91b9c3d977aab7c30c41a8dc069fb5368576ac0054870a0e6
  languageName: node
  linkType: hard

"fdir@npm:^6.4.4":
  version: 6.4.6
  resolution: "fdir@npm:6.4.6"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: fe9f3014901d023cf631831dcb9eae5447f4d7f69218001dd01ecf007eccc40f6c129a04411b5cc273a5f93c14e02e971e17270afc9022041c80be924091eb6f
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: ^4.0.0
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"filter-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "filter-obj@npm:1.1.0"
  checksum: cf2104a7c45ff48e7f505b78a3991c8f7f30f28bd8106ef582721f321f1c6277f7751aacd5d83026cb079d9d5091082f588d14a72e7c5d720ece79118fa61e10
  languageName: node
  linkType: hard

"find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: ^3.2.9
    keyv: ^4.5.4
  checksum: 899fc86bf6df093547d76e7bfaeb900824b869d7d457d02e9b8aae24836f0a99fbad79328cfd6415ee8908f180699bf259dc7614f793447cb14f707caf5996f6
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.3
  resolution: "flatted@npm:3.3.3"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3, for-each@npm:^0.3.5":
  version: 0.3.5
  resolution: "for-each@npm:0.3.5"
  dependencies:
    is-callable: ^1.2.7
  checksum: 3c986d7e11f4381237cc98baa0a2f87eabe74719eee65ed7bed275163082b940ede19268c61d04c6260e0215983b12f8d885e3c8f9aa8c2113bf07c37051745c
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: ^7.0.6
    signal-exit: ^4.0.1
  checksum: b2c1a6fc0bf0233d645d9fefdfa999abf37db1b33e5dab172b3cbfb0662b88bfbd2c9e7ab853533d199050ec6b65c03fcf078fc212d26e4990220e98c6930eef
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6, function.prototype.name@npm:^1.1.8":
  version: 1.1.8
  resolution: "function.prototype.name@npm:1.1.8"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    functions-have-names: ^1.2.3
    hasown: ^2.0.2
    is-callable: ^1.2.7
  checksum: 3a366535dc08b25f40a322efefa83b2da3cd0f6da41db7775f2339679120ef63b6c7e967266182609e655b8f0a8f65596ed21c7fd72ad8bd5621c2340edd4010
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: c3f1f5ba20f4e962efb71344ce0a40722163e85bee2101ce25f88214e78182d2d2476aa85ef37950c579eb6cf6ee811c17b3101bb84004bb75655f3e33f3fdb5
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.1":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.4, get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.2.7, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: ^1.0.2
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.1.1
    function-bind: ^1.1.2
    get-proto: ^1.0.1
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    math-intrinsics: ^1.1.0
  checksum: 301008e4482bb9a9cb49e132b88fee093bff373b4e6def8ba219b1e96b60158a6084f273ef5cafe832e42cd93462f4accb46a618d35fe59a2b507f2388c5b79d
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0, get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: ^1.0.1
    es-object-atoms: ^1.0.0
  checksum: 4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.1.0":
  version: 1.1.0
  resolution: "get-symbol-description@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
  checksum: 655ed04db48ee65ef2ddbe096540d4405e79ba0a7f54225775fef43a7e2afcb93a77d141c5f05fdef0afce2eb93bcbfb3597142189d562ac167ff183582683cd
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.10.0":
  version: 4.10.1
  resolution: "get-tsconfig@npm:4.10.1"
  dependencies:
    resolve-pkg-maps: ^1.0.0
  checksum: 22925debda6bd0992171a44ee79a22c32642063ba79534372c4d744e0c9154abe2c031659da0fb86bc9e73fc56a3b76b053ea5d24ca3ac3da43d2e6f7d1c3c33
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:^10.2.2":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 534b8216736a5425737f59f6e6a5c7f386254560c9f41d24a9227d60ee3ad4a9e82c5b85def0e212e9d92162f83a92544be4c7fd4c902cb913736c10e08237ac
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.4":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: ^1.2.1
    gopd: ^1.0.1
  checksum: 39ad667ad9f01476474633a1834a70842041f70a55571e8dcef5fb957980a92da5022db5430fca8aecc5d47704ae30618c0bc877a579c70710c904e9ef06108a
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1, gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: cc6d8e655e360955bdccaca51a12a474268f95bb793fc3e1f2bdadb075f28bfd1fd988dab872daf77a61d78cbaf13744bc8727a17cfb1d150d76047d805375f3
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"h3@npm:^1.15.2":
  version: 1.15.3
  resolution: "h3@npm:1.15.3"
  dependencies:
    cookie-es: ^1.2.2
    crossws: ^0.3.4
    defu: ^6.1.4
    destr: ^2.0.5
    iron-webcrypto: ^1.2.1
    node-mock-http: ^1.0.0
    radix3: ^1.1.2
    ufo: ^1.6.1
    uncrypto: ^0.1.3
  checksum: 23bd309aed995e64f5cb69333d24e628b6516b7d86cae504be486437d229beaecd03a296c1294740d33d6e9fd1c2c1423268783eea6228f87b18f6c4cd6d933e
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.2":
  version: 1.1.0
  resolution: "has-bigints@npm:1.1.0"
  checksum: 79730518ae02c77e4af6a1d1a0b6a2c3e1509785532771f9baf0241e83e36329542c3d7a0e723df8cbc85f74eff4f177828a2265a01ba576adbdc2d40d86538b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: ^1.0.0
  checksum: fcbb246ea2838058be39887935231c6d5788babed499d0e9d0cc5737494c48aba4fe17ba1449e0d0fbbb1e36175442faa37f9c427ae357d6ccb1d895fbcd3de3
  languageName: node
  linkType: hard

"has-proto@npm:^1.2.0":
  version: 1.2.0
  resolution: "has-proto@npm:1.2.0"
  dependencies:
    dunder-proto: ^1.0.0
  checksum: f55010cb94caa56308041d77967c72a02ffd71386b23f9afa8447e58bc92d49d15c19bf75173713468e92fe3fb1680b03b115da39c21c32c74886d1d50d3e7ff
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: b2316c7302a0e8ba3aaba215f834e96c22c86f192e7310bdf689dd0e6999510c89b00fbc5742571507cebf25764d68c988b3a0da217369a73596191ac0ce694b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: 999d60bb753ad714356b2c6c87b7fb74f32463b8426e159397da4bde5bca7e598ab1073f4d8d4deafac297f2eb311484cd177af242776bf05f0d11565680468d
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 7a7246ddfce629f96832791176fd643589d954e6f3b49548dadb4290451961237fab8fcea41cd2008fe819d95b41c1e8b97f47d088afc0a1c81705287b4ddbcc
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: ^7.1.2
    debug: 4
  checksum: b882377a120aa0544846172e5db021fa8afbf83fea2a897d397bd2ddd8095ab268c24bc462f40a15f2a8c600bf4aa05ce52927f70038d4014e68aefecfa94e8d
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"idb-keyval@npm:^6.2.1":
  version: 6.2.2
  resolution: "idb-keyval@npm:6.2.2"
  checksum: 5ac99363d346dd849fda82057301b95c8a79d4d8f5435f84fb921c6f5f471d6a2bdf89599bbe075ea3466612d0a58b787a1698ef5e8f1fbb9a5d629be96523fa
  languageName: node
  linkType: hard

"ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"ignore@npm:^5.2.0":
  version: 5.3.2
  resolution: "ignore@npm:5.3.2"
  checksum: 2acfd32a573260ea522ea0bfeff880af426d68f6831f973129e2ba7363f422923cf53aab62f8369cbf4667c7b25b6f8a3761b34ecdb284ea18e87a5262a865be
  languageName: node
  linkType: hard

"ignore@npm:^7.0.0":
  version: 7.0.5
  resolution: "ignore@npm:7.0.5"
  checksum: d0862bf64d3d58bf34d5fb0a9f725bec9ca5ce8cd1aecc8f28034269e8f69b8009ffd79ca3eda96962a6a444687781cd5efdb8c7c8ddc0a6996e36d31c217f14
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1":
  version: 3.3.1
  resolution: "import-fresh@npm:3.3.1"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: a06b19461b4879cc654d46f8a6244eb55eb053437afd4cbb6613cad6be203811849ed3e4ea038783092879487299fda24af932b86bdfff67c9055ba3612b8c87
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"internal-slot@npm:^1.1.0":
  version: 1.1.0
  resolution: "internal-slot@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    hasown: ^2.0.2
    side-channel: ^1.1.0
  checksum: 8e0991c2d048cc08dab0a91f573c99f6a4215075887517ea4fa32203ce8aea60fa03f95b177977fa27eb502e5168366d0f3e02c762b799691411d49900611861
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"iron-webcrypto@npm:^1.2.1":
  version: 1.2.1
  resolution: "iron-webcrypto@npm:1.2.1"
  checksum: b158d1893c8d037c11a7dcfd1998b519f31f979643c2c505c6eb1170fd63553498a58b05947d5dea116975df8f12ede5ca235cb68e4c1f404fa6695e4508c60c
  languageName: node
  linkType: hard

"is-arguments@npm:^1.0.4":
  version: 1.2.0
  resolution: "is-arguments@npm:1.2.0"
  dependencies:
    call-bound: ^1.0.2
    has-tostringtag: ^1.0.2
  checksum: aae9307fedfe2e5be14aebd0f48a9eeedf6b8c8f5a0b66257b965146d1e94abdc3f08e3dce3b1d908e1fa23c70039a88810ee1d753905758b9b6eebbab0bafeb
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4, is-array-buffer@npm:^3.0.5":
  version: 3.0.5
  resolution: "is-array-buffer@npm:3.0.5"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    get-intrinsic: ^1.2.6
  checksum: f137a2a6e77af682cdbffef1e633c140cf596f72321baf8bba0f4ef22685eb4339dde23dfe9e9ca430b5f961dee4d46577dcf12b792b68518c8449b134fb9156
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 977e64f54d91c8f169b59afcd80ff19227e9f5c791fa28fa2e5bce355cbaf6c2c356711b734656e80c9dd4a854dd7efcf7894402f1031dfc5de5d620775b4d5f
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.1.1
  resolution: "is-async-function@npm:2.1.1"
  dependencies:
    async-function: ^1.0.0
    call-bound: ^1.0.3
    get-proto: ^1.0.1
    has-tostringtag: ^1.0.2
    safe-regex-test: ^1.1.0
  checksum: 9bece45133da26636488ca127d7686b85ad3ca18927e2850cff1937a650059e90be1c71a48623f8791646bb7a241b0cabf602a0b9252dcfa5ab273f2399000e6
  languageName: node
  linkType: hard

"is-bigint@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-bigint@npm:1.1.0"
  dependencies:
    has-bigints: ^1.0.2
  checksum: ee1544f0e664f253306786ed1dce494b8cf242ef415d6375d8545b4d8816b0f054bd9f948a8988ae2c6325d1c28260dd02978236b2f7b8fb70dfc4838a6c9fa7
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.2.1":
  version: 1.2.2
  resolution: "is-boolean-object@npm:1.2.2"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 0415b181e8f1bfd5d3f8a20f8108e64d372a72131674eea9c2923f39d065b6ad08d654765553bdbffbd92c3746f1007986c34087db1bd89a31f71be8359ccdaa
  languageName: node
  linkType: hard

"is-bun-module@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-bun-module@npm:2.0.0"
  dependencies:
    semver: ^7.7.1
  checksum: e75bd87cb1aaff7c97cf085509669559a713f741a43b4fd5979cb44c5c0c16c05670ce5f23fc22337d1379211fac118c525c5ed73544076ddaf181c1c21ace35
  languageName: node
  linkType: hard

"is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0, is-core-module@npm:^2.16.0, is-core-module@npm:^2.16.1":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: ^2.0.2
  checksum: 6ec5b3c42d9cbf1ac23f164b16b8a140c3cec338bf8f884c076ca89950c7cc04c33e78f02b8cae7ff4751f3247e3174b2330f1fe4de194c7210deb8b1ea316a7
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1, is-data-view@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-data-view@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    is-typed-array: ^1.1.13
  checksum: 31600dd19932eae7fd304567e465709ffbfa17fa236427c9c864148e1b54eb2146357fcf3aed9b686dee13c217e1bb5a649cb3b9c479e1004c0648e9febde1b2
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.5, is-date-object@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-date-object@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    has-tostringtag: ^1.0.2
  checksum: d6c36ab9d20971d65f3fc64cef940d57a4900a2ac85fb488a46d164c2072a33da1cb51eefcc039e3e5c208acbce343d3480b84ab5ff0983f617512da2742562a
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.1.0":
  version: 1.1.1
  resolution: "is-finalizationregistry@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
  checksum: 38c646c506e64ead41a36c182d91639833311970b6b6c6268634f109eef0a1a9d2f1f2e499ef4cb43c744a13443c4cdd2f0812d5afdcee5e9b65b72b28c48557
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10, is-generator-function@npm:^1.0.7":
  version: 1.1.0
  resolution: "is-generator-function@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    get-proto: ^1.0.0
    has-tostringtag: ^1.0.2
    safe-regex-test: ^1.1.0
  checksum: f7f7276131bdf7e28169b86ac55a5b080012a597f9d85a0cbef6fe202a7133fa450a3b453e394870e3cb3685c5a764c64a9f12f614684b46969b1e6f297bed6b
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: e6ce5f6380f32b141b3153e6ba9074892bbbbd655e92e7ba5ff195239777e767a976dcd4e22f864accaf30e53ebf961ab1995424aef91af68788f0591b7396cc
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: c1e6b23d2070c0539d7b36022d5a94407132411d01aba39ec549af824231f3804b1aea90b5e4e58e807a65d23ceb538ed6e355ce76b267bdd86edb757ffcbdcd
  languageName: node
  linkType: hard

"is-number-object@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-number-object@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 6517f0a0e8c4b197a21afb45cd3053dc711e79d45d8878aa3565de38d0102b130ca8732485122c7b336e98c27dacd5236854e3e6526e0eb30cae64956535662f
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-regex@npm:^1.2.1":
  version: 1.2.1
  resolution: "is-regex@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 99ee0b6d30ef1bb61fa4b22fae7056c6c9b3c693803c0c284ff7a8570f83075a7d38cda53b06b7996d441215c27895ea5d1af62124562e13d91b3dbec41a5e13
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 36e3f8c44bdbe9496c9689762cc4110f6a6a12b767c5d74c0398176aa2678d4467e3bf07595556f2dba897751bde1422480212b97d973c7b08a343100b0c0dfe
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.4":
  version: 1.0.4
  resolution: "is-shared-array-buffer@npm:1.0.4"
  dependencies:
    call-bound: ^1.0.3
  checksum: 1611fedc175796eebb88f4dfc393dd969a4a8e6c69cadaff424ee9d4464f9f026399a5f84a90f7c62d6d7ee04e3626a912149726de102b0bd6c1ee6a9868fa5a
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-string@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-string@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
    has-tostringtag: ^1.0.2
  checksum: 2eeaaff605250f5e836ea3500d33d1a5d3aa98d008641d9d42fb941e929ffd25972326c2ef912987e54c95b6f10416281aaf1b35cdf81992cfb7524c5de8e193
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.4, is-symbol@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-symbol@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.2
    has-symbols: ^1.1.0
    safe-regex-test: ^1.1.0
  checksum: bfafacf037af6f3c9d68820b74be4ae8a736a658a3344072df9642a090016e281797ba8edbeb1c83425879aae55d1cb1f30b38bf132d703692b2570367358032
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13, is-typed-array@npm:^1.1.14, is-typed-array@npm:^1.1.15, is-typed-array@npm:^1.1.3":
  version: 1.1.15
  resolution: "is-typed-array@npm:1.1.15"
  dependencies:
    which-typed-array: ^1.1.16
  checksum: ea7cfc46c282f805d19a9ab2084fd4542fed99219ee9dbfbc26284728bd713a51eac66daa74eca00ae0a43b61322920ba334793607dc39907465913e921e0892
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: f36aef758b46990e0d3c37269619c0a08c5b29428c0bb11ecba7f75203442d6c7801239c2f31314bc79199217ef08263787f3837d9e22610ad1da62970d6616d
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2, is-weakref@npm:^1.1.1":
  version: 1.1.1
  resolution: "is-weakref@npm:1.1.1"
  dependencies:
    call-bound: ^1.0.3
  checksum: 1769b9aed5d435a3a989ffc18fc4ad1947d2acdaf530eb2bd6af844861b545047ea51102f75901f89043bed0267ed61d914ee21e6e8b9aa734ec201cdfc0726f
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.4
  resolution: "is-weakset@npm:2.0.4"
  dependencies:
    call-bound: ^1.0.3
    get-intrinsic: ^1.2.6
  checksum: 5c6c8415a06065d78bdd5e3a771483aa1cd928df19138aa73c4c51333226f203f22117b4325df55cc8b3085a6716870a320c2d757efee92d7a7091a039082041
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"isows@npm:1.0.6":
  version: 1.0.6
  resolution: "isows@npm:1.0.6"
  peerDependencies:
    ws: "*"
  checksum: ab9e85b50bcc3d70aa5ec875aa2746c5daf9321cb376ed4e5434d3c2643c5d62b1f466d93a05cd2ad0ead5297224922748c31707cb4fbd68f5d05d0479dce99c
  languageName: node
  linkType: hard

"isows@npm:1.0.7":
  version: 1.0.7
  resolution: "isows@npm:1.0.7"
  peerDependencies:
    ws: "*"
  checksum: 044b949b369872882af07b60b613b5801ae01b01a23b5b72b78af80c8103bbeed38352c3e8ceff13a7834bc91fd2eb41cf91ec01d59a041d8705680e6b0ec546
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.4":
  version: 1.1.5
  resolution: "iterator.prototype@npm:1.1.5"
  dependencies:
    define-data-property: ^1.1.4
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.6
    get-proto: ^1.0.0
    has-symbols: ^1.1.0
    set-function-name: ^2.0.2
  checksum: 7db23c42629ba4790e6e15f78b555f41dbd08818c85af306988364bd19d86716a1187cb333444f3a0036bfc078a0e9cb7ec67fef3a61662736d16410d7f77869
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"jiti@npm:^2.4.2":
  version: 2.4.2
  resolution: "jiti@npm:2.4.2"
  bin:
    jiti: lib/jiti-cli.mjs
  checksum: c6c30c7b6b293e9f26addfb332b63d964a9f143cdd2cf5e946dbe5143db89f7c1b50ad9223b77fb1f6ddb0b9c5ecef995fea024ecf7d2861d285d779cde66e1e
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-rpc-engine@npm:^6.1.0":
  version: 6.1.0
  resolution: "json-rpc-engine@npm:6.1.0"
  dependencies:
    "@metamask/safe-event-emitter": ^2.0.0
    eth-rpc-errors: ^4.0.2
  checksum: 33b6c9bbd81abf8e323a0281ee05871713203c40d34a4d0bda27706cd0a0935c7b51845238ba89b73027e44ebc8034bbd82db9f962e6c578eb922d9b95acc8bd
  languageName: node
  linkType: hard

"json-rpc-random-id@npm:^1.0.0, json-rpc-random-id@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-rpc-random-id@npm:1.0.1"
  checksum: fcd2e884193a129ace4002bd65a86e9cdb206733b4693baea77bd8b372cf8de3043fbea27716a2c9a716581a908ca8d978d9dfec4847eb2cf77edb4cf4b2252c
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json5@npm:^1.0.2":
  version: 1.0.2
  resolution: "json5@npm:1.0.2"
  dependencies:
    minimist: ^1.2.0
  bin:
    json5: lib/cli.js
  checksum: 866458a8c58a95a49bef3adba929c625e82532bcff1fe93f01d29cb02cac7c3fe1f4b79951b7792c2da9de0b32871a8401a6e3c5b36778ad852bf5b8a61165d7
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0, jsx-ast-utils@npm:^3.3.5":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.flat: ^1.3.1
    object.assign: ^4.1.4
    object.values: ^1.1.6
  checksum: f4b05fa4d7b5234230c905cfa88d36dc8a58a6666975a3891429b1a8cdc8a140bca76c297225cb7a499fad25a2c052ac93934449a2c31a44fc9edd06c773780a
  languageName: node
  linkType: hard

"keccak@npm:^3.0.3":
  version: 3.0.4
  resolution: "keccak@npm:3.0.4"
  dependencies:
    node-addon-api: ^2.0.0
    node-gyp: latest
    node-gyp-build: ^4.2.0
    readable-stream: ^3.6.0
  checksum: 2bf27b97b2f24225b1b44027de62be547f5c7326d87d249605665abd0c8c599d774671c35504c62c9b922cae02758504c6f76a73a84234d23af8a2211afaaa11
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: 3.0.1
  checksum: 74a24395b1c34bd44ad5cb2b49140d087553e170625240b86755a6604cd65aa16efdbdeae5cdb17ba1284a0fbb25ad06263755dbc71b8d8b06f74232ce3cdd72
  languageName: node
  linkType: hard

"keyvaluestorage-interface@npm:^1.0.0":
  version: 1.0.0
  resolution: "keyvaluestorage-interface@npm:1.0.0"
  checksum: e20530e71b738dc094ad170a91a98d4b9bdc772dd9044b23cdaaa102aafa8997b1ac867550a1e66ba1d64fcaa949214df31aed18413b4bac31e5fe1f2c76c9de
  languageName: node
  linkType: hard

"language-subtag-registry@npm:^0.3.20":
  version: 0.3.23
  resolution: "language-subtag-registry@npm:0.3.23"
  checksum: 0b64c1a6c5431c8df648a6d25594ff280613c886f4a1a542d9b864e5472fb93e5c7856b9c41595c38fac31370328fc79fcc521712e89ea6d6866cbb8e0995d81
  languageName: node
  linkType: hard

"language-tags@npm:^1.0.9":
  version: 1.0.9
  resolution: "language-tags@npm:1.0.9"
  dependencies:
    language-subtag-registry: ^0.3.20
  checksum: 57c530796dc7179914dee71bc94f3747fd694612480241d0453a063777265dfe3a951037f7acb48f456bf167d6eb419d4c00263745326b3ba1cdcf4657070e78
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lightningcss-darwin-arm64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-darwin-arm64@npm:1.30.1"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-darwin-x64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-darwin-x64@npm:1.30.1"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-freebsd-x64@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-freebsd-x64@npm:1.30.1"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"lightningcss-linux-arm-gnueabihf@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm-gnueabihf@npm:1.30.1"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-gnu@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm64-gnu@npm:1.30.1"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-arm64-musl@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-arm64-musl@npm:1.30.1"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-linux-x64-gnu@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-x64-gnu@npm:1.30.1"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"lightningcss-linux-x64-musl@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-linux-x64-musl@npm:1.30.1"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"lightningcss-win32-arm64-msvc@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-win32-arm64-msvc@npm:1.30.1"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"lightningcss-win32-x64-msvc@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss-win32-x64-msvc@npm:1.30.1"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"lightningcss@npm:1.30.1":
  version: 1.30.1
  resolution: "lightningcss@npm:1.30.1"
  dependencies:
    detect-libc: ^2.0.3
    lightningcss-darwin-arm64: 1.30.1
    lightningcss-darwin-x64: 1.30.1
    lightningcss-freebsd-x64: 1.30.1
    lightningcss-linux-arm-gnueabihf: 1.30.1
    lightningcss-linux-arm64-gnu: 1.30.1
    lightningcss-linux-arm64-musl: 1.30.1
    lightningcss-linux-x64-gnu: 1.30.1
    lightningcss-linux-x64-musl: 1.30.1
    lightningcss-win32-arm64-msvc: 1.30.1
    lightningcss-win32-x64-msvc: 1.30.1
  dependenciesMeta:
    lightningcss-darwin-arm64:
      optional: true
    lightningcss-darwin-x64:
      optional: true
    lightningcss-freebsd-x64:
      optional: true
    lightningcss-linux-arm-gnueabihf:
      optional: true
    lightningcss-linux-arm64-gnu:
      optional: true
    lightningcss-linux-arm64-musl:
      optional: true
    lightningcss-linux-x64-gnu:
      optional: true
    lightningcss-linux-x64-musl:
      optional: true
    lightningcss-win32-arm64-msvc:
      optional: true
    lightningcss-win32-x64-msvc:
      optional: true
  checksum: cda1e15c2060ffcf8b07c2bf5489eb108a3c836c4d90c3afda7669114099b83fa0b1f28e4db380eb4cd1e7e071b06897bda82379e5981ba15258dc3103ecf507
  languageName: node
  linkType: hard

"lit-element@npm:^4.2.0":
  version: 4.2.0
  resolution: "lit-element@npm:4.2.0"
  dependencies:
    "@lit-labs/ssr-dom-shim": ^1.2.0
    "@lit/reactive-element": ^2.1.0
    lit-html: ^3.3.0
  checksum: 84bfa51608c6efbf4991032f1ff64b3ae38007fb06eda93e5a2625277c8fc515ad930d4aee8069d92a8f0a969c58808af41f3025f0f5d175dfcc23d4839553f7
  languageName: node
  linkType: hard

"lit-html@npm:^3.3.0":
  version: 3.3.0
  resolution: "lit-html@npm:3.3.0"
  dependencies:
    "@types/trusted-types": ^2.0.2
  checksum: c7e310385d624fabaf4c3090e1787ce14ce99cb8b076dcce78b32a6dde8a91daa7afc089da3bcd3e451cd2ddeafb92c86df574939d54f0d3b7029acfb3f038f0
  languageName: node
  linkType: hard

"lit@npm:3.3.0":
  version: 3.3.0
  resolution: "lit@npm:3.3.0"
  dependencies:
    "@lit/reactive-element": ^2.1.0
    lit-element: ^4.2.0
    lit-html: ^3.3.0
  checksum: 9b9b1ee6c9283ad2995cc7b3db1ad06ba218b42f31bd53d47ff28ab7959aa5fd9620187ac2df706d307e2bd51ae3f5ff4d21a7a2a86745e1bf78ac05dbd56573
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0, lru-cache@npm:^10.4.3":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.17":
  version: 0.30.17
  resolution: "magic-string@npm:0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec": ^1.5.0
  checksum: f4b4ed17c5ada64f77fc98491847302ebad64894a905c417c943840c0384662118c9b37f9f68bb86add159fa4749ff6f118c4627d69a470121b46731f8debc6d
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": ^3.0.0
    cacache: ^19.0.1
    http-cache-semantics: ^4.1.1
    minipass: ^7.0.2
    minipass-fetch: ^4.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^1.0.0
    proc-log: ^5.0.0
    promise-retry: ^2.0.1
    ssri: ^12.0.0
  checksum: 6fb2fee6da3d98f1953b03d315826b5c5a4ea1f908481afc113782d8027e19f080c85ae998454de4e5f27a681d3ec58d57278f0868d4e0b736f51d396b661691
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 0e513b29d120f478c85a70f49da0b8b19bc638975eca466f2eeae0071f3ad00454c621bf66e16dd435896c208e719fc91ad79bbfba4e400fe0b372e7c1c9c9a2
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"micro-ftch@npm:^0.3.1":
  version: 0.3.1
  resolution: "micro-ftch@npm:0.3.1"
  checksum: 0e496547253a36e98a83fb00c628c53c3fb540fa5aaeaf718438873785afd193244988c09d219bb1802984ff227d04938d9571ef90fe82b48bd282262586aaff
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 79920eb634e6f400b464a954fcfa589c4e7c7143209488e44baf627f9affc8b1e306f41f4f0deedde97e69cb725920879462d3e750ab3bd3c1aed675bb3a8966
  languageName: node
  linkType: hard

"minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^3.0.1
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 3dfca705ce887ca9ff14d73e8d8593996dea1a1ecd8101fdbb9c10549d1f9670bc8fb66ad0192769ead4c2dc01b4f9ca1cf567ded365adff17827a303b948140
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: ^7.1.2
  checksum: 493bed14dcb6118da7f8af356a8947cf1473289c09658e5aabd69a737800a8c3b1736fb7d7931b722268a9c9bc038a6d53c049b6a6af24b34a121823bb709996
  languageName: node
  linkType: hard

"mipd@npm:0.0.7":
  version: 0.0.7
  resolution: "mipd@npm:0.0.7"
  peerDependencies:
    typescript: ">=5.0.4"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 14526f78d6d1bc8580898922508d64714f5abc7293b5998fe93c54237fd1cea120dc98674fe2b329ba3803bda5a85f3e442c3b1fa880e4c6b443bf73018514a8
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 972deb188e8fb55547f1e58d66bd6b4a3623bf0c7137802582602d73e6480c1c2268dcbafbfb1be466e00cc7e56ac514d7fd9334b7cf33e3e2ab547c16f83a8d
  languageName: node
  linkType: hard

"ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"multiformats@npm:^9.4.2":
  version: 9.9.0
  resolution: "multiformats@npm:9.9.0"
  checksum: d3e8c1be400c09a014f557ea02251a2710dbc9fca5aa32cc702ff29f636c5471e17979f30bdcb0a9cbb556f162a8591dc2e1219c24fc21394a56115b820bb84e
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.11, nanoid@npm:^3.3.6":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 3be20d8866a57a6b6d218e82549711c8352ed969f9ab3c45379da28f405363ad4c9aeb0b39e9abc101a529ca65a72ff9502b00bf74a912c4b64a9d62dfd26c29
  languageName: node
  linkType: hard

"napi-postinstall@npm:^0.2.4":
  version: 0.2.5
  resolution: "napi-postinstall@npm:0.2.5"
  bin:
    napi-postinstall: lib/cli.js
  checksum: cd91b92cd89ee2c2b6435ee50a4dab1b4aa66261aa6f59c28b11118ade7691204d28c227bcf961ee5881ced5c6ef508c3feec236f7bced84bd62bd180b2ea955
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 20ebfe79b2d2e7cf9cbc8239a72662b584f71164096e6e8896c8325055497c96f6b80cd22c258e8a2f2aa382a787795ec3ee8b37b422a302c7d4381b0d5ecfbb
  languageName: node
  linkType: hard

"next@npm:15.3.4":
  version: 15.3.4
  resolution: "next@npm:15.3.4"
  dependencies:
    "@next/env": 15.3.4
    "@next/swc-darwin-arm64": 15.3.4
    "@next/swc-darwin-x64": 15.3.4
    "@next/swc-linux-arm64-gnu": 15.3.4
    "@next/swc-linux-arm64-musl": 15.3.4
    "@next/swc-linux-x64-gnu": 15.3.4
    "@next/swc-linux-x64-musl": 15.3.4
    "@next/swc-win32-arm64-msvc": 15.3.4
    "@next/swc-win32-x64-msvc": 15.3.4
    "@swc/counter": 0.1.3
    "@swc/helpers": 0.5.15
    busboy: 1.6.0
    caniuse-lite: ^1.0.30001579
    postcss: 8.4.31
    sharp: ^0.34.1
    styled-jsx: 5.1.6
  peerDependencies:
    "@opentelemetry/api": ^1.1.0
    "@playwright/test": ^1.41.2
    babel-plugin-react-compiler: "*"
    react: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    react-dom: ^18.2.0 || 19.0.0-rc-de68d2f4-20241204 || ^19.0.0
    sass: ^1.3.0
  dependenciesMeta:
    "@next/swc-darwin-arm64":
      optional: true
    "@next/swc-darwin-x64":
      optional: true
    "@next/swc-linux-arm64-gnu":
      optional: true
    "@next/swc-linux-arm64-musl":
      optional: true
    "@next/swc-linux-x64-gnu":
      optional: true
    "@next/swc-linux-x64-musl":
      optional: true
    "@next/swc-win32-arm64-msvc":
      optional: true
    "@next/swc-win32-x64-msvc":
      optional: true
    sharp:
      optional: true
  peerDependenciesMeta:
    "@opentelemetry/api":
      optional: true
    "@playwright/test":
      optional: true
    babel-plugin-react-compiler:
      optional: true
    sass:
      optional: true
  bin:
    next: dist/bin/next
  checksum: c861837488c88b0b5799f3243eb3747aa42d480b388cdc371ef3e049eaf850cc08a4e5f314a66b3693d2b7e5f8f1bf1c67a905975d4595f0776b057e83fe4f36
  languageName: node
  linkType: hard

"node-addon-api@npm:^2.0.0":
  version: 2.0.2
  resolution: "node-addon-api@npm:2.0.2"
  dependencies:
    node-gyp: latest
  checksum: 31fb22d674648204f8dd94167eb5aac896c841b84a9210d614bf5d97c74ef059cc6326389cf0c54d2086e35312938401d4cc82e5fcd679202503eb8ac84814f8
  languageName: node
  linkType: hard

"node-fetch-native@npm:^1.6.4, node-fetch-native@npm:^1.6.6":
  version: 1.6.6
  resolution: "node-fetch-native@npm:1.6.6"
  checksum: 1d8559b0828784d089c10bdaccdbfac35af41d8c93edfaf14b3aa7bb9fc1ea33a252a43f2dc31e95b7e8c6516794b227a532d9647483dea85e48beb93cbcfb83
  languageName: node
  linkType: hard

"node-fetch@npm:^2.7.0":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: d76d2f5edb451a3f05b15115ec89fc6be39de37c6089f1b6368df03b91e1633fd379a7e01b7ab05089a25034b2023d959b47e59759cb38d88341b2459e89d6e5
  languageName: node
  linkType: hard

"node-gyp-build@npm:^4.2.0, node-gyp-build@npm:^4.3.0":
  version: 4.8.4
  resolution: "node-gyp-build@npm:4.8.4"
  bin:
    node-gyp-build: bin.js
    node-gyp-build-optional: optional.js
    node-gyp-build-test: build-test.js
  checksum: 8b81ca8ffd5fa257ad8d067896d07908a36918bc84fb04647af09d92f58310def2d2b8614d8606d129d9cd9b48890a5d2bec18abe7fcff54818f72bedd3a7d74
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    graceful-fs: ^4.2.6
    make-fetch-happen: ^14.0.3
    nopt: ^8.0.0
    proc-log: ^5.0.0
    semver: ^7.3.5
    tar: ^7.4.3
    tinyglobby: ^0.2.12
    which: ^5.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 2536282ba81f8a94b29482d3622b6ab298611440619e46de4512a6f32396a68b5530357c474b859787069d84a4c537d99e0c71078cce5b9f808bf84eeb78e8fb
  languageName: node
  linkType: hard

"node-mock-http@npm:^1.0.0":
  version: 1.0.1
  resolution: "node-mock-http@npm:1.0.1"
  checksum: 8f009ecfd435e12a004432a4f3d02ce8452a023dc06384455c690b9fd925d5491f69a62c9f44aa1aef238b1e794b0ae5adab6c4f2a57f5df5de3bc3ddface6f5
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: ^3.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 49cfd3eb6f565e292bf61f2ff1373a457238804d5a5a63a8d786c923007498cba89f3648e3b952bc10203e3e7285752abf5b14eaf012edb821e84f24e881a92a
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"obj-multiplex@npm:^1.0.0":
  version: 1.0.0
  resolution: "obj-multiplex@npm:1.0.0"
  dependencies:
    end-of-stream: ^1.4.0
    once: ^1.4.0
    readable-stream: ^2.3.3
  checksum: 6bdcb7d48a1cd4458a7ff0be0b3c1dc58e8e9e6504f937c10b1eac096a3d459b85d7ba32bdd9a45382bb238e245eb42ebcd91430c72f04b0a57c97f846f2d06f
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3, object-inspect@npm:^1.13.4":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 582810c6a8d2ef988ea0a39e69e115a138dad8f42dd445383b394877e5816eb4268489f316a6f74ee9c4e0a984b3eab1028e3e79d62b1ed67c726661d55c7a8b
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4, object.assign@npm:^4.1.7":
  version: 4.1.7
  resolution: "object.assign@npm:4.1.7"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
    has-symbols: ^1.1.0
    object-keys: ^1.1.1
  checksum: 60e07d2651cf4f5528c485f1aa4dbded9b384c47d80e8187cefd11320abb1aebebf78df5483451dfa549059f8281c21f7b4bf7d19e9e5e97d8d617df0df298de
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.9":
  version: 1.1.9
  resolution: "object.entries@npm:1.1.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    define-properties: ^1.2.1
    es-object-atoms: ^1.1.1
  checksum: 0ab2ef331c4d6a53ff600a5d69182948d453107c3a1f7fd91bc29d387538c2aba21d04949a74f57c21907208b1f6fb175567fd1f39f1a7a4046ba1bca762fb41
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
  checksum: 29b2207a2db2782d7ced83f93b3ff5d425f901945f3665ffda1821e30a7253cd1fd6b891a64279976098137ddfa883d748787a6fea53ecdb51f8df8b8cec0ae1
  languageName: node
  linkType: hard

"object.groupby@npm:^1.0.3":
  version: 1.0.3
  resolution: "object.groupby@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
  checksum: 0d30693ca3ace29720bffd20b3130451dca7a56c612e1926c0a1a15e4306061d84410bdb1456be2656c5aca53c81b7a3661eceaa362db1bba6669c2c9b6d1982
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6, object.values@npm:^1.2.1":
  version: 1.2.1
  resolution: "object.values@npm:1.2.1"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: f9b9a2a125ccf8ded29414d7c056ae0d187b833ee74919821fc60d7e216626db220d9cb3cf33f965c84aaaa96133626ca13b80f3c158b673976dc8cfcfcd26bb
  languageName: node
  linkType: hard

"ofetch@npm:^1.4.1":
  version: 1.4.1
  resolution: "ofetch@npm:1.4.1"
  dependencies:
    destr: ^2.0.3
    node-fetch-native: ^1.6.4
    ufo: ^1.5.4
  checksum: 005974d238b7212dc10b67ddb019eda9cf89ba781dfa8c2f31d8eea0782261d626ce7a36ac377deb71ec0f72f05a023e6d3cc31b7384fbbabdb328afbf1bf929
  languageName: node
  linkType: hard

"on-exit-leak-free@npm:^0.2.0":
  version: 0.2.0
  resolution: "on-exit-leak-free@npm:0.2.0"
  checksum: d22b0f0538069110626b578db6e68b6ee0e85b1ee9cc5ef9b4de1bba431431d6a8da91a61e09d2ad46f22a96f968e5237833cb9d0b69bc4d294f7ec82f609b05
  languageName: node
  linkType: hard

"once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
    word-wrap: ^1.2.5
  checksum: ecbd010e3dc73e05d239976422d9ef54a82a13f37c11ca5911dff41c98a6c7f0f163b27f922c37e7f8340af9d36febd3b6e9cef508f3339d4c393d7276d716bb
  languageName: node
  linkType: hard

"own-keys@npm:^1.0.1":
  version: 1.0.1
  resolution: "own-keys@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.2.6
    object-keys: ^1.1.1
    safe-push-apply: ^1.0.0
  checksum: cc9dd7d85c4ccfbe8109fce307d581ac7ede7b26de892b537873fbce2dc6a206d89aea0630dbb98e47ce0873517cefeaa7be15fcf94aaf4764a3b34b474a5b61
  languageName: node
  linkType: hard

"ox@npm:0.6.7":
  version: 0.6.7
  resolution: "ox@npm:0.6.7"
  dependencies:
    "@adraffy/ens-normalize": ^1.10.1
    "@noble/curves": ^1.6.0
    "@noble/hashes": ^1.5.0
    "@scure/bip32": ^1.5.0
    "@scure/bip39": ^1.4.0
    abitype: ^1.0.6
    eventemitter3: 5.0.1
  peerDependencies:
    typescript: ">=5.4.0"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 99acb683ff1cf78749f2b4230d3c208b8cdea0b3bf2bff0db564207917ae6833093b203cb7b9853fc8ec642ca0c8c87cd70a50eab9ff9944c55bf990436112b5
  languageName: node
  linkType: hard

"ox@npm:0.8.1":
  version: 0.8.1
  resolution: "ox@npm:0.8.1"
  dependencies:
    "@adraffy/ens-normalize": ^1.11.0
    "@noble/ciphers": ^1.3.0
    "@noble/curves": ^1.9.1
    "@noble/hashes": ^1.8.0
    "@scure/bip32": ^1.7.0
    "@scure/bip39": ^1.6.0
    abitype: ^1.0.8
    eventemitter3: 5.0.1
  peerDependencies:
    typescript: ">=5.4.0"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 0c8c3210c173f44f617055cfca34630dd74752f21f7f244d25095403aaebb748d53197f78ba24a830024dc4f7d8f2c7175230d2a7d95ae5826509571791a1763
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 8c92d533acf82f0d12f7e196edccff773f384098bbb048acdd55a08778ce4fc8889d8f1bde72969487bd96f9c63212698d79744c20bedfce36c5b00b46d369f8
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 58ee9538f2f762988433da00e26acc788036914d57c71c246bf0be1b60cdbd77dd60b6a3e1a30465f0b248aeb80079e0b34cb6050b1dfa18c06953bb1cbc7602
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: e1cf46bf84886c79055fdfa9dcb3e4711ad259949e3565154b004b260cd356c5d54b31a1437ce9782624bf766272fe6b0154f5f0c744fb7af5d454d2b60db045
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: a7a5188c954f82c6585720e9143297ccd0e35ad8072231608086ca950bee672d51b0ef676254af0788205e59bd4e4deb4e7708769226bed725bf13370a7d1464
  languageName: node
  linkType: hard

"pify@npm:^3.0.0":
  version: 3.0.0
  resolution: "pify@npm:3.0.0"
  checksum: 6cdcbc3567d5c412450c53261a3f10991665d660961e06605decf4544a61a97a54fefe70a68d5c37080ff9d6f4cf51444c90198d1ba9f9309a6c0d6e9f5c4fde
  languageName: node
  linkType: hard

"pify@npm:^5.0.0":
  version: 5.0.0
  resolution: "pify@npm:5.0.0"
  checksum: 443e3e198ad6bfa8c0c533764cf75c9d5bc976387a163792fb553ffe6ce923887cf14eebf5aea9b7caa8eab930da8c33612990ae85bd8c2bc18bedb9eae94ecb
  languageName: node
  linkType: hard

"pino-abstract-transport@npm:v0.5.0":
  version: 0.5.0
  resolution: "pino-abstract-transport@npm:0.5.0"
  dependencies:
    duplexify: ^4.1.2
    split2: ^4.0.0
  checksum: c503f867de3189f8217ab9cf794e8a631dddd0029a829f0f985f5511308152ebd53e363764fbc5570b3d1c715b341e3923456ce16ad84cd41be2b9a074ada234
  languageName: node
  linkType: hard

"pino-std-serializers@npm:^4.0.0":
  version: 4.0.0
  resolution: "pino-std-serializers@npm:4.0.0"
  checksum: 89d487729b58c9d3273a0ee851ead068d6d2e2ccc1af8e1c1d28f1b3442423679bec7ec04d9a2aba36f94f335e82be9f4de19dc4fbc161e71c136aaa15b85ad3
  languageName: node
  linkType: hard

"pino@npm:7.11.0":
  version: 7.11.0
  resolution: "pino@npm:7.11.0"
  dependencies:
    atomic-sleep: ^1.0.0
    fast-redact: ^3.0.0
    on-exit-leak-free: ^0.2.0
    pino-abstract-transport: v0.5.0
    pino-std-serializers: ^4.0.0
    process-warning: ^1.0.0
    quick-format-unescaped: ^4.0.3
    real-require: ^0.1.0
    safe-stable-stringify: ^2.1.0
    sonic-boom: ^2.2.1
    thread-stream: ^0.15.1
  bin:
    pino: bin.js
  checksum: b919e7dbe41de978bb050dcef94fd687c012eb78d344a18f75f04ce180d5810fc162be1f136722d70cd005ed05832c4023a38b9acbc1076ae63c9f5ec5ca515c
  languageName: node
  linkType: hard

"pngjs@npm:^5.0.0":
  version: 5.0.0
  resolution: "pngjs@npm:5.0.0"
  checksum: 04e912cc45fb9601564e2284efaf0c5d20d131d9b596244f8a6789fc6cdb6b18d2975a6bbf7a001858d7e159d5c5c5dd7b11592e97629b7137f7f5cef05904c8
  languageName: node
  linkType: hard

"pony-cause@npm:^2.1.10":
  version: 2.1.11
  resolution: "pony-cause@npm:2.1.11"
  checksum: 4aaa9ddab8f8225b5cbb32f7329a71b73679074579fa91f9e9d6853d398f3c2872de979519e1525c0c91d53afc82c32fddb76e379d19157e69ef1f7064523dfa
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.1.0
  resolution: "possible-typed-array-names@npm:1.1.0"
  checksum: cfcd4f05264eee8fd184cd4897a17890561d1d473434b43ab66ad3673d9c9128981ec01e0cb1d65a52cd6b1eebfb2eae1e53e39b2e0eca86afc823ede7a4f41b
  languageName: node
  linkType: hard

"postcss@npm:8.4.31":
  version: 8.4.31
  resolution: "postcss@npm:8.4.31"
  dependencies:
    nanoid: ^3.3.6
    picocolors: ^1.0.0
    source-map-js: ^1.0.2
  checksum: 1d8611341b073143ad90486fcdfeab49edd243377b1f51834dc4f6d028e82ce5190e4f11bb2633276864503654fb7cab28e67abdc0fbf9d1f88cad4a0ff0beea
  languageName: node
  linkType: hard

"postcss@npm:^8.4.41":
  version: 8.5.6
  resolution: "postcss@npm:8.5.6"
  dependencies:
    nanoid: ^3.3.11
    picocolors: ^1.1.1
    source-map-js: ^1.2.1
  checksum: 20f3b5d673ffeec2b28d65436756d31ee33f65b0a8bedb3d32f556fbd5973be38c3a7fb5b959a5236c60a5db7b91b0a6b14ffaac0d717dce1b903b964ee1c1bb
  languageName: node
  linkType: hard

"preact@npm:^10.16.0, preact@npm:^10.24.2":
  version: 10.26.9
  resolution: "preact@npm:10.26.9"
  checksum: d5a9c521ca4ab35414bd9c7874e2ac4eaf0561875e2a5bb9f3c4c67e6e3d6971e122f763d19333f9b616a5f280b5d22cde526bb9c9fd9820bee15600a2f7269b
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: c78b26ecef6d5cce4a7489a1e9923d7b4b1679028c8654aef0463b27f4a90b0946cd598f55799da602895c52feb085ec76381d007ab8dcceebd40b89c2f9dfe0
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"process-warning@npm:^1.0.0":
  version: 1.0.0
  resolution: "process-warning@npm:1.0.0"
  checksum: c708a03241deec3cabaeee39c4f9ee8c4d71f1c5ef9b746c8252cdb952a6059068cfcdaf348399775244cbc441b6ae5e26a9c87ed371f88335d84f26d19180f9
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: bfcce49814f7d172a6e6a14d5fa3ac92cc3d0c3b9feb1279774708a719e19acd673995226351a082a9ae99978254e320ccda4240ddc474ba31a76c79491ca7c3
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"proxy-compare@npm:2.6.0":
  version: 2.6.0
  resolution: "proxy-compare@npm:2.6.0"
  checksum: 244dc49b45749ebf9dd2c6890e600c16a7d7432a8f535be9dc72db616469685bb5db4bbdb61f211b14c59034b601508f826007715df441d6e3f089b880e265d8
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.3
  resolution: "pump@npm:3.0.3"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: 52843fc933b838c0330f588388115a1b28ef2a5ffa7774709b142e35431e8ab0c2edec90de3fa34ebb72d59fef854f151eea7dfc211b6dcf586b384556bd2f39
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: bb0a0ceedca4c3c57a9b981b90601579058903c62be23c5e8e843d2c2d4148a3ecf029d5133486fb0e1822b098ba8bba09e89d6b21742d02fa26bda6441a6fb2
  languageName: node
  linkType: hard

"qrcode@npm:1.5.3":
  version: 1.5.3
  resolution: "qrcode@npm:1.5.3"
  dependencies:
    dijkstrajs: ^1.0.1
    encode-utf8: ^1.0.3
    pngjs: ^5.0.0
    yargs: ^15.3.1
  bin:
    qrcode: bin/qrcode
  checksum: 9a8a20a0a9cb1d15de8e7b3ffa214e8b6d2a8b07655f25bd1b1d77f4681488f84d7bae569870c0652872d829d5f8ac4922c27a6bd14c13f0e197bf07b28dead7
  languageName: node
  linkType: hard

"query-string@npm:7.1.3":
  version: 7.1.3
  resolution: "query-string@npm:7.1.3"
  dependencies:
    decode-uri-component: ^0.2.2
    filter-obj: ^1.1.0
    split-on-first: ^1.0.0
    strict-uri-encode: ^2.0.0
  checksum: 91af02dcd9cc9227a052841d5c2eecb80a0d6489d05625df506a097ef1c59037cfb5e907f39b84643cbfd535c955abec3e553d0130a7b510120c37d06e0f4346
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"quick-format-unescaped@npm:^4.0.3":
  version: 4.0.4
  resolution: "quick-format-unescaped@npm:4.0.4"
  checksum: 7bc32b99354a1aa46c089d2a82b63489961002bb1d654cee3e6d2d8778197b68c2d854fd23d8422436ee1fdfd0abaddc4d4da120afe700ade68bd357815b26fd
  languageName: node
  linkType: hard

"radix3@npm:^1.1.2":
  version: 1.1.2
  resolution: "radix3@npm:1.1.2"
  checksum: c4d49a3f603b5b7b7704dd907383c8884d12064d6d475f7ca8b05ecc7604d3bd73524b55e0fbcca0f7c9da3a2e9b473a6b4fbc0b639c29c2b0e85020ebda67d3
  languageName: node
  linkType: hard

"react-dom@npm:^19.0.0":
  version: 19.1.0
  resolution: "react-dom@npm:19.1.0"
  dependencies:
    scheduler: ^0.26.0
  peerDependencies:
    react: ^19.1.0
  checksum: 1d154b6543467095ac269e61ca59db546f34ef76bcdeb90f2dad41d682cd210aae492e70c85010ed5d0a2caea225e9a55139ebc1a615ee85bf197d7f99678cdf
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react@npm:^19.0.0":
  version: 19.1.0
  resolution: "react@npm:19.1.0"
  checksum: c0905f8cfb878b0543a5522727e5ed79c67c8111dc16ceee135b7fe19dce77b2c1c19293513061a8934e721292bfc1517e0487e262d1906f306bdf95fa54d02f
  languageName: node
  linkType: hard

"readable-stream@npm:^2.3.3":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: 65645467038704f0c8aaf026a72fbb588a9e2ef7a75cd57a01702ee9db1c4a1e4b03aaad36861a6a0926546a74d174149c8c207527963e0c2d3eee2f37678a42
  languageName: node
  linkType: hard

"readable-stream@npm:^3.1.1, readable-stream@npm:^3.6.0, readable-stream@npm:^3.6.2":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"readable-stream@npm:^3.6.2 || ^4.4.2":
  version: 4.7.0
  resolution: "readable-stream@npm:4.7.0"
  dependencies:
    abort-controller: ^3.0.0
    buffer: ^6.0.3
    events: ^3.3.0
    process: ^0.11.10
    string_decoder: ^1.3.0
  checksum: 03ec762faed8e149dc6452798b60394a8650861a1bb4bf936fa07b94044826bc25abe73696f5f45372abc404eec01876c560f64b479eba108b56397312dbe2ae
  languageName: node
  linkType: hard

"readdirp@npm:^4.0.1":
  version: 4.1.2
  resolution: "readdirp@npm:4.1.2"
  checksum: 3242ee125422cb7c0e12d51452e993f507e6ed3d8c490bc8bf3366c5cdd09167562224e429b13e9cb2b98d4b8b2b11dc100d3c73883aa92d657ade5a21ded004
  languageName: node
  linkType: hard

"real-require@npm:^0.1.0":
  version: 0.1.0
  resolution: "real-require@npm:0.1.0"
  checksum: 96745583ed4f82cd5c6a6af012fd1d3c6fc2f13ae1bcff1a3c4f8094696013a1a07c82c5aa66a403d7d4f84949fc2203bc927c7ad120caad125941ca2d7e5e8e
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.6, reflect.getprototypeof@npm:^1.0.9":
  version: 1.0.10
  resolution: "reflect.getprototypeof@npm:1.0.10"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-abstract: ^1.23.9
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.7
    get-proto: ^1.0.1
    which-builtin-type: ^1.2.1
  checksum: ccc5debeb66125e276ae73909cecb27e47c35d9bb79d9cc8d8d055f008c58010ab8cb401299786e505e4aab733a64cba9daf5f312a58e96a43df66adad221870
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.3, regexp.prototype.flags@npm:^1.5.4":
  version: 1.5.4
  resolution: "regexp.prototype.flags@npm:1.5.4"
  dependencies:
    call-bind: ^1.0.8
    define-properties: ^1.2.1
    es-errors: ^1.3.0
    get-proto: ^1.0.1
    gopd: ^1.2.0
    set-function-name: ^2.0.2
  checksum: 18cb667e56cb328d2dda569d7f04e3ea78f2683135b866d606538cf7b1d4271f7f749f09608c877527799e6cf350e531368f3c7a20ccd1bb41048a48926bdeeb
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"require-main-filename@npm:^2.0.0":
  version: 2.0.0
  resolution: "require-main-filename@npm:2.0.0"
  checksum: e9e294695fea08b076457e9ddff854e81bffbe248ed34c1eec348b7abbd22a0d02e8d75506559e2265e96978f3c4720bd77a6dad84755de8162b357eb6c778c7
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 1012afc566b3fdb190a6309cc37ef3b2dcc35dff5fa6683a9d00cd25c3247edfbc4691b91078c97adc82a29b77a2660c30d791d65dab4fc78bfc473f60289977
  languageName: node
  linkType: hard

"resolve@npm:^1.22.4":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: ab7a32ff4046fcd7c6fdd525b24a7527847d03c3650c733b909b01b757f92eb23510afa9cc3e9bf3f26a3e073b48c88c706dfd4c1d2fb4a16a96b73b6328ddcf
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.5":
  version: 2.0.0-next.5
  resolution: "resolve@npm:2.0.0-next.5"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: a73ac69a1c4bd34c56b213d91f5b17ce390688fdb4a1a96ed3025cc7e08e7bfb90b3a06fcce461780cb0b589c958afcb0080ab802c71c01a7ecc8c64feafc89f
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.22.4#~builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#~builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: ^2.16.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 8aac1e4e4628bd00bf4b94b23de137dd3fe44097a8d528fd66db74484be929936e20c696e1a3edf4488f37e14180b73df6f600992baea3e089e8674291f16c9d
  languageName: node
  linkType: hard

"resolve@patch:resolve@^2.0.0-next.5#~builtin<compat/resolve>":
  version: 2.0.0-next.5
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.5#~builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 064d09c1808d0c51b3d90b5d27e198e6d0c5dad0eb57065fd40803d6a20553e5398b07f76739d69cbabc12547058bec6b32106ea66622375fb0d7e8fca6a846c
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 64cb3142ac5e9ad689aca289585cb41d22521f4571f73e9488af39f6b1bd62f0cbb3d65e2ecc768ec6494052523f473f1eb4b55c3e9014b3590c17fc6a03e22a
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.3":
  version: 1.1.3
  resolution: "safe-array-concat@npm:1.1.3"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    get-intrinsic: ^1.2.6
    has-symbols: ^1.1.0
    isarray: ^2.0.5
  checksum: 00f6a68140e67e813f3ad5e73e6dedcf3e42a9fa01f04d44b0d3f7b1f4b257af876832a9bfc82ac76f307e8a6cc652e3cf95876048a26cbec451847cf6ae3707
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.0.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safe-push-apply@npm:^1.0.0":
  version: 1.0.0
  resolution: "safe-push-apply@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    isarray: ^2.0.5
  checksum: 8c11cbee6dc8ff5cc0f3d95eef7052e43494591384015902e4292aef4ae9e539908288520ed97179cee17d6ffb450fe5f05a46ce7a1749685f7524fd568ab5db
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.3, safe-regex-test@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex-test@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    is-regex: ^1.2.1
  checksum: 3c809abeb81977c9ed6c869c83aca6873ea0f3ab0f806b8edbba5582d51713f8a6e9757d24d2b4b088f563801475ea946c8e77e7713e8c65cdd02305b6caedab
  languageName: node
  linkType: hard

"safe-stable-stringify@npm:^2.1.0":
  version: 2.5.0
  resolution: "safe-stable-stringify@npm:2.5.0"
  checksum: d3ce103ed43c6c2f523e39607208bfb1c73aa48179fc5be53c3aa97c118390bffd4d55e012f5393b982b65eb3e0ee954dd57b547930d3f242b0053dcdb923d17
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"scheduler@npm:^0.26.0":
  version: 0.26.0
  resolution: "scheduler@npm:0.26.0"
  checksum: c63a9f1c0e5089b537231cff6c11f75455b5c8625ae09535c1d7cd0a1b0c77ceecdd9f1074e5e063da5d8dc11e73e8033dcac3361791088be08a6e60c0283ed9
  languageName: node
  linkType: hard

"semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.3.8, semver@npm:^7.5.4, semver@npm:^7.6.0, semver@npm:^7.7.1, semver@npm:^7.7.2":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: dd94ba8f1cbc903d8eeb4dd8bf19f46b3deb14262b6717d0de3c804b594058ae785ef2e4b46c5c3b58733c99c83339068203002f9e37cfe44f7e2cc5e3d2f621
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 6e65a05f7cf7ebdf8b7c75b101e18c0b7e3dff4940d480efed8aad3a36a4005140b660fa1d804cb8bce911cac290441dc728084a30504d3516ac2ff7ad607b02
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.2":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
  checksum: a8248bdacdf84cb0fab4637774d9fb3c7a8e6089866d04c817583ff48e14149c87044ce683d7f50759a8c50fb87c7a7e173535b06169c87ef76f5fb276dfff72
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    functions-have-names: ^1.2.3
    has-property-descriptors: ^1.0.2
  checksum: d6229a71527fd0404399fc6227e0ff0652800362510822a291925c9d7b48a1ca1a468b11b281471c34cd5a2da0db4f5d7ff315a61d26655e77f6e971e6d0c80f
  languageName: node
  linkType: hard

"set-proto@npm:^1.0.0":
  version: 1.0.0
  resolution: "set-proto@npm:1.0.0"
  dependencies:
    dunder-proto: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
  checksum: ec27cbbe334598547e99024403e96da32aca3e530583e4dba7f5db1c43cbc4affa9adfbd77c7b2c210b9b8b2e7b2e600bad2a6c44fd62e804d8233f96bbb62f4
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.11":
  version: 2.4.11
  resolution: "sha.js@npm:2.4.11"
  dependencies:
    inherits: ^2.0.1
    safe-buffer: ^5.0.1
  bin:
    sha.js: ./bin.js
  checksum: ebd3f59d4b799000699097dadb831c8e3da3eb579144fd7eb7a19484cbcbb7aca3c68ba2bb362242eb09e33217de3b4ea56e4678184c334323eca24a58e3ad07
  languageName: node
  linkType: hard

"sharp@npm:^0.34.1":
  version: 0.34.2
  resolution: "sharp@npm:0.34.2"
  dependencies:
    "@img/sharp-darwin-arm64": 0.34.2
    "@img/sharp-darwin-x64": 0.34.2
    "@img/sharp-libvips-darwin-arm64": 1.1.0
    "@img/sharp-libvips-darwin-x64": 1.1.0
    "@img/sharp-libvips-linux-arm": 1.1.0
    "@img/sharp-libvips-linux-arm64": 1.1.0
    "@img/sharp-libvips-linux-ppc64": 1.1.0
    "@img/sharp-libvips-linux-s390x": 1.1.0
    "@img/sharp-libvips-linux-x64": 1.1.0
    "@img/sharp-libvips-linuxmusl-arm64": 1.1.0
    "@img/sharp-libvips-linuxmusl-x64": 1.1.0
    "@img/sharp-linux-arm": 0.34.2
    "@img/sharp-linux-arm64": 0.34.2
    "@img/sharp-linux-s390x": 0.34.2
    "@img/sharp-linux-x64": 0.34.2
    "@img/sharp-linuxmusl-arm64": 0.34.2
    "@img/sharp-linuxmusl-x64": 0.34.2
    "@img/sharp-wasm32": 0.34.2
    "@img/sharp-win32-arm64": 0.34.2
    "@img/sharp-win32-ia32": 0.34.2
    "@img/sharp-win32-x64": 0.34.2
    color: ^4.2.3
    detect-libc: ^2.0.4
    semver: ^7.7.2
  dependenciesMeta:
    "@img/sharp-darwin-arm64":
      optional: true
    "@img/sharp-darwin-x64":
      optional: true
    "@img/sharp-libvips-darwin-arm64":
      optional: true
    "@img/sharp-libvips-darwin-x64":
      optional: true
    "@img/sharp-libvips-linux-arm":
      optional: true
    "@img/sharp-libvips-linux-arm64":
      optional: true
    "@img/sharp-libvips-linux-ppc64":
      optional: true
    "@img/sharp-libvips-linux-s390x":
      optional: true
    "@img/sharp-libvips-linux-x64":
      optional: true
    "@img/sharp-libvips-linuxmusl-arm64":
      optional: true
    "@img/sharp-libvips-linuxmusl-x64":
      optional: true
    "@img/sharp-linux-arm":
      optional: true
    "@img/sharp-linux-arm64":
      optional: true
    "@img/sharp-linux-s390x":
      optional: true
    "@img/sharp-linux-x64":
      optional: true
    "@img/sharp-linuxmusl-arm64":
      optional: true
    "@img/sharp-linuxmusl-x64":
      optional: true
    "@img/sharp-wasm32":
      optional: true
    "@img/sharp-win32-arm64":
      optional: true
    "@img/sharp-win32-ia32":
      optional: true
    "@img/sharp-win32-x64":
      optional: true
  checksum: beb34afe75cc6492fc7e6331efebfa11a0f92bf0f54ac850bf4c93ab48ab4152103cf096a892802bacca7c8102b721312b098bfdda16a4bf6c95716dabb28a16
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
  checksum: 603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
  checksum: 42501371cdf71f4ccbbc9c9e2eb00aaaab80a4c1c429d5e8da713fd4d39ef3b8d4a4b37ed4f275798a65260a551a7131fd87fe67e922dba4ac18586d6aab8b06
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
    side-channel-map: ^1.0.1
  checksum: a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
    side-channel-list: ^1.0.0
    side-channel-map: ^1.0.1
    side-channel-weakmap: ^1.0.2
  checksum: bf73d6d6682034603eb8e99c63b50155017ed78a522d27c2acec0388a792c3ede3238b878b953a08157093b85d05797217d270b7666ba1f111345fbe933380ff
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: ^0.3.1
  checksum: a7f3f2ab5c76c4472d5c578df892e857323e452d9f392e1b5cf74b74db66e6294a1e1b8b390b519fa1b96b5b613f2a37db6cffef52c3f1f8f3c5ea64eb2d54c0
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socket.io-client@npm:^4.5.1":
  version: 4.8.1
  resolution: "socket.io-client@npm:4.8.1"
  dependencies:
    "@socket.io/component-emitter": ~3.1.0
    debug: ~4.3.2
    engine.io-client: ~6.6.1
    socket.io-parser: ~4.2.4
  checksum: 0ac31d224c7a8567d95bb0522214cc0d3dfa1fbf6de38e63d9aa8e806526e27381d06a527d39e02f173199c48ad112737107148d2e60e1c48a6a8e15391dce8d
  languageName: node
  linkType: hard

"socket.io-parser@npm:~4.2.4":
  version: 4.2.4
  resolution: "socket.io-parser@npm:4.2.4"
  dependencies:
    "@socket.io/component-emitter": ~3.1.0
    debug: ~4.3.1
  checksum: 61540ef99af33e6a562b9effe0fad769bcb7ec6a301aba5a64b3a8bccb611a0abdbe25f469933ab80072582006a78ca136bf0ad8adff9c77c9953581285e2263
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: ^7.1.2
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b4fbcdb7ad2d6eec445926e255a1fb95c975db0020543fbac8dfa6c47aecc6b3b619b7fb9c60a3f82c9b2969912a5e7e174a056ae4d98cb5322f3524d6036e1d
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.5
  resolution: "socks@npm:2.8.5"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: d39a77a8c91cfacafc75c67dba45925eccfd884a8a4a68dcda6fb9ab7f37de6e250bb6db3721e8a16a066a8e1ebe872d4affc26f3eb763f4befedcc7b733b7ed
  languageName: node
  linkType: hard

"sonic-boom@npm:^2.2.1":
  version: 2.8.0
  resolution: "sonic-boom@npm:2.8.0"
  dependencies:
    atomic-sleep: ^1.0.0
  checksum: c7f9c89f931d7f60f8e0741551a729f0d81e6dc407a99420fc847a9a4c25af048a615b1188ab3c4f1fb3708fe4904973ddab6ebcc8ed5b78b50ab81a99045910
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.2, source-map-js@npm:^1.2.1":
  version: 1.2.1
  resolution: "source-map-js@npm:1.2.1"
  checksum: 4eb0cd997cdf228bc253bcaff9340afeb706176e64868ecd20efbe6efea931465f43955612346d6b7318789e5265bdc419bc7669c1cebe3db0eb255f57efa76b
  languageName: node
  linkType: hard

"split-on-first@npm:^1.0.0":
  version: 1.1.0
  resolution: "split-on-first@npm:1.1.0"
  checksum: 16ff85b54ddcf17f9147210a4022529b343edbcbea4ce977c8f30e38408b8d6e0f25f92cd35b86a524d4797f455e29ab89eb8db787f3c10708e0b47ebf528d30
  languageName: node
  linkType: hard

"split2@npm:^4.0.0":
  version: 4.2.0
  resolution: "split2@npm:4.2.0"
  checksum: 05d54102546549fe4d2455900699056580cca006c0275c334611420f854da30ac999230857a85fdd9914dc2109ae50f80fda43d2a445f2aa86eccdc1dfce779d
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: ^7.0.3
  checksum: ef4b6b0ae47b4a69896f5f1c4375f953b9435388c053c36d27998bc3d73e046969ccde61ab659e679142971a0b08e50478a1228f62edb994105b280f17900c98
  languageName: node
  linkType: hard

"stable-hash@npm:^0.0.5":
  version: 0.0.5
  resolution: "stable-hash@npm:0.0.5"
  checksum: 9222ea2c558e37c4a576cb4e406966b9e6aa05b93f5c4f09ef4aaabe3577439b9b8fbff407b16840b63e2ae83de74290c7b1c2da7360d571e480e46a4aec0a56
  languageName: node
  linkType: hard

"stop-iteration-iterator@npm:^1.1.0":
  version: 1.1.0
  resolution: "stop-iteration-iterator@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    internal-slot: ^1.1.0
  checksum: be944489d8829fb3bdec1a1cc4a2142c6b6eb317305eeace1ece978d286d6997778afa1ae8cb3bd70e2b274b9aa8c69f93febb1e15b94b1359b11058f9d3c3a1
  languageName: node
  linkType: hard

"stream-shift@npm:^1.0.2":
  version: 1.0.3
  resolution: "stream-shift@npm:1.0.3"
  checksum: a24c0a3f66a8f9024bd1d579a533a53be283b4475d4e6b4b3211b964031447bdf6532dd1f3c2b0ad66752554391b7c62bd7ca4559193381f766534e723d50242
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 1cce16cea8405d7a233d32ca5e00a00169cc0e19fbc02aa839959985f267335d435c07f96e5e0edd0eadc6d39c98d5435fb5bbbdefc62c41834eadc5622ad942
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^2.0.0":
  version: 2.0.0
  resolution: "strict-uri-encode@npm:2.0.0"
  checksum: eaac4cf978b6fbd480f1092cab8b233c9b949bcabfc9b598dd79a758f7243c28765ef7639c876fa72940dac687181b35486ea01ff7df3e65ce3848c64822c581
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.includes@npm:^2.0.1":
  version: 2.0.1
  resolution: "string.prototype.includes@npm:2.0.1"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.3
  checksum: ed4b7058b092f30d41c4df1e3e805eeea92479d2c7a886aa30f42ae32fde8924a10cc99cccc99c29b8e18c48216608a0fe6bf887f8b4aadf9559096a758f313a
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.12":
  version: 4.0.12
  resolution: "string.prototype.matchall@npm:4.0.12"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.3
    define-properties: ^1.2.1
    es-abstract: ^1.23.6
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.6
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    internal-slot: ^1.1.0
    regexp.prototype.flags: ^1.5.3
    set-function-name: ^2.0.2
    side-channel: ^1.1.0
  checksum: 98a09d6af91bfc6ee25556f3d7cd6646d02f5f08bda55d45528ed273d266d55a71af7291fe3fc76854deffb9168cc1a917d0b07a7d5a178c7e9537c99e6d2b57
  languageName: node
  linkType: hard

"string.prototype.repeat@npm:^1.0.0":
  version: 1.0.0
  resolution: "string.prototype.repeat@npm:1.0.0"
  dependencies:
    define-properties: ^1.1.3
    es-abstract: ^1.17.5
  checksum: 95dfc514ed7f328d80a066dabbfbbb1615c3e51490351085409db2eb7cbfed7ea29fdadaf277647fbf9f4a1e10e6dd9e95e78c0fd2c4e6bb6723ea6e59401004
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.10":
  version: 1.2.10
  resolution: "string.prototype.trim@npm:1.2.10"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-data-property: ^1.1.4
    define-properties: ^1.2.1
    es-abstract: ^1.23.5
    es-object-atoms: ^1.0.0
    has-property-descriptors: ^1.0.2
  checksum: 87659cd8561237b6c69f5376328fda934693aedde17bb7a2c57008e9d9ff992d0c253a391c7d8d50114e0e49ff7daf86a362f7961cf92f7564cd01342ca2e385
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.9":
  version: 1.0.9
  resolution: "string.prototype.trimend@npm:1.0.9"
  dependencies:
    call-bind: ^1.0.8
    call-bound: ^1.0.2
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: cb86f639f41d791a43627784be2175daa9ca3259c7cb83e7a207a729909b74f2ea0ec5d85de5761e6835e5f443e9420c6ff3f63a845378e4a61dd793177bc287
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: df1007a7f580a49d692375d996521dc14fd103acda7f3034b3c558a60b82beeed3a64fa91e494e164581793a8ab0ae2f59578a49896a7af6583c1f20472bce96
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1, string_decoder@npm:^1.3.0":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"styled-jsx@npm:5.1.6":
  version: 5.1.6
  resolution: "styled-jsx@npm:5.1.6"
  dependencies:
    client-only: 0.0.1
  peerDependencies:
    react: ">= 16.8.0 || 17.x.x || ^18.0.0-0 || ^19.0.0-0"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    babel-plugin-macros:
      optional: true
  checksum: 879ad68e3e81adcf4373038aaafe55f968294955593660e173fbf679204aff158c59966716a60b29af72dc88795cfb2c479b6d2c3c87b2b2d282f3e27cc66461
  languageName: node
  linkType: hard

"superstruct@npm:^1.0.3":
  version: 1.0.4
  resolution: "superstruct@npm:1.0.4"
  checksum: 2e070994cc4998a753c3f0215449d6de01ffb8180e4f46527f559ffbc2ebcc40fcf428f545ccd355921ef2920db7d138a96258ae35c788e6c24b2aa8bb1695cb
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"tailwindcss@npm:4.1.11, tailwindcss@npm:^4":
  version: 4.1.11
  resolution: "tailwindcss@npm:4.1.11"
  checksum: db5883219aa7b4192aa13a028547867c5d6c18b3438b99c25ac7af64c3f9475d1b96970cb27f52f6f53789bb91d8fc4ed8e7f181c73ba7dda721cf5a44983804
  languageName: node
  linkType: hard

"tapable@npm:^2.2.0":
  version: 2.2.2
  resolution: "tapable@npm:2.2.2"
  checksum: 781b3666f4454eb506fd2bcd985c1994f2b93884ea88a7a2a5be956cad8337b31128a7591e771f7aab8e247993b2a0887d360a2d4f54382902ed89994c102740
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": ^4.0.0
    chownr: ^3.0.0
    minipass: ^7.1.2
    minizlib: ^3.0.1
    mkdirp: ^3.0.1
    yallist: ^5.0.0
  checksum: 8485350c0688331c94493031f417df069b778aadb25598abdad51862e007c39d1dd5310702c7be4a6784731a174799d8885d2fde0484269aea205b724d7b2ffa
  languageName: node
  linkType: hard

"thread-stream@npm:^0.15.1":
  version: 0.15.2
  resolution: "thread-stream@npm:0.15.2"
  dependencies:
    real-require: ^0.1.0
  checksum: 0547795a8f357ba1ac0dba29c71f965182e29e21752951a04a7167515ee37524bfba6c410f31e65a01a8d3e5b93400b812889aa09523e38ce4d744c894ffa6c0
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12, tinyglobby@npm:^0.2.13":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: ^6.4.4
    picomatch: ^4.0.2
  checksum: 261e986e3f2062dec3a582303bad2ce31b4634b9348648b46828c000d464b012cf474e38f503312367d4117c3f2f18611992738fca684040758bba44c24de522
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 726321c5eaf41b5002e17ffbd1fb7245999a073e8979085dacd47c4b4e8068ff5777142fc6726d6ca1fd2ff16921b48788b87225cbc57c72636f6efa8efbffe3
  languageName: node
  linkType: hard

"ts-api-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "ts-api-utils@npm:2.1.0"
  peerDependencies:
    typescript: ">=4.8.4"
  checksum: 5b1ef89105654d93d67582308bd8dfe4bbf6874fccbcaa729b08fbb00a940fd4c691ca6d0d2b18c3c70878d9a7e503421b7cc473dbc3d0d54258b86401d4b15d
  languageName: node
  linkType: hard

"tsconfig-paths@npm:^3.15.0":
  version: 3.15.0
  resolution: "tsconfig-paths@npm:3.15.0"
  dependencies:
    "@types/json5": ^0.0.29
    json5: ^1.0.2
    minimist: ^1.2.6
    strip-bom: ^3.0.0
  checksum: 59f35407a390d9482b320451f52a411a256a130ff0e7543d18c6f20afab29ac19fbe55c360a93d6476213cc335a4d76ce90f67df54c4e9037f7d240920832201
  languageName: node
  linkType: hard

"tslib@npm:1.14.1":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: dbe628ef87f66691d5d2959b3e41b9ca0045c3ee3c7c7b906cc1e328b39f199bb1ad9e671c39025bd56122ac57dfbf7385a94843b1cc07c60a4db74795829acd
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.4.0, tslib@npm:^2.6.0, tslib@npm:^2.8.0":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: e4aba30e632b8c8902b47587fd13345e2827fa639e7c3121074d5ee0880723282411a8838f830b55100cbe4517672f84a2472667d355b81e8af165a55dc6203a
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-buffer@npm:1.0.3"
  dependencies:
    call-bound: ^1.0.3
    es-errors: ^1.3.0
    is-typed-array: ^1.1.14
  checksum: 3fb91f0735fb413b2bbaaca9fabe7b8fc14a3fa5a5a7546bab8a57e755be0e3788d893195ad9c2b842620592de0e68d4c077d4c2c41f04ec25b8b5bb82fa9a80
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.3":
  version: 1.0.3
  resolution: "typed-array-byte-length@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.8
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-proto: ^1.2.0
    is-typed-array: ^1.1.14
  checksum: cda9352178ebeab073ad6499b03e938ebc30c4efaea63a26839d89c4b1da9d2640b0d937fc2bd1f049eb0a38def6fbe8a061b601292ae62fe079a410ce56e3a6
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.4":
  version: 1.0.4
  resolution: "typed-array-byte-offset@npm:1.0.4"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    for-each: ^0.3.3
    gopd: ^1.2.0
    has-proto: ^1.2.0
    is-typed-array: ^1.1.15
    reflect.getprototypeof: ^1.0.9
  checksum: 670b7e6bb1d3c2cf6160f27f9f529e60c3f6f9611c67e47ca70ca5cfa24ad95415694c49d1dbfeda016d3372cab7dfc9e38c7b3e1bb8d692cae13a63d3c144d7
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.7":
  version: 1.0.7
  resolution: "typed-array-length@npm:1.0.7"
  dependencies:
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    is-typed-array: ^1.1.13
    possible-typed-array-names: ^1.0.0
    reflect.getprototypeof: ^1.0.6
  checksum: deb1a4ffdb27cd930b02c7030cb3e8e0993084c643208e52696e18ea6dd3953dfc37b939df06ff78170423d353dc8b10d5bae5796f3711c1b3abe52872b3774c
  languageName: node
  linkType: hard

"typescript@npm:^5":
  version: 5.8.3
  resolution: "typescript@npm:5.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: cb1d081c889a288b962d3c8ae18d337ad6ee88a8e81ae0103fa1fecbe923737f3ba1dbdb3e6d8b776c72bc73bfa6d8d850c0306eed1a51377d2fccdfd75d92c4
  languageName: node
  linkType: hard

"typescript@patch:typescript@^5#~builtin<compat/typescript>":
  version: 5.8.3
  resolution: "typescript@patch:typescript@npm%3A5.8.3#~builtin<compat/typescript>::version=5.8.3&hash=77c9e2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 1b503525a88ff0ff5952e95870971c4fb2118c17364d60302c21935dedcd6c37e6a0a692f350892bafcef6f4a16d09073fe461158547978d2f16fbe4cb18581c
  languageName: node
  linkType: hard

"ufo@npm:^1.5.4, ufo@npm:^1.6.1":
  version: 1.6.1
  resolution: "ufo@npm:1.6.1"
  checksum: 2c401dd45bd98ad00806e044aa8571aa2aa1762fffeae5e78c353192b257ef2c638159789f119e5d8d5e5200e34228cd1bbde871a8f7805de25daa8576fb1633
  languageName: node
  linkType: hard

"uint8arrays@npm:3.1.0":
  version: 3.1.0
  resolution: "uint8arrays@npm:3.1.0"
  dependencies:
    multiformats: ^9.4.2
  checksum: 77fe0c8644417a849f5cfc0e5a5308c65e3b779a56f816dd27b8f60f7fac1ac7626f57c9abacec77d147beb5da8401b86438b1591d93cae7f7511a3211cc01b3
  languageName: node
  linkType: hard

"uint8arrays@npm:^3.0.0":
  version: 3.1.1
  resolution: "uint8arrays@npm:3.1.1"
  dependencies:
    multiformats: ^9.4.2
  checksum: b93b6c3f0a526b116799f3a3409bd4b5d5553eb3e73e485998ece7974742254fbc0d2f7988dd21ac86c4b974552f45d9ae9cf9cba9647e529f8eb1fdd2ed84d0
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.1.0":
  version: 1.1.0
  resolution: "unbox-primitive@npm:1.1.0"
  dependencies:
    call-bound: ^1.0.3
    has-bigints: ^1.0.2
    has-symbols: ^1.1.0
    which-boxed-primitive: ^1.1.1
  checksum: 729f13b84a5bfa3fead1d8139cee5c38514e63a8d6a437819a473e241ba87eeb593646568621c7fc7f133db300ef18d65d1a5a60dc9c7beb9000364d93c581df
  languageName: node
  linkType: hard

"uncrypto@npm:^0.1.3":
  version: 0.1.3
  resolution: "uncrypto@npm:0.1.3"
  checksum: 07160e08806dd6cea16bb96c3fd54cd70fc801e02fc3c6f86980144d15c9ebbd1c55587f7280a207b3af6cd34901c0d0b77ada5a02c2f7081a033a05acf409e2
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 46331c7d6016bf85b3e8f20c159d62f5ae471aba1eb3dc52fff35a0259d58dcc7d592d4cc4f00c5f9243fa738a11cfa48bd20203040d4a9e6bc25e807fab7ab3
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: ^5.0.0
  checksum: 6a62094fcac286b9ec39edbd1f8f64ff92383baa430af303dfed1ffda5e47a08a6b316408554abfddd9730c78b6106bef4ca4d02c1231a735ddd56ced77573df
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 222d0322bc7bbf6e45c08967863212398313ef73423f4125e075f893a02405a5ffdbaaf150f7dd1e99f8861348a486dd079186d27c5f2c60e465b7dcbb1d3e5b
  languageName: node
  linkType: hard

"unrs-resolver@npm:^1.6.2":
  version: 1.9.2
  resolution: "unrs-resolver@npm:1.9.2"
  dependencies:
    "@unrs/resolver-binding-android-arm-eabi": 1.9.2
    "@unrs/resolver-binding-android-arm64": 1.9.2
    "@unrs/resolver-binding-darwin-arm64": 1.9.2
    "@unrs/resolver-binding-darwin-x64": 1.9.2
    "@unrs/resolver-binding-freebsd-x64": 1.9.2
    "@unrs/resolver-binding-linux-arm-gnueabihf": 1.9.2
    "@unrs/resolver-binding-linux-arm-musleabihf": 1.9.2
    "@unrs/resolver-binding-linux-arm64-gnu": 1.9.2
    "@unrs/resolver-binding-linux-arm64-musl": 1.9.2
    "@unrs/resolver-binding-linux-ppc64-gnu": 1.9.2
    "@unrs/resolver-binding-linux-riscv64-gnu": 1.9.2
    "@unrs/resolver-binding-linux-riscv64-musl": 1.9.2
    "@unrs/resolver-binding-linux-s390x-gnu": 1.9.2
    "@unrs/resolver-binding-linux-x64-gnu": 1.9.2
    "@unrs/resolver-binding-linux-x64-musl": 1.9.2
    "@unrs/resolver-binding-wasm32-wasi": 1.9.2
    "@unrs/resolver-binding-win32-arm64-msvc": 1.9.2
    "@unrs/resolver-binding-win32-ia32-msvc": 1.9.2
    "@unrs/resolver-binding-win32-x64-msvc": 1.9.2
    napi-postinstall: ^0.2.4
  dependenciesMeta:
    "@unrs/resolver-binding-android-arm-eabi":
      optional: true
    "@unrs/resolver-binding-android-arm64":
      optional: true
    "@unrs/resolver-binding-darwin-arm64":
      optional: true
    "@unrs/resolver-binding-darwin-x64":
      optional: true
    "@unrs/resolver-binding-freebsd-x64":
      optional: true
    "@unrs/resolver-binding-linux-arm-gnueabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm-musleabihf":
      optional: true
    "@unrs/resolver-binding-linux-arm64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-arm64-musl":
      optional: true
    "@unrs/resolver-binding-linux-ppc64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-riscv64-musl":
      optional: true
    "@unrs/resolver-binding-linux-s390x-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-gnu":
      optional: true
    "@unrs/resolver-binding-linux-x64-musl":
      optional: true
    "@unrs/resolver-binding-wasm32-wasi":
      optional: true
    "@unrs/resolver-binding-win32-arm64-msvc":
      optional: true
    "@unrs/resolver-binding-win32-ia32-msvc":
      optional: true
    "@unrs/resolver-binding-win32-x64-msvc":
      optional: true
  checksum: 56385373ab1d02132a459286705d3df93990fa4999d686358a7de550bb410ac6c6b16c0a1dfebd28a5baf70c1855d2c2e8b260b4dd946e825efc9961c31b45f8
  languageName: node
  linkType: hard

"unstorage@npm:^1.9.0":
  version: 1.16.0
  resolution: "unstorage@npm:1.16.0"
  dependencies:
    anymatch: ^3.1.3
    chokidar: ^4.0.3
    destr: ^2.0.5
    h3: ^1.15.2
    lru-cache: ^10.4.3
    node-fetch-native: ^1.6.6
    ofetch: ^1.4.1
    ufo: ^1.6.1
  peerDependencies:
    "@azure/app-configuration": ^1.8.0
    "@azure/cosmos": ^4.2.0
    "@azure/data-tables": ^13.3.0
    "@azure/identity": ^4.6.0
    "@azure/keyvault-secrets": ^4.9.0
    "@azure/storage-blob": ^12.26.0
    "@capacitor/preferences": ^6.0.3 || ^7.0.0
    "@deno/kv": ">=0.9.0"
    "@netlify/blobs": ^6.5.0 || ^7.0.0 || ^8.1.0
    "@planetscale/database": ^1.19.0
    "@upstash/redis": ^1.34.3
    "@vercel/blob": ">=0.27.1"
    "@vercel/kv": ^1.0.1
    aws4fetch: ^1.0.20
    db0: ">=0.2.1"
    idb-keyval: ^6.2.1
    ioredis: ^5.4.2
    uploadthing: ^7.4.4
  peerDependenciesMeta:
    "@azure/app-configuration":
      optional: true
    "@azure/cosmos":
      optional: true
    "@azure/data-tables":
      optional: true
    "@azure/identity":
      optional: true
    "@azure/keyvault-secrets":
      optional: true
    "@azure/storage-blob":
      optional: true
    "@capacitor/preferences":
      optional: true
    "@deno/kv":
      optional: true
    "@netlify/blobs":
      optional: true
    "@planetscale/database":
      optional: true
    "@upstash/redis":
      optional: true
    "@vercel/blob":
      optional: true
    "@vercel/kv":
      optional: true
    aws4fetch:
      optional: true
    db0:
      optional: true
    idb-keyval:
      optional: true
    ioredis:
      optional: true
    uploadthing:
      optional: true
  checksum: fced28aad75d047dad25b90a8c308db624a8f3113819fc5b6240168914e7c32f4e433550a510c80e751b01434c34d3f02bee90adb950c36441225b66118155ef
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"use-sync-external-store@npm:1.2.0":
  version: 1.2.0
  resolution: "use-sync-external-store@npm:1.2.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: 5c639e0f8da3521d605f59ce5be9e094ca772bd44a4ce7322b055a6f58eeed8dda3c94cabd90c7a41fb6fa852210092008afe48f7038792fd47501f33299116a
  languageName: node
  linkType: hard

"use-sync-external-store@npm:1.4.0":
  version: 1.4.0
  resolution: "use-sync-external-store@npm:1.4.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: dc3843a1b59ac8bd01417bd79498d4c688d5df8bf4801be50008ef4bfaacb349058c0b1605b5b43c828e0a2d62722d7e861573b3f31cea77a7f23e8b0fc2f7e3
  languageName: node
  linkType: hard

"utf-8-validate@npm:^5.0.2":
  version: 5.0.10
  resolution: "utf-8-validate@npm:5.0.10"
  dependencies:
    node-gyp: latest
    node-gyp-build: ^4.3.0
  checksum: 5579350a023c66a2326752b6c8804cc7b39dcd251bb088241da38db994b8d78352e388dcc24ad398ab98385ba3c5ffcadb6b5b14b2637e43f767869055e46ba6
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"util@npm:^0.12.4":
  version: 0.12.5
  resolution: "util@npm:0.12.5"
  dependencies:
    inherits: ^2.0.3
    is-arguments: ^1.0.4
    is-generator-function: ^1.0.7
    is-typed-array: ^1.1.3
    which-typed-array: ^1.1.2
  checksum: 705e51f0de5b446f4edec10739752ac25856541e0254ea1e7e45e5b9f9b0cb105bc4bd415736a6210edc68245a7f903bf085ffb08dd7deb8a0e847f60538a38a
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 5575a8a75c13120e2f10e6ddc801b2c7ed7d8f3c8ac22c7ed0c7b2ba6383ec0abda88c905085d630e251719e0777045ae3236f04c812184b7c765f63a70e58df
  languageName: node
  linkType: hard

"uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 39931f6da74e307f51c0fb463dc2462807531dc80760a9bff1e35af4316131b4fc3203d16da60ae33f07fdca5b56f3f1dd662da0c99fea9aaeab2004780cc5f4
  languageName: node
  linkType: hard

"valtio@npm:1.13.2":
  version: 1.13.2
  resolution: "valtio@npm:1.13.2"
  dependencies:
    derive-valtio: 0.1.0
    proxy-compare: 2.6.0
    use-sync-external-store: 1.2.0
  peerDependencies:
    "@types/react": ">=16.8"
    react: ">=16.8"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    react:
      optional: true
  checksum: eb0f12d85c90bd808379b30430cabb1a224749343d8b750bb88f69072825b3e2d6be057ef1d3952253357713871ff079ac9a3aa4f48265bc49c6e3949d8c0919
  languageName: node
  linkType: hard

"viem@npm:2.23.2":
  version: 2.23.2
  resolution: "viem@npm:2.23.2"
  dependencies:
    "@noble/curves": 1.8.1
    "@noble/hashes": 1.7.1
    "@scure/bip32": 1.6.2
    "@scure/bip39": 1.5.4
    abitype: 1.0.8
    isows: 1.0.6
    ox: 0.6.7
    ws: 8.18.0
  peerDependencies:
    typescript: ">=5.0.4"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 4ee79122e4d484c3f5585ced9a2ddeaa935fd13779010ef8869c39c70f78eab2af0408c765543088e052fd8c3c46d8e79225d64b68563b096d8de558daf6d115
  languageName: node
  linkType: hard

"viem@npm:>=2.29.0, viem@npm:^2.1.1, viem@npm:^2.31.4":
  version: 2.31.6
  resolution: "viem@npm:2.31.6"
  dependencies:
    "@noble/curves": 1.9.2
    "@noble/hashes": 1.8.0
    "@scure/bip32": 1.7.0
    "@scure/bip39": 1.6.0
    abitype: 1.0.8
    isows: 1.0.7
    ox: 0.8.1
    ws: 8.18.2
  peerDependencies:
    typescript: ">=5.0.4"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 2df78251cb9e86a2c4ae17818ba082334ec3abc9faf2bf7158877fc80a21bdaa196ec917f7f0e280a6008db695be57f6e1b8b5b2d2ebd6d5a3ed3e159ef48d80
  languageName: node
  linkType: hard

"wagmi@npm:^2.15.6":
  version: 2.15.6
  resolution: "wagmi@npm:2.15.6"
  dependencies:
    "@wagmi/connectors": 5.8.5
    "@wagmi/core": 2.17.3
    use-sync-external-store: 1.4.0
  peerDependencies:
    "@tanstack/react-query": ">=5.0.0"
    react: ">=18"
    typescript: ">=5.0.4"
    viem: 2.x
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: ea263ae1e73270ec320559e6ed9867d1ee40e78fa8c25c804f50a080478e90e1dce16c87a1054587286cbe9549ac5af841fca5768cb5155f2618d61e34ebe77d
  languageName: node
  linkType: hard

"web@workspace:.":
  version: 0.0.0-use.local
  resolution: "web@workspace:."
  dependencies:
    "@eslint/eslintrc": ^3
    "@tailwindcss/postcss": ^4
    "@tanstack/react-query": ^5.81.5
    "@types/node": ^20
    "@types/react": ^19
    "@types/react-dom": ^19
    eslint: ^9
    eslint-config-next: 15.3.4
    next: 15.3.4
    react: ^19.0.0
    react-dom: ^19.0.0
    tailwindcss: ^4
    typescript: ^5
    viem: ^2.31.4
    wagmi: ^2.15.6
  languageName: unknown
  linkType: soft

"webextension-polyfill@npm:>=0.10.0 <1.0":
  version: 0.12.0
  resolution: "webextension-polyfill@npm:0.12.0"
  checksum: fc2166c8c9d3f32d7742727394092ff1a1eb19cbc4e5a73066d57f9bff1684e38342b90fabd23981e7295e904c536e8509552a64e989d217dae5de6ddca73532
  languageName: node
  linkType: hard

"webextension-polyfill@npm:^0.10.0":
  version: 0.10.0
  resolution: "webextension-polyfill@npm:0.10.0"
  checksum: 4a59036bda571360c2c0b2fb03fe1dc244f233946bcf9a6766f677956c40fd14d270aaa69cdba95e4ac521014afbe4008bfa5959d0ac39f91c990eb206587f91
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: c92a0a6ab95314bde9c32e1d0a6dfac83b578f8fa5f21e675bc2706ed6981bc26b7eb7e6a1fab158e5ce4adf9caa4a0aee49a52505d4d13c7be545f15021b17c
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: b8daed4ad3356cc4899048a15b2c143a9aed0dfae1f611ebd55073310c7b910f522ad75d727346ad64203d7e6c79ef25eafd465f4d12775ca44b90fa82ed9e2c
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.1.0, which-boxed-primitive@npm:^1.1.1":
  version: 1.1.1
  resolution: "which-boxed-primitive@npm:1.1.1"
  dependencies:
    is-bigint: ^1.1.0
    is-boolean-object: ^1.2.1
    is-number-object: ^1.1.1
    is-string: ^1.1.1
    is-symbol: ^1.1.1
  checksum: ee41d0260e4fd39551ad77700c7047d3d281ec03d356f5e5c8393fe160ba0db53ef446ff547d05f76ffabfd8ad9df7c9a827e12d4cccdbc8fccf9239ff8ac21e
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.2.1":
  version: 1.2.1
  resolution: "which-builtin-type@npm:1.2.1"
  dependencies:
    call-bound: ^1.0.2
    function.prototype.name: ^1.1.6
    has-tostringtag: ^1.0.2
    is-async-function: ^2.0.0
    is-date-object: ^1.1.0
    is-finalizationregistry: ^1.1.0
    is-generator-function: ^1.0.10
    is-regex: ^1.2.1
    is-weakref: ^1.0.2
    isarray: ^2.0.5
    which-boxed-primitive: ^1.1.0
    which-collection: ^1.0.2
    which-typed-array: ^1.1.16
  checksum: 7a3617ba0e7cafb795f74db418df889867d12bce39a477f3ee29c6092aa64d396955bf2a64eae3726d8578440e26777695544057b373c45a8bcf5fbe920bf633
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: ^2.0.3
    is-set: ^2.0.3
    is-weakmap: ^2.0.2
    is-weakset: ^2.0.3
  checksum: c51821a331624c8197916598a738fc5aeb9a857f1e00d89f5e4c03dc7c60b4032822b8ec5696d28268bb83326456a8b8216344fb84270d18ff1d7628051879d9
  languageName: node
  linkType: hard

"which-module@npm:^2.0.0":
  version: 2.0.1
  resolution: "which-module@npm:2.0.1"
  checksum: 1967b7ce17a2485544a4fdd9063599f0f773959cca24176dbe8f405e55472d748b7c549cd7920ff6abb8f1ab7db0b0f1b36de1a21c57a8ff741f4f1e792c52be
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.16, which-typed-array@npm:^1.1.19, which-typed-array@npm:^1.1.2":
  version: 1.1.19
  resolution: "which-typed-array@npm:1.1.19"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.8
    call-bound: ^1.0.4
    for-each: ^0.3.5
    get-proto: ^1.0.1
    gopd: ^1.2.0
    has-tostringtag: ^1.0.2
  checksum: 162d2a07f68ea323f88ed9419861487ce5d02cb876f2cf9dd1e428d04a63133f93a54f89308f337b27cabd312ee3d027cae4a79002b2f0a85b79b9ef4c190670
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: 6ec99e89ba32c7e748b8a3144e64bfc74aa63e2b2eacbb61a0060ad0b961eb1a632b08fb1de067ed59b002cec3e21de18299216ebf2325ef0f78e0f121e14e90
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: f93ba3586fc181f94afdaff3a6fef27920b4b6d9eaefed0f428f8e07adea2a7f54a5f2830ce59406c8416f033f86902b91eb824072354645eea687dff3691ccb
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: 6cd96a410161ff617b63581a08376f0cb9162375adeb7956e10c8cd397821f7eb2a6de24eb22a0b28401300bf228c86e50617cd568209b5f6775b93c97d2fe3a
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"ws@npm:8.18.0":
  version: 8.18.0
  resolution: "ws@npm:8.18.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 91d4d35bc99ff6df483bdf029b9ea4bfd7af1f16fc91231a96777a63d263e1eabf486e13a2353970efc534f9faa43bdbf9ee76525af22f4752cbc5ebda333975
  languageName: node
  linkType: hard

"ws@npm:8.18.2":
  version: 8.18.2
  resolution: "ws@npm:8.18.2"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: e38beae19ba4d68577ec24eb34fbfab376333fedd10f99b07511a8e842e22dbc102de39adac333a18e4c58868d0703cd5f239b04b345e22402d0ed8c34ea0aa0
  languageName: node
  linkType: hard

"ws@npm:^7.5.1":
  version: 7.5.10
  resolution: "ws@npm:7.5.10"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: f9bb062abf54cc8f02d94ca86dcd349c3945d63851f5d07a3a61c2fcb755b15a88e943a63cf580cbdb5b74436d67ef6b67f745b8f7c0814e411379138e1863cb
  languageName: node
  linkType: hard

"ws@npm:~8.17.1":
  version: 8.17.1
  resolution: "ws@npm:8.17.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 442badcce1f1178ec87a0b5372ae2e9771e07c4929a3180321901f226127f252441e8689d765aa5cfba5f50ac60dd830954afc5aeae81609aefa11d3ddf5cecf
  languageName: node
  linkType: hard

"xmlhttprequest-ssl@npm:~2.1.1":
  version: 2.1.2
  resolution: "xmlhttprequest-ssl@npm:2.1.2"
  checksum: f8ecb894301dd024804669245888b3d5ceed2dfcdb4fddb0e38d811a07a2e3c227535f29b269eccf92005819457db3f270d2ff98df516abf95cfad1b9759512d
  languageName: node
  linkType: hard

"xtend@npm:^4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"y18n@npm:^4.0.0":
  version: 4.0.3
  resolution: "y18n@npm:4.0.3"
  checksum: 014dfcd9b5f4105c3bb397c1c8c6429a9df004aa560964fb36732bfb999bfe83d45ae40aeda5b55d21b1ee53d8291580a32a756a443e064317953f08025b1aa4
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: eba51182400b9f35b017daa7f419f434424410691bbc5de4f4240cc830fdef906b504424992700dc047f16b4d99100a6f8b8b11175c193f38008e9c96322b6a5
  languageName: node
  linkType: hard

"yargs-parser@npm:^18.1.2":
  version: 18.1.3
  resolution: "yargs-parser@npm:18.1.3"
  dependencies:
    camelcase: ^5.0.0
    decamelize: ^1.2.0
  checksum: 60e8c7d1b85814594d3719300ecad4e6ae3796748b0926137bfec1f3042581b8646d67e83c6fc80a692ef08b8390f21ddcacb9464476c39bbdf52e34961dd4d9
  languageName: node
  linkType: hard

"yargs@npm:^15.3.1":
  version: 15.4.1
  resolution: "yargs@npm:15.4.1"
  dependencies:
    cliui: ^6.0.0
    decamelize: ^1.2.0
    find-up: ^4.1.0
    get-caller-file: ^2.0.1
    require-directory: ^2.1.1
    require-main-filename: ^2.0.0
    set-blocking: ^2.0.0
    string-width: ^4.2.0
    which-module: ^2.0.0
    y18n: ^4.0.0
    yargs-parser: ^18.1.2
  checksum: 40b974f508d8aed28598087720e086ecd32a5fd3e945e95ea4457da04ee9bdb8bdd17fd91acff36dc5b7f0595a735929c514c40c402416bbb87c03f6fb782373
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zod@npm:3.22.4":
  version: 3.22.4
  resolution: "zod@npm:3.22.4"
  checksum: 80bfd7f8039b24fddeb0718a2ec7c02aa9856e4838d6aa4864335a047b6b37a3273b191ef335bf0b2002e5c514ef261ffcda5a589fb084a48c336ffc4cdbab7f
  languageName: node
  linkType: hard

"zustand@npm:5.0.0":
  version: 5.0.0
  resolution: "zustand@npm:5.0.0"
  peerDependencies:
    "@types/react": ">=18.0.0"
    immer: ">=9.0.6"
    react: ">=18.0.0"
    use-sync-external-store: ">=1.2.0"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    immer:
      optional: true
    react:
      optional: true
    use-sync-external-store:
      optional: true
  checksum: dc7414de234f9d2c0afad472d6971e9ac32281292faa8ee0910521cad063f84eeeb6f792efab068d6750dab5854fb1a33ac6e9294b796925eb680a59fc1b42f9
  languageName: node
  linkType: hard
