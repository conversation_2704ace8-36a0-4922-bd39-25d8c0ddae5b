# 第一阶段：依赖安装和构建
FROM coinflow/bun-base AS build
WORKDIR /app

# 复制依赖文件并安装
COPY package.json bun.lock ./
RUN bun install --frozen-lockfile

# 复制全部源代码并构建
COPY . .
RUN bun run build

# 第二阶段：生产运行
FROM coinflow/bun-base AS runner
WORKDIR /app

# 复制构建产物和静态资源
COPY --from=build /app/public ./public
COPY --from=build /app/.next/standalone ./
COPY --from=build /app/.next/static ./.next/static

# 暴露端口
EXPOSE 3000

# 环境变量
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 启动应用
CMD ["bun", "run", "start"]
