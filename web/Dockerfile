# 简化的 Dockerfile for Next.js with npm
FROM coinflow/bun-base AS base
WORKDIR /app

# 安装依赖阶段
FROM base AS deps
WORKDIR /app

# 复制包管理文件
COPY package.json bun.lock ./

# 安装依赖
RUN bun install --frozen-lockfile

# 构建阶段
FROM base AS builder
WORKDIR /app

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules

# 复制源代码
COPY . .

# 构建应用
RUN bun run build

# 生产运行阶段
FROM base AS runner
WORKDIR /app

# 复制必要文件
COPY --from=builder /app/public ./public

# 创建 .next 目录
RUN mkdir .next

# 复制构建产物
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 启动应用（如果你用 bun 启动 Next.js，通常是 bun run start）
CMD ["bun", "run", "start"]
