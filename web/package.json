{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"@tanstack/react-query": "^5.81.5", "@types/better-sqlite3": "^7.6.13", "@wagmi/connectors": "^5.8.5", "@walletconnect/ethereum-provider": "^2.21.4", "better-sqlite3": "^12.2.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "viem": "^2.31.4", "wagmi": "^2.15.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.53.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}