import { getDatabase } from './src/lib/database.ts';
import { getIndexer } from './src/lib/indexer.ts';

async function testIndexerFix() {
    console.log('🧪 测试索引器修复...');
    
    try {
        // 测试数据库连接
        const db = getDatabase();
        console.log('✅ 数据库连接成功');
        
        // 检查当前状态
        const currentBlock = db.getLastProcessedBlock();
        console.log('📊 当前处理的区块:', currentBlock);
        
        // 如果没有状态，测试初始化
        if (currentBlock === null) {
            console.log('🔧 数据库状态为空，测试索引器初始化...');
            
            const indexer = getIndexer();
            await indexer.start();
            
            // 检查初始化后的状态
            const newBlock = db.getLastProcessedBlock();
            console.log('📊 初始化后的区块:', newBlock);
            
            // 停止索引器
            indexer.stop();
        } else {
            console.log('✅ 数据库已有状态，无需重新初始化');
        }
        
        console.log('🎉 测试完成！');
        process.exit(0);
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
        process.exit(1);
    }
}

testIndexerFix(); 