import { test, expect } from '@playwright/test';

test.describe('Bitroot Token Exchange E2E Test', () => {
  test('should load and function correctly', async ({ page }) => {
    // Step 1: Navigate to the application homepage
    await page.goto('/');

    // Step 2: Verify page loads with correct title and content
    await expect(page).toHaveTitle('Bitroot Token Exchange');
    await expect(page.getByRole('heading', { name: 'Bitroot' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Token Exchange' })).toBeVisible();
    await expect(page.getByText('Exchange your BRT tokens for BRT(one) tokens')).toBeVisible();

    // Verify key UI elements are present
    await expect(page.getByText('From', { exact: true })).toBeVisible();
    await expect(page.getByText('To', { exact: true })).toBeVisible();
    await expect(page.getByText('BRT', { exact: true })).toBeVisible();
    await expect(page.getByText('BRT(one)', { exact: true })).toBeVisible();
    await expect(page.getByText('Balance: 0.0 BRT')).toBeVisible();

    // Step 3: Test wallet connection modal opens and closes
    const connectWalletButton = page.getByRole('banner').getByRole('button', { name: 'Connect Wallet' });
    await expect(connectWalletButton).toBeVisible();
    
    // Open wallet modal
    await connectWalletButton.click();
    await expect(page.getByRole('heading', { name: 'Connect Wallet' })).toBeVisible();
    
    // Verify wallet options are present
    await expect(page.getByRole('button', { name: 'M MetaMask' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'O OKX Wallet' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'T TokenPocket' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'W WalletConnect' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'C Coinbase Wallet' })).toBeVisible();
    
    // Close modal
    await page.getByRole('button', { name: '✕' }).click();
    await expect(page.getByRole('heading', { name: 'Connect Wallet' })).not.toBeVisible();

    // Step 4: Test amount input functionality
    const amountInput = page.getByRole('textbox', { name: '0.0' });
    await expect(amountInput).toBeVisible();
    
    // Test input
    await amountInput.fill('100');
    await expect(amountInput).toHaveValue('100');

    // Step 5: Test MAX button behavior
    const maxButton = page.getByRole('button', { name: 'MAX' });
    await expect(maxButton).toBeVisible();
    await maxButton.click();
    // Note: MAX button won't change value when wallet is not connected (balance is 0)

    // Step 6: Test navigation links work correctly
    const historyLink = page.getByRole('link', { name: 'History' });
    await expect(historyLink).toBeVisible();
    await historyLink.click();
    await expect(page).toHaveURL('/#history');

    const exchangeLink = page.getByRole('link', { name: 'Exchange' });
    await exchangeLink.click();
    await expect(page).toHaveURL('/#exchange');

    // Step 7: Verify transaction history section displays correctly
    await expect(page.getByRole('heading', { name: 'Transaction History' })).toBeVisible();
    await expect(page.getByText('Please connect your wallet to view transaction history.')).toBeVisible();

    // Verify exchange form elements
    await expect(page.getByText('Amount to Exchange')).toBeVisible();
    await expect(page.getByText('Exchange Rate')).toBeVisible();
    await expect(page.getByText('Network Fee')).toBeVisible();
    await expect(page.getByText('You will receive')).toBeVisible();

    // Verify main action button
    const mainActionButton = page.getByRole('main').getByRole('button', { name: 'Connect Wallet' });
    await expect(mainActionButton).toBeVisible();
    await expect(mainActionButton).toBeDisabled();

    // Verify footer
    await expect(page.getByText('© 2025 Bitroot. All rights reserved.')).toBeVisible();
  });

  test('should handle responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    await expect(page.getByRole('heading', { name: 'Bitroot' })).toBeVisible();
    await expect(page.getByRole('heading', { name: 'Token Exchange' })).toBeVisible();
  });

  test('should display correct exchange information', async ({ page }) => {
    await page.goto('/');
    
    // Verify exchange rate display
    await expect(page.getByText('1 BRT = ... BRT(one)')).toBeVisible();
    
    // Verify network fee display
    await expect(page.getByText('~0.0001 ARB')).toBeVisible();
    
    // Verify receive amount display
    await expect(page.getByText('0.0 BRT(one)', { exact: true }).first()).toBeVisible();
  });
});
