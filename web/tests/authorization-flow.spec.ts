import { test, expect } from '@playwright/test'

test.describe('ERC20 Authorization Flow', () => {
  test('should show Approve BRT button when no authorization', async ({ page }) => {
    // Navigate to the application
    await page.goto('/')
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle')
    
    // Check if the page loads correctly
    await expect(page).toHaveTitle(/Bitroot Token Exchange/)
    
    // Look for the exchange form
    const exchangeForm = page.locator('[data-testid="exchange-form"]').or(
      page.locator('div').filter({ hasText: 'Exchange' }).first()
    )
    
    // Check if Connect Wallet button is present (when not connected)
    const connectButton = page.getByRole('button', { name: /Connect Wallet/i })
    if (await connectButton.isVisible()) {
      console.log('✓ Connect Wallet button is visible when not connected')
    }
    
    // Look for input field
    const amountInput = page.locator('input[placeholder*="exchange"]').or(
      page.locator('input[type="text"]').first()
    )
    
    if (await amountInput.isVisible()) {
      console.log('✓ Amount input field is visible')
      
      // Try to input an amount (this might be disabled if not connected)
      try {
        await amountInput.fill('100')
        console.log('✓ Successfully entered amount: 100')
      } catch (error) {
        console.log('ℹ Amount input is disabled (wallet not connected)')
      }
    }
    
    // Check for any buttons that might indicate authorization state
    const approveButton = page.getByRole('button', { name: /Approve.*BRT/i })
    const exchangeButton = page.getByRole('button', { name: /Exchange/i })
    
    if (await approveButton.isVisible()) {
      console.log('✓ Approve BRT button is visible')
    } else if (await exchangeButton.isVisible()) {
      console.log('⚠ Exchange button is visible (might indicate authorization issue)')
    }
    
    // Take a screenshot for manual verification
    await page.screenshot({ path: 'test-results/authorization-flow-initial.png', fullPage: true })
    
    console.log('✓ Authorization flow test completed - check screenshot for visual verification')
  })
  
  test('should display correct button text based on connection state', async ({ page }) => {
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // Check for different button states
    const buttonStates = [
      'Connect Wallet',
      'Switch to',
      'Approve BRT', 
      'Exchange',
      'Approving...',
      'Exchanging...'
    ]
    
    let foundStates: string[] = []
    
    for (const state of buttonStates) {
      const button = page.getByRole('button', { name: new RegExp(state, 'i') })
      if (await button.isVisible()) {
        foundStates.push(state)
      }
    }
    
    console.log('Found button states:', foundStates)
    
    // At least one button state should be present
    expect(foundStates.length).toBeGreaterThan(0)
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/button-states.png', fullPage: true })
  })
})
