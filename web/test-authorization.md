# ERC20 授权流程测试指南

## 测试目标
验证 ERC20 授权流程是否正确实现，确保用户必须先 approve 才能 exchange。

## 测试步骤

### 1. 初始状态测试
- 打开应用：http://localhost:3001
- 连接钱包（MetaMask）
- 切换到支持的网络（Bitroot Test Network 1337）
- **预期结果**：
  - 用户对合约的授权额度默认为 0
  - 输入任何金额后，按钮应显示 "Approve BRT"
  - 不应该直接显示 "Exchange"

### 2. 授权流程测试
- 输入兑换金额（例如：100）
- 点击 "Approve BRT" 按钮
- **预期结果**：
  - 按钮文本变为 "Approving..."
  - 显示 loading 动画
  - MetaMask 弹出授权交易确认

### 3. 授权完成测试
- 在 MetaMask 中确认授权交易
- 等待交易确认
- **预期结果**：
  - 授权完成后，自动刷新 allowance
  - 按钮文本自动变为 "Exchange"
  - 可以进行兑换操作

### 4. 授权额度检查测试
- 如果输入的金额 > 已授权的金额
- **预期结果**：
  - 按钮应重新显示 "Approve BRT"
  - 需要重新授权更大的金额

## 调试信息
在开发环境中，打开浏览器控制台可以看到授权检查的调试信息：
```
Authorization Check: {
  amount: "100",
  allowance: "0",
  amountBN: "100000000000000000000",
  needsAuth: true
}
```

## 关键检查点
1. ✅ allowance 正确获取（调用 tokenA.allowance(userAddress, exchangeContract)）
2. ✅ needsApproval 逻辑正确（allowance < amount 时返回 true）
3. ✅ 按钮文本正确显示（Approve BRT vs Exchange）
4. ✅ 授权完成后自动刷新 allowance
5. ✅ 防止未授权直接调用 exchange 函数

## 常见问题排查
- 如果始终显示 "Exchange"：检查 needsApproval 逻辑
- 如果授权后不自动更新：检查 refetchAllowance 调用
- 如果可以未授权兑换：检查 handleExchange 中的授权检查
