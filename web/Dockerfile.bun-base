# Bun 基础镜像 - 包含所有构建依赖
# 使用方法：
# 1. 构建基础镜像：docker build -f Dockerfile.bun-base -t bun-base:latest .
# 2. 在主 Dockerfile 中使用：FROM bun-base:latest AS base

# 使用最新的 Bun 版本，确保 Node.js 18+ 兼容性
FROM oven/bun:latest

# 安装构建依赖（使用 Debian 包管理器）
RUN apt-get update -qq && \
    apt-get install -y -qq \
    python3 \
    make \
    g++ \
    git \
    curl \
    ca-certificates \
    gnupg \
    && ln -sf python3 /usr/bin/python \
    && rm -rf /var/lib/apt/lists/*

# 安装 Node.js 20.x
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV BUN_INSTALL="/root/.bun"
ENV PATH="$BUN_INSTALL/bin:$PATH"

# 标签信息
LABEL maintainer="Bitroot Team"
LABEL description="Bun base image with build tools for Node.js projects"
LABEL version="1.1.42"
