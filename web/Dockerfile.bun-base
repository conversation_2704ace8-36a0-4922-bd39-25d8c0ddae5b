# Bun 基础镜像 - 包含所有构建依赖
# 使用方法：
# 1. 构建基础镜像：docker build -f Dockerfile.bun-base -t bun-base:latest .
# 2. 在主 Dockerfile 中使用：FROM bun-base:latest AS base

# 使用最新的 Bun 版本，确保 Node.js 18+ 兼容性
FROM oven/bun:1.1.42-alpine

# 安装构建依赖（使用 Alpine 包管理器）
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl \
    nodejs \
    npm \
    && ln -sf python3 /usr/bin/python

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV BUN_INSTALL="/root/.bun"
ENV PATH="$BUN_INSTALL/bin:$PATH"

# 标签信息
LABEL maintainer="Bitroot Team"
LABEL description="Bun base image with build tools for Node.js projects"
LABEL version="1.1.42"
