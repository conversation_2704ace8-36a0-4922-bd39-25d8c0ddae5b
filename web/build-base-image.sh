#!/bin/bash

# 构建 Bun 基础镜像脚本
# 用法：./build-base-image.sh [tag]

set -e

# 默认标签
TAG=${1:-"bun-base:latest"}

echo "🔨 构建 Bun 基础镜像..."
echo "📦 标签: $TAG"

# 构建基础镜像
docker build -f Dockerfile.bun-base -t "$TAG" .

echo "✅ 基础镜像构建完成: $TAG"

# 显示镜像信息
echo "📊 镜像信息:"
docker images "$TAG"

# 测试镜像
echo "🧪 测试镜像..."
docker run --rm "$TAG" bun --version
docker run --rm "$TAG" python --version

echo "🎉 基础镜像构建并测试成功！"
echo ""
echo "💡 使用方法："
echo "1. 在 Dockerfile 中使用: FROM $TAG AS base"
echo "2. 在 Woodpecker CI 中使用: image: $TAG"
echo ""
echo "🚀 推送到仓库 (可选):"
echo "docker tag $TAG your-registry.com/$TAG"
echo "docker push your-registry.com/$TAG"
