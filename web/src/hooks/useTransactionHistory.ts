'use client'

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';

export interface Transaction {
    transactionHash: string;
    userAddress: string;
    oldTokenAmount: string;
    newTokenAmount: string;
    blockNumber: number;
    timestamp: number;
}

export function useTransactionHistory() {
    const { address, isConnected } = useAccount();
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const fetchTransactions = async (userAddress?: string) => {
        if (!userAddress) return;

        setIsLoading(true);
        setError(null);

        try {
            const response = await fetch(`/api/transactions?userAddress=${userAddress}`);
            if (!response.ok) {
                throw new Error('Failed to fetch transactions');
            }

            const data = await response.json();
            setTransactions(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Unknown error');
            console.error('Error fetching transactions:', err);
        } finally {
            setIsLoading(false);
        }
    };

    const refreshTransactions = async () => {
        if (!address) return;

        try {
            // Trigger refresh on backend
            await fetch('/api/transactions/refresh', {
                method: 'POST',
            });

            // Wait a moment for the backend to sync
            setTimeout(() => {
                fetchTransactions(address);
            }, 2000);
        } catch (err) {
            console.error('Error refreshing transactions:', err);
        }
    };

    // Auto-fetch when address changes
    useEffect(() => {
        if (isConnected && address) {
            fetchTransactions(address);
        } else {
            setTransactions([]);
        }
    }, [address, isConnected]);

    return {
        transactions,
        isLoading,
        error,
        fetchTransactions,
        refreshTransactions,
    };
} 