import { http, createConfig } from 'wagmi'
import { arbitrum, arbitrumGoerli, foundry } from 'wagmi/chains'
import { injected, walletConnect, metaMask, coinbaseWallet } from '@wagmi/connectors'
import { define<PERSON>hain } from 'viem'

const projectId = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || 'your-project-id'

// Define Bitroot Test Network
export const bitrootTest = defineChain({
  id: 1337,
  name: 'Bitroot Test Network',
  nativeCurrency: {
    decimals: 18,
    name: 'Ether',
    symbol: 'ETH',
  },
  rpcUrls: {
    default: {
      http: ['https://test-rpc.bitroot.co/'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Bitroot Explorer',
      url: 'https://explorer.bitroot.co',
    },
  },
  testnet: true,
})

// Helper function to safely access window
const getWindowProvider = (path: string) => {
  if (typeof window === 'undefined') return undefined
  return path.split('.').reduce((obj, key) => obj?.[key], window as any)
}

// Create connectors only on client side
const getConnectors = () => {
  if (typeof window === 'undefined') {
    return [metaMask()]
  }

  return [
    metaMask(),
    injected({
      target: () => ({
        id: 'okx',
        name: 'OKX Wallet',
        provider: getWindowProvider('okxwallet'),
      }),
    }),
    injected({
      target: () => ({
        id: 'tp',
        name: 'TokenPocket',
        provider: getWindowProvider('tokenpocket.ethereum'),
      }),
    }),
    walletConnect({
      projectId,
      metadata: {
        name: 'Bitroot Token Exchange',
        description: 'Exchange your BRT tokens',
        url: 'https://bitroot.exchange',
        icons: ['https://bitroot.exchange/icon.png'],
      },
    }),
    coinbaseWallet({
      appName: 'Bitroot Token Exchange',
    }),
  ]
}

export const config = createConfig({
  chains: [arbitrum, arbitrumGoerli, bitrootTest, foundry],
  connectors: getConnectors(),
  transports: {
    [arbitrum.id]: http(),
    [arbitrumGoerli.id]: http(),
    [bitrootTest.id]: http(),
    [foundry.id]: http(),
  },
})