import { http, createConfig } from 'wagmi'
import { arbitrum, arbitrumGoerli, foundry } from 'wagmi/chains'
import { injected, walletConnect, metaMask, coinbaseWallet } from '@wagmi/connectors'

const projectId = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || 'your-project-id'

export const config = createConfig({
  chains: [arbitrum, arbitrumGoerli, foundry],
  connectors: [
    metaMask(),
    injected({
      target: {
        id: 'okx',
        name: 'OKX Wallet',
        provider: (window as any)?.okxwallet,
      },
    }),
    injected({
      target: {
        id: 'tp',
        name: 'TokenPocket',
        provider: (window as any)?.tokenpocket?.ethereum,
      },
    }),
    walletConnect({
      projectId,
      metadata: {
        name: 'Bitroot Token Exchange',
        description: 'Exchange your BRT tokens',
        url: 'https://bitroot.exchange',
        icons: ['https://bitroot.exchange/icon.png'],
      },
    }),
    coinbaseWallet({
      appName: 'Bitroot Token Exchange',
    }),
  ],
  transports: {
    [arbitrum.id]: http(),
    [arbitrumGoerli.id]: http(),
    [foundry.id]: http(),
  },
})