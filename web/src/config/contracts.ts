// 多网络合约地址配置
export const contractAddresses = {
  
  // Bitroot Test Network (1337)
  1337: {
    tokenA: '0xc5a5c42992decbae36851359345fe25997f5c42d',
    tokenB: '0x67d269191c92caf3cd7723f116c85e6e9bf55933',
    tokenExchange: '0xe6e340d132b5f46d1e472debcd681b2abc16e57e',
    rpcUrl: 'https://test-rpc.bitroot.co/',
    startBlock: 9051218, // 合约部署区块
  },
  // Local Foundry (31337)
  31337: {
    tokenA: '0x5FbDB2315678afecb367f032d93F642f64180aa3',
    tokenB: '0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512',
    tokenExchange: '0x9fE46736679d2D9a65F0992F2272dE9f3c7fa6e0',
    rpcUrl: 'http://127.0.0.1:8545',
    startBlock: 1, // 本地开发从第1个区块开始
  },
} as const

// 获取当前网络的合约地址，如果网络不受支持则返回 undefined
export function getContractAddresses(chainId: number) {
  return contractAddresses[chainId as keyof typeof contractAddresses] 
}

// 获取所有支持的网络配置
export function getAllNetworkConfigs() {
  return contractAddresses;
}

// 获取默认的索引器配置
export function getIndexerConfig() {
  return {
    supportedChainIds: Object.keys(contractAddresses).map(Number),
    defaultRpcUrl: contractAddresses[1337].rpcUrl,
    defaultStartBlock: contractAddresses[1337].startBlock,
  };
}