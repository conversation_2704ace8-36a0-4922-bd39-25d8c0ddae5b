// 多网络合约地址配置
export const contractAddresses = {
  // Arbitrum One (42161)
  42161: {
    tokenA: '0x0000000000000000000000000000000000000000', // 待部署
    tokenB: '0x0000000000000000000000000000000000000000', // 待部署
    tokenExchange: '0x0000000000000000000000000000000000000000', // 待部署
  },
  // Arbitrum Goerli (421614)
  421614: {
    tokenA: '0x0000000000000000000000000000000000000000', // 待部署
    tokenB: '0x0000000000000000000000000000000000000000', // 待部署
    tokenExchange: '0x0000000000000000000000000000000000000000', // 待部署
  },
  // Bitroot Test Network (1337)
  1337: {
    tokenA: '0x0000000000000000000000000000000000000000', // 待部署
    tokenB: '0x0000000000000000000000000000000000000000', // 待部署
    tokenExchange: '0x0000000000000000000000000000000000000000', // 待部署
  },
  // Local Foundry (31337)
  31337: {
    tokenA: '0x5FbDB2315678afecb367f032d93F642f64180aa3',
    tokenB: '0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512',
    tokenExchange: '0x9fE46736679d2D9a65F0992F2272dE9f3c7fa6e0',
  },
} as const

// 获取当前网络的合约地址
export function getContractAddresses(chainId: number) {
  return contractAddresses[chainId as keyof typeof contractAddresses] || contractAddresses[31337]
}

// 向后兼容的默认导出
export const contracts = {
  tokenA: {
    address: '0x5FbDB2315678afecb367f032d93F642f64180aa3',
  },
  tokenB: {
    address: '0xe7f1725E7734CE288F8367e1Bb143E90bb3F0512',
  },
  tokenExchange: {
    address: '0x9fE46736679d2D9a65F0992F2272dE9f3c7fa6e0',
  },
} as const