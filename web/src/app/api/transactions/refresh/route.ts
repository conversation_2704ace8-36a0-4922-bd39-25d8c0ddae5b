import { NextResponse } from 'next/server';
import { getIndexer } from '@/lib/indexer';

export async function POST() {
    try {
        const indexer = getIndexer();
        await indexer.forceSync();

        return NextResponse.json({
            success: true,
            message: 'Transaction sync triggered successfully'
        });

    } catch (error) {
        console.error('Error triggering transaction sync:', error);
        return NextResponse.json(
            { 
                success: false,
                error: 'Failed to trigger transaction sync' 
            },
            { status: 500 }
        );
    }
} 