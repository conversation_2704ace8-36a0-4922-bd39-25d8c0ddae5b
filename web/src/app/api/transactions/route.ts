import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';

export async function GET(request: NextRequest) {
    try {
        const searchParams = request.nextUrl.searchParams;
        const userAddress = searchParams.get('userAddress');

        if (!userAddress) {
            return NextResponse.json(
                { error: 'userAddress parameter is required' },
                { status: 400 }
            );
        }

        const db = getDatabase();
        const transactions = db.getTransactionsByUser(userAddress.toLowerCase());

        return NextResponse.json(transactions);

    } catch (error) {
        console.error('Error fetching transactions:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
} 