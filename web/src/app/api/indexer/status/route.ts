import { NextResponse } from 'next/server';
import { getIndexer } from '@/lib/indexer';
import { getDatabase } from '@/lib/database';

export async function GET() {
    try {
        const indexer = getIndexer();
        const db = getDatabase();
        
        const status = indexer.getStatus();
        
        // Get some database stats
        const allTransactions = db.getTransactionsByUser(''); // Empty string to get all
        const uniqueUsers = new Set(allTransactions.map(tx => tx.userAddress)).size;
        
        const response = {
            ...status,
            database: {
                totalTransactions: allTransactions.length,
                uniqueUsers: uniqueUsers,
                latestTransaction: allTransactions[0] || null,
            },
            timestamp: new Date().toISOString(),
        };

        return NextResponse.json(response);

    } catch (error) {
        console.error('Error getting indexer status:', error);
        return NextResponse.json(
            { 
                error: 'Failed to get indexer status',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
} 