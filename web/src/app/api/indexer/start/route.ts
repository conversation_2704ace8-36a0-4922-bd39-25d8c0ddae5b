import { NextResponse } from 'next/server';
import { getIndexer } from '@/lib/indexer';

export async function POST() {
    try {
        const indexer = getIndexer();
        await indexer.start();

        return NextResponse.json({
            success: true,
            message: 'Indexer service started successfully'
        });

    } catch (error) {
        console.error('Error starting indexer service:', error);
        return NextResponse.json(
            { 
                success: false,
                error: 'Failed to start indexer service' 
            },
            { status: 500 }
        );
    }
} 