import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { getIndexerConfig } from '@/config/contracts';

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const { startBlock, clearTransactions = false } = body;
        
        const db = getDatabase();
        const config = getIndexerConfig();
        
        // Use provided startBlock or default
        const newStartBlock = startBlock || config.defaultStartBlock;
        
        console.log(`🔄 Resetting indexer to block ${newStartBlock}`);
        
        // Clear transactions if requested
        if (clearTransactions) {
            console.log('🗑️  Clearing all transaction data');
            db.clearAllTransactions();
        }
        
        // Reset the last processed block
        const success = db.updateLastProcessedBlock(newStartBlock - 1);
        
        if (success) {
            console.log(`✅ Reset indexer state to block ${newStartBlock - 1}`);
            return NextResponse.json({
                success: true,
                message: `Indexer reset to start from block ${newStartBlock}`,
                startBlock: newStartBlock,
                clearedTransactions: clearTransactions
            });
        } else {
            throw new Error('Failed to update database');
        }

    } catch (error) {
        console.error('❌ Error resetting indexer:', error);
        return NextResponse.json(
            { 
                success: false,
                error: 'Failed to reset indexer',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
} 