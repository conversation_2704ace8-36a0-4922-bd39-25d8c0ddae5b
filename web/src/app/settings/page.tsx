'use client'

import React, { useState, useEffect } from 'react'
import { Head<PERSON> } from '@/components/Header'
import { useRouter } from 'next/navigation'

interface NetworkConfig {
  chainId: number
  name: string
  rpcUrl: string
  blockExplorer: string
}

interface ContractConfig {
  tokenA: string
  tokenB: string
  tokenExchange: string
}

const defaultNetworks: NetworkConfig[] = [
  {
    chainId: 42161,
    name: 'Arbitrum One',
    rpcUrl: 'https://arb1.arbitrum.io/rpc',
    blockExplorer: 'https://arbiscan.io'
  },
  {
    chainId: 421614,
    name: 'Arbitrum Goerli',
    rpcUrl: 'https://goerli-rollup.arbitrum.io/rpc',
    blockExplorer: 'https://goerli.arbiscan.io'
  }
]

const defaultContracts: Record<number, ContractConfig> = {
  42161: {
    tokenA: '0x1234567890123456789012345678901234567890',
    tokenB: '0x0987654321098765432109876543210987654321',
    tokenExchange: '0x1111111111111111111111111111111111111111'
  },
  421614: {
    tokenA: '0x2222222222222222222222222222222222222222',
    tokenB: '0x3333333333333333333333333333333333333333',
    tokenExchange: '0x4444444444444444444444444444444444444444'
  }
}

export default function SettingsPage() {
  const router = useRouter()
  const [selectedNetwork, setSelectedNetwork] = useState<number>(42161)
  const [networks, setNetworks] = useState<NetworkConfig[]>(defaultNetworks)
  const [contracts, setContracts] = useState<Record<number, ContractConfig>>(defaultContracts)
  const [isEditing, setIsEditing] = useState(false)

  // Load saved settings from localStorage
  useEffect(() => {
    const savedNetworks = localStorage.getItem('bitroot-networks')
    const savedContracts = localStorage.getItem('bitroot-contracts')
    const savedSelectedNetwork = localStorage.getItem('bitroot-selected-network')

    if (savedNetworks) {
      setNetworks(JSON.parse(savedNetworks))
    }
    if (savedContracts) {
      setContracts(JSON.parse(savedContracts))
    }
    if (savedSelectedNetwork) {
      setSelectedNetwork(parseInt(savedSelectedNetwork))
    }
  }, [])

  const saveSettings = () => {
    localStorage.setItem('bitroot-networks', JSON.stringify(networks))
    localStorage.setItem('bitroot-contracts', JSON.stringify(contracts))
    localStorage.setItem('bitroot-selected-network', selectedNetwork.toString())
    setIsEditing(false)
    alert('Settings saved successfully!')
  }

  const resetToDefaults = () => {
    setNetworks(defaultNetworks)
    setContracts(defaultContracts)
    setSelectedNetwork(42161)
  }

  const updateContractAddress = (field: keyof ContractConfig, value: string) => {
    setContracts(prev => ({
      ...prev,
      [selectedNetwork]: {
        ...prev[selectedNetwork],
        [field]: value
      }
    }))
  }

  const currentNetwork = networks.find(n => n.chainId === selectedNetwork)
  const currentContracts = contracts[selectedNetwork] || defaultContracts[selectedNetwork]

  return (
    <div className="min-h-screen bg-main">
      <Header />
      
      <main className="pt-20 pb-10">
        <div className="max-w-4xl mx-auto px-4">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Settings</h1>
              <p className="text-gray-400">Configure network and contract settings</p>
            </div>
            <button
              onClick={() => router.push('/')}
              className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Back to Exchange
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Network Configuration */}
            <div className="bg-card rounded-[20px] p-6">
              <h2 className="text-xl font-semibold text-white mb-4">Network Configuration</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Select Network
                  </label>
                  <select
                    value={selectedNetwork}
                    onChange={(e) => setSelectedNetwork(parseInt(e.target.value))}
                    className="w-full bg-main border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    {networks.map((network) => (
                      <option key={network.chainId} value={network.chainId}>
                        {network.name} (Chain ID: {network.chainId})
                      </option>
                    ))}
                  </select>
                </div>

                {currentNetwork && (
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Network Name
                      </label>
                      <input
                        type="text"
                        value={currentNetwork.name}
                        readOnly={!isEditing}
                        className="w-full bg-main border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        RPC URL
                      </label>
                      <input
                        type="text"
                        value={currentNetwork.rpcUrl}
                        readOnly={!isEditing}
                        className="w-full bg-main border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Block Explorer
                      </label>
                      <input
                        type="text"
                        value={currentNetwork.blockExplorer}
                        readOnly={!isEditing}
                        className="w-full bg-main border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Contract Configuration */}
            <div className="bg-card rounded-[20px] p-6">
              <h2 className="text-xl font-semibold text-white mb-4">Contract Addresses</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    BRT Token Contract
                  </label>
                  <input
                    type="text"
                    value={currentContracts?.tokenA || ''}
                    onChange={(e) => updateContractAddress('tokenA', e.target.value)}
                    readOnly={!isEditing}
                    placeholder="0x..."
                    className="w-full bg-main border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    BRT(one) Token Contract
                  </label>
                  <input
                    type="text"
                    value={currentContracts?.tokenB || ''}
                    onChange={(e) => updateContractAddress('tokenB', e.target.value)}
                    readOnly={!isEditing}
                    placeholder="0x..."
                    className="w-full bg-main border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Token Exchange Contract
                  </label>
                  <input
                    type="text"
                    value={currentContracts?.tokenExchange || ''}
                    onChange={(e) => updateContractAddress('tokenExchange', e.target.value)}
                    readOnly={!isEditing}
                    placeholder="0x..."
                    className="w-full bg-main border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center gap-4 mt-8">
            {!isEditing ? (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-6 py-3 bg-primary text-card font-medium rounded-lg hover:opacity-90 transition-opacity"
                >
                  Edit Settings
                </button>
                <button
                  onClick={resetToDefaults}
                  className="px-6 py-3 bg-gray-700 text-white font-medium rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Reset to Defaults
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={saveSettings}
                  className="px-6 py-3 bg-primary text-card font-medium rounded-lg hover:opacity-90 transition-opacity"
                >
                  Save Settings
                </button>
                <button
                  onClick={() => setIsEditing(false)}
                  className="px-6 py-3 bg-gray-700 text-white font-medium rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
              </>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
