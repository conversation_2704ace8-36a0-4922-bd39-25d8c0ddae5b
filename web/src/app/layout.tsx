import type { <PERSON>ada<PERSON> } from "next";
import { Web3Provider } from "./Web3Provider";
import "./globals.css";

export const metadata: Metadata = {
  title: "Bitroot Token Exchange",
  description: "Exchange your legacy tokens for the new ones.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <Web3Provider>{children}</Web3Provider>
      </body>
    </html>
  );
}
