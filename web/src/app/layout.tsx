import type { <PERSON>ada<PERSON> } from "next";
import { Web3Provider } from "./Web3Provider";
import { IndexerProvider } from "@/components/IndexerProvider";
import "./globals.css";

export const metadata: Metadata = {
  title: "Bitroot Token Exchange",
  description: "Exchange your legacy tokens for the new ones.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body>
        <Web3Provider>
          <IndexerProvider>
            {children}
          </IndexerProvider>
        </Web3Provider>
      </body>
    </html>
  );
}
