import { Header } from "@/components/Header";
import { NewExchangeForm } from '@/components/NewExchangeForm'
import { Countdown } from '@/components/Countdown'
import { TransactionHistory } from '@/components/TransactionHistory'

export default function Home() {
  // Set deadline to 60 days from now (as per contract requirement)
  const deadline = new Date()
  deadline.setDate(deadline.getDate() + 30) // 30 days for demo
  deadline.setHours(23, 59, 59) // End of day

  return (
    <div className="min-h-screen bg-main">
      <Header />

      <main className="pt-20"> {/* Add padding for fixed header */}
        <div className="max-w-[540px] mx-auto px-4 py-10">
          {/* Countdown Section */}
          <Countdown targetDate={deadline} />

          {/* Exchange Form */}
          <NewExchangeForm />
        </div>

        {/* Transaction History - Full Width */}
        <div className="max-w-7xl mx-auto px-4 py-8">
          <TransactionHistory />
        </div>
      </main>

      <footer className="border-t border-border py-6 text-center text-secondary-text">
        © 2025 Bitroot. All rights reserved.
      </footer>
    </div>
  );
}
