'use client'

import React, { useState, useMemo, useEffect } from 'react'
import { useAccount, useBalance, useReadContract, useWriteContract, useWaitForTransactionReceipt, useConnect, useChainId, useSwitch<PERSON>hain, Connector } from 'wagmi'
import { getContractAddresses, contractAddresses } from '@/config/contracts'
import { formatUnits, parseUnits } from 'viem'
import tokenExchangeAbi from '@/abis/TokenExchange.json'
import tokenAbi from '@/abis/Token.json'
import { ConfirmModal } from '@/components/ConfirmModal'
import { StatusModal } from '@/components/StatusModal'
import { ExchangeSuccessModal } from '@/components/ExchangeSuccessModal'
import Image from 'next/image'

// 获取第一个支持的网络作为默认切换目标
const supportedChainIds = Object.keys(contractAddresses).map(Number)
const defaultChainId = supportedChainIds[0]

export function NewExchangeForm() {
  const { address, isConnected, chain } = useAccount()
  const { connectors, connect } = useConnect()
  const { switchChain } = useSwitchChain()
  const chainId = useChainId()
  
  // State
  const [amount, setAmount] = useState('')
  const [isApproving, setIsApproving] = useState(false)
  const [isExchanging, setIsExchanging] = useState(false)
  const [isAwaitingExchange, setIsAwaitingExchange] = useState(false) // New state for auto-trigger
  const [showConfirm, setShowConfirm] = useState(false)
  const [showStatus, setShowStatus] = useState(false)
  const [statusSuccess, setStatusSuccess] = useState(true)
  const [statusMsg, setStatusMsg] = useState('')
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [exchangeTxHash, setExchangeTxHash] = useState('')
  const [approvalTxHash, setApprovalTxHash] = useState('')
  const [mounted, setMounted] = useState(false)

  useEffect(() => setMounted(true), [])

  // Derived State
  const currentContracts = useMemo(() => getContractAddresses(chainId), [chainId])
  const isNetworkSupported = !!currentContracts
  const targetChainId = chain?.id && supportedChainIds.includes(chain.id) ? chain.id : defaultChainId

  // Reset form on address or network change
  useEffect(() => {
    setAmount('')
    setIsAwaitingExchange(false)
  }, [address, chainId])

  // Wagmi Hooks
  const { data: tokenABalance, refetch: refetchBalanceA } = useBalance({
    address,
    token: currentContracts?.tokenA,
    query: { enabled: isConnected && isNetworkSupported },
  })

  const { data: tokenBBalance, refetch: refetchBalanceB } = useBalance({
    address,
    token: currentContracts?.tokenB,
    query: { enabled: isConnected && isNetworkSupported },
  })

  const { data: allowance, refetch: refetchAllowance } = useReadContract({
    address: currentContracts?.tokenA,
    abi: tokenAbi.abi,
    functionName: 'allowance',
    args: [address, currentContracts?.tokenExchange],
    query: { enabled: isConnected && isNetworkSupported && !!address },
  })

  const { data: exchangeRate } = useReadContract({
    address: currentContracts?.tokenExchange,
    abi: tokenExchangeAbi.abi,
    functionName: 'getExchangeRate',
    query: { enabled: isNetworkSupported },
  })
  
  const { writeContract: approveToken, data: approveData, error: approveError } = useWriteContract()
  const { writeContract: exchangeTokens, data: exchangeData, error: exchangeError } = useWriteContract()

  // Transaction Receipt Hooks
  const { isLoading: isApprovalPending, isSuccess: isApprovalSuccess } = useWaitForTransactionReceipt({ hash: approvalTxHash as `0x${string}` })
  const { isLoading: isExchangePending, isSuccess: isExchangeSuccess } = useWaitForTransactionReceipt({ hash: exchangeTxHash as `0x${string}` })

  // Effect to set transaction hashes
  useEffect(() => { if (approveData) setApprovalTxHash(approveData) }, [approveData])
  useEffect(() => { if (exchangeData) setExchangeTxHash(exchangeData) }, [exchangeData])

  // Auto-trigger exchange after successful approval
  useEffect(() => {
    if (isApprovalSuccess && isAwaitingExchange) {
      setIsApproving(false)
      setApprovalTxHash('')
      refetchAllowance()
      setIsAwaitingExchange(false)
      setShowConfirm(true) // Open confirmation modal automatically
    }
  }, [isApprovalSuccess, isAwaitingExchange, refetchAllowance])

  // Handle successful exchange
  useEffect(() => {
    if (isExchangeSuccess) {
      setIsExchanging(false)
      setAmount('')
      refetchBalanceA()
      refetchBalanceB()
      setShowSuccessModal(true)
      setExchangeTxHash('')
    }
  }, [isExchangeSuccess, refetchBalanceA, refetchBalanceB])

  // Handle transaction errors
  useEffect(() => {
    const error = approveError || exchangeError
    if (error) {
      setIsApproving(false)
      setIsExchanging(false)
      setStatusSuccess(false)
      setStatusMsg(error.message.split('.')[0] || 'Transaction failed.')
      setShowStatus(true)
    }
  }, [approveError, exchangeError])


  // Memoized calculations
  const amountToReceive = useMemo(() => {
    if (!amount || !exchangeRate) return '0.0'
    try {
      const amountBN = parseUnits(amount, 18)
      const receiveBN = amountBN * BigInt(exchangeRate as number)
      return formatUnits(receiveBN, 18)
    } catch { return '0.0' }
  }, [amount, exchangeRate])

  const needsApproval = useMemo(() => {
    if (!amount || !allowance) return false
    try {
      const amountBN = parseUnits(amount, 18)
      return amountBN > (allowance as bigint)
    } catch { return false }
  }, [amount, allowance])

  // Handlers
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (value === '' || /^\d*\.?\d*$/.test(value)) setAmount(value)
  }

  const handleMaxClick = () => {
    if (tokenABalance) setAmount(formatUnits(tokenABalance.value, tokenABalance.decimals))
  }
  
  const handleSwitchNetwork = async () => await switchChain({ chainId: targetChainId })

  const handleApprove = async () => {
    if (!amount || !isNetworkSupported || !currentContracts) return
    setIsApproving(true)
    setIsAwaitingExchange(true) // Set flag to auto-trigger exchange
    approveToken({
      address: currentContracts.tokenA,
      abi: tokenAbi.abi,
      functionName: 'approve',
      args: [currentContracts.tokenExchange, parseUnits(amount, 18)],
    })
  }

  const handleExchange = async () => {
    if (!amount || !isNetworkSupported || !currentContracts) return
    setShowConfirm(false)
    setIsExchanging(true)
    exchangeTokens({
      address: currentContracts.tokenExchange,
      abi: tokenExchangeAbi.abi,
      functionName: 'exchangeAToB',
      args: [parseUnits(amount, 18)],
    })
  }

  const onPrimaryClick = () => {
    if (!isConnected) {
      connect({ connector: connectors.find((c: Connector) => c.id === 'injected' || c.id === 'metaMask') || connectors[0] })
      return
    }
    if (!isNetworkSupported) {
      handleSwitchNetwork()
      return
    }
    if (needsApproval) {
      handleApprove()
    } else {
      setShowConfirm(true)
    }
  }

  // Button text and disabled logic
  const buttonText = () => {
    if (!mounted) return 'Loading...'
    if (!isConnected) return 'Connect Wallet'
    if (!isNetworkSupported) return `Switch to ${chain?.name || 'Supported Network'}`
    if (isApproving || isApprovalPending) return 'Approving...'
    if (isExchanging || isExchangePending) return 'Exchanging...'
    if (needsApproval) return 'Approve & Exchange'
    return 'Exchange'
  }

  const isButtonDisabled = () => {
    if (!mounted || isApproving || isExchanging || isApprovalPending || isExchangePending) return true
    if (!isConnected || !isNetworkSupported) return false // Always allow connecting or switching
    if (!amount || parseFloat(amount) <= 0) return true
    try {
      if (tokenABalance && parseUnits(amount, 18) > tokenABalance.value) return true
    } catch { return true }
    return false
  }
  
  // Render logic...
  // The JSX part is largely the same, just a few dynamic properties will change
  // based on the new logic (e.g., buttonText, isButtonDisabled)

  // Don't render anything until mounted to prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="bg-card rounded-[20px] p-5">
        <div className="animate-pulse">
          <div className="h-20 bg-main rounded-[20px] mb-5"></div>
          <div className="h-16 bg-main rounded-[20px] mb-5"></div>
          <div className="h-12 bg-primary rounded-2xl"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-card rounded-[20px] p-5">
      {/* Network Warning */}
      {isConnected && !isNetworkSupported && (
        <div className="bg-red-600/20 border border-red-600/50 rounded-2xl p-3 mb-5">
          <p className="text-red-400 text-sm text-center">
            Please switch to a supported network to continue
          </p>
        </div>
      )}

      {/* Horizontal Token Exchange Layout */}
      <div className="flex items-center gap-2 mb-5">
        {/* From Token */}
        <div className="flex-1 bg-main rounded-[20px] p-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center">
              <Image 
                src="/images/brt-old-token.svg" 
                alt="BRT Token" 
                width={18} 
                height={21}
                className="w-[18px] h-[21px]"
              />
            </div>
            <div className="flex-1">
              <p className="text-xs text-gray-400 mb-1">From</p>
              <p className="text-xl font-semibold text-white">BRT</p>
              {isConnected && isNetworkSupported && (
                <p className="text-xs text-gray-500">
                  Balance: {tokenABalance ? formatUnits(tokenABalance.value, tokenABalance.decimals) : '0.0'}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Swap Arrow */}
        <div className="w-6 h-6 flex items-center justify-center">
          <Image 
            src="/images/swap-arrow.svg" 
            alt="Swap" 
            width={8} 
            height={14}
            className="text-white"
          />
        </div>

        {/* To Token */}
        <div className="flex-1 bg-main rounded-[20px] p-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-gray-600 to-gray-800 flex items-center justify-center">
              <Image 
                src="/images/brt-new-token.svg" 
                alt="BRT(one) Token" 
                width={18} 
                height={21}
                className="w-[18px] h-[21px]"
              />
            </div>
            <div className="flex-1">
              <p className="text-xs text-gray-400 mb-1">To</p>
              <p className="text-xl font-semibold text-white">BRT(one)</p>
              {isConnected && isNetworkSupported && (
                <p className="text-xs text-gray-500">
                  Balance: {tokenBBalance ? formatUnits(tokenBBalance.value, tokenBBalance.decimals) : '0.0'}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Exchange Input Section */}
      <div className="flex gap-6 mb-5">
        <div className="flex-1">
          <div className="flex items-center justify-center mb-3">
            <span className="text-xs text-white">Exchange</span>
          </div>
          <div className="bg-main rounded-2xl p-3 relative">
            <input
              type="text"
              className="w-full bg-transparent text-xs text-gray-400 placeholder-gray-500 outline-none"
              placeholder="Please enter the exchange quantity"
              value={amount}
              onChange={handleAmountChange}
              disabled={!isConnected || !isNetworkSupported}
            />
            <button
              onClick={handleMaxClick}
              disabled={!isConnected || !isNetworkSupported}
              className="absolute right-3 top-1/2 -translate-y-1/2 bg-gray-600 px-2 py-1 rounded text-xs text-gray-400 hover:bg-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Max
            </button>
          </div>
        </div>
      </div>

      {/* Exchange Info */}
      <div className="flex justify-between mb-5">
        <div className="flex items-center">
          <span className="text-xs text-white">1 BRT = {exchangeRate ? Number(exchangeRate) : '2000'} BRT(one)</span>
        </div>
        <div className="flex items-center">
          <span className="text-xs text-white">Gas:0.0001Arb</span>
        </div>
      </div>

      {/* Connect Wallet Button */}
      <button
        className="w-full py-4 bg-primary rounded-2xl font-medium text-card hover:opacity-90 disabled:opacity-40 transition-all text-base disabled:cursor-not-allowed"
        disabled={isButtonDisabled()}
        onClick={onPrimaryClick}
      >
        {(isApproving || isExchanging || isApprovalPending || isExchangePending) && (
          <div className="inline-flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-card"></div>
            {buttonText()}
          </div>
        )}
        {!isApproving && !isExchanging && !isApprovalPending && !isExchangePending && buttonText()}
      </button>

      {/* Modals */}
      <ConfirmModal
        open={showConfirm}
        amount={amount}
        amountToReceive={amountToReceive}
        onCancel={() => setShowConfirm(false)}
        onConfirm={handleExchange}
      />
      <StatusModal
        open={showStatus}
        success={statusSuccess}
        message={statusMsg}
        onClose={() => setShowStatus(false)}
      />
      <ExchangeSuccessModal
        open={showSuccessModal}
        amount={amount}
        receivedAmount={amountToReceive}
        txHash={exchangeTxHash}
        onClose={() => setShowSuccessModal(false)}
      />
    </div>
  )
}
