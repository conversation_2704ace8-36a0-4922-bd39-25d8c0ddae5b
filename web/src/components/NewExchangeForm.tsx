'use client'

import React, { useState, useMemo } from 'react'
import { useAccount, useBalance, useReadContract, useWriteContract, useWaitForTransactionReceipt, useConnect, useChainId } from 'wagmi'
import { contracts, getContractAddresses } from '@/config/contracts'
import { formatUnits, parseUnits } from 'viem'
import tokenExchangeAbi from '@/abis/TokenExchange.json'
import tokenAbi from '@/abis/Token.json'
import { ConfirmModal } from '@/components/ConfirmModal'
import { StatusModal } from '@/components/StatusModal'
import { ExchangeSuccessModal } from '@/components/ExchangeSuccessModal'
import Image from 'next/image'

export function NewExchangeForm() {
  const { address, isConnected } = useAccount()
  const { connectors, connect } = useConnect()
  const chainId = useChainId()
  const [amount, setAmount] = useState('')
  const [isApproving, setIsApproving] = useState(false)
  const [isExchanging, setIsExchanging] = useState(false)
  const [showConfirm, setShowConfirm] = useState(false)
  const [showStatus, setShowStatus] = useState(false)
  const [statusSuccess, setStatusSuccess] = useState(true)
  const [statusMsg, setStatusMsg] = useState('')
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [exchangeTxHash, setExchangeTxHash] = useState('')

  // 获取当前网络的合约地址
  const currentContracts = useMemo(() => {
    return getContractAddresses(chainId)
  }, [chainId])

  const { data: tokenABalance, refetch: refetchBalance } = useBalance({
    address,
    token: currentContracts.tokenA as `0x${string}`,
  })

  const { data: allowance, refetch: refetchAllowance } = useReadContract({
    address: currentContracts.tokenA as `0x${string}`,
    abi: tokenAbi.abi,
    functionName: 'allowance',
    args: [address, currentContracts.tokenExchange],
    query: {
      enabled: isConnected && !!address,
    }
  })

  const { data: exchangeRate } = useReadContract({
    address: currentContracts.tokenExchange as `0x${string}`,
    abi: tokenExchangeAbi.abi,
    functionName: 'getExchangeRate',
  })

  const { writeContract: approveToken } = useWriteContract()
  const { writeContract: exchangeTokens } = useWriteContract()

  const amountToReceive = useMemo(() => {
    if (!amount || !exchangeRate) return '0.0'
    try {
      const amountBN = parseUnits(amount, 18)
      const receiveBN = amountBN * BigInt(exchangeRate)
      return formatUnits(receiveBN, 18)
    } catch {
      return '0.0'
    }
  }, [amount, exchangeRate])

  const needsApproval = useMemo(() => {
    if (!amount || !allowance) return false
    try {
      const amountBN = parseUnits(amount, 18)
      return amountBN > allowance
    } catch {
      return false
    }
  }, [amount, allowance])

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (value === '' || /^\d*\.?\d*$/.test(value)) {
      setAmount(value)
    }
  }

  const handleMaxClick = () => {
    if (tokenABalance) {
      setAmount(formatUnits(tokenABalance.value, tokenABalance.decimals))
    }
  }

  const handleApprove = async () => {
    if (!amount) return
    setIsApproving(true)
    try {
      const amountBN = parseUnits(amount, 18)
      await approveToken({
        address: currentContracts.tokenA as `0x${string}`,
        abi: tokenAbi.abi,
        functionName: 'approve',
        args: [currentContracts.tokenExchange, amountBN],
      })
      await refetchAllowance()
    } catch (error) {
      console.error('Approval failed:', error)
    } finally {
      setIsApproving(false)
    }
  }

  const handleExchange = async () => {
    if (!amount) return
    setIsExchanging(true)
    try {
      const amountBN = parseUnits(amount, 18)
      const result = await exchangeTokens({
        address: currentContracts.tokenExchange as `0x${string}`,
        abi: tokenExchangeAbi.abi,
        functionName: 'exchangeAToB',
        args: [amountBN],
      })

      // Store transaction hash for success modal
      if (result) {
        setExchangeTxHash(result)
      }

      // Clear form and refresh balance
      const exchangedAmount = amount
      setAmount('')
      await refetchBalance()

      // Show success modal instead of status modal
      setShowSuccessModal(true)
    } catch (error) {
      console.error('Exchange failed:', error)
      setStatusSuccess(false)
      setStatusMsg('Exchange failed. Please try again.')
      setShowStatus(true)
    } finally {
      setIsExchanging(false)
    }
  }

  const handleConnectWallet = () => {
    // Find MetaMask connector and connect directly
    const metaMaskConnector = connectors.find(connector =>
      connector.name.toLowerCase().includes('metamask') ||
      connector.id === 'metaMask' ||
      connector.id === 'injected'
    )

    if (metaMaskConnector) {
      connect({ connector: metaMaskConnector })
    } else {
      // Fallback to first available connector
      if (connectors.length > 0) {
        connect({ connector: connectors[0] })
      }
    }
  }

  const onPrimaryClick = () => {
    if (!isConnected) {
      handleConnectWallet()
      return
    }
    if (needsApproval) {
      handleApprove()
    } else {
      setShowConfirm(true)
    }
  }

  const buttonText = () => {
    if (!isConnected) return 'Connect Wallet'
    if (isApproving) return 'Approving...'
    if (isExchanging) return 'Exchanging...'
    if (needsApproval) return 'Approve BRT'
    return 'Exchange'
  }

  const isButtonDisabled = () => {
    // 未连接钱包时，按钮可点击用于连接
    if (!isConnected) return false

    // 连接钱包但正在处理中时，按钮不可点击
    if (isApproving || isExchanging) return true

    // 连接钱包但未输入金额时，按钮不可点击
    if (!amount || amount === '0' || amount === '0.0') return true

    // 检查金额是否有效且不超过余额
    try {
      const amountBN = parseUnits(amount, 18)
      if (tokenABalance && amountBN > tokenABalance.value) return true
    } catch {
      return true
    }

    return false
  }

  return (
    <div className="bg-card rounded-[20px] p-5">
      {/* Horizontal Token Exchange Layout */}
      <div className="flex items-center gap-2 mb-5">
        {/* From Token */}
        <div className="flex-1 bg-main rounded-[20px] p-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center">
              <Image 
                src="/images/brt-old-token.svg" 
                alt="BRT Token" 
                width={18} 
                height={21}
                className="w-[18px] h-[21px]"
              />
            </div>
            <div className="flex-1">
              <p className="text-xs text-gray-400 mb-1">Form</p>
              <p className="text-xl font-semibold text-white">BRT</p>
            </div>
          </div>
        </div>

        {/* Swap Arrow */}
        <div className="w-6 h-6 flex items-center justify-center">
          <Image 
            src="/images/swap-arrow.svg" 
            alt="Swap" 
            width={8} 
            height={14}
            className="text-white"
          />
        </div>

        {/* To Token */}
        <div className="flex-1 bg-main rounded-[20px] p-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-gray-600 to-gray-800 flex items-center justify-center">
              <Image 
                src="/images/brt-new-token.svg" 
                alt="BRT(one) Token" 
                width={18} 
                height={21}
                className="w-[18px] h-[21px]"
              />
            </div>
            <div className="flex-1">
              <p className="text-xs text-gray-400 mb-1">To</p>
              <p className="text-xl font-semibold text-white">BRT(one)</p>
            </div>
          </div>
        </div>
      </div>

      {/* Exchange Input Section */}
      <div className="flex gap-6 mb-5">
        <div className="flex-1">
          <div className="flex items-center justify-center mb-3">
            <span className="text-xs text-white">Exchange</span>
          </div>
          <div className="bg-main rounded-2xl p-3 relative">
            <input
              type="text"
              className="w-full bg-transparent text-xs text-gray-400 placeholder-gray-500 outline-none"
              placeholder="Please enter the exchange quantity"
              value={amount}
              onChange={handleAmountChange}
            />
            <button
              onClick={handleMaxClick}
              className="absolute right-3 top-1/2 -translate-y-1/2 bg-gray-600 px-2 py-1 rounded text-xs text-gray-400 hover:bg-gray-500 transition-colors"
            >
              Max
            </button>
          </div>
        </div>
      </div>

      {/* Exchange Info */}
      <div className="flex justify-between mb-5">
        <div className="flex items-center">
          <span className="text-xs text-white">1 BRT = {exchangeRate ? Number(exchangeRate) : '2000'} BRT(one)</span>
        </div>
        <div className="flex items-center">
          <span className="text-xs text-white">Gas:0.0001Arb</span>
        </div>
      </div>

      {/* Connect Wallet Button */}
      <button
        className="w-full py-4 bg-primary rounded-2xl font-medium text-card hover:opacity-90 disabled:opacity-40 transition-all text-base disabled:cursor-not-allowed"
        disabled={isButtonDisabled()}
        onClick={onPrimaryClick}
      >
        {(isApproving || isExchanging) && (
          <div className="inline-flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-card"></div>
            {buttonText()}
          </div>
        )}
        {!isApproving && !isExchanging && buttonText()}
      </button>

      {/* Modals */}
      <ConfirmModal
        open={showConfirm}
        amount={amount}
        onCancel={() => setShowConfirm(false)}
        onConfirm={handleExchange}
      />
      <StatusModal
        open={showStatus}
        success={statusSuccess}
        message={statusMsg}
        onClose={() => setShowStatus(false)}
      />
      <ExchangeSuccessModal
        open={showSuccessModal}
        amount={amount}
        receivedAmount={amountToReceive}
        txHash={exchangeTxHash}
        onClose={() => setShowSuccessModal(false)}
      />
    </div>
  )
}
