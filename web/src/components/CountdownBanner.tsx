'use client'

import { useEffect, useState } from 'react'
import { useReadContract } from 'wagmi'
import { contracts } from '@/config/contracts'
import tokenExchangeAbi from '@/abis/TokenExchange.json'

function CountdownSegment({ value, label }: { value: string; label: string }) {
  return (
    <div className="flex flex-col items-center">
      <div className="flex gap-1">
        {value.split('').map((digit, i) => (
          <div key={i} className="w-8 h-10 bg-main flex items-center justify-center text-xl font-semibold rounded-md">
            {digit}
          </div>
        ))}
      </div>
      <p className="text-sm text-secondary-text mt-1">{label}</p>
    </div>
  )
}

function formatRemaining(seconds: number) {
    if (seconds <= 0) return { d: '00', h: '00', m: '00', s: '00' };
    const d = Math.floor(seconds / 86400).toString().padStart(2, '0');
    const h = Math.floor((seconds % 86400) / 3600).toString().padStart(2, '0');
    const m = Math.floor((seconds % 3600) / 60).toString().padStart(2, '0');
    const s = (seconds % 60).toString().padStart(2, '0');
    return { d, h, m, s };
}

export function CountdownBanner() {
  const { data: endTime } = useReadContract({
    address: contracts.tokenExchange.address,
    abi: tokenExchangeAbi.abi,
    functionName: 'getExchangeEndTime',
  })

  const [remaining, setRemaining] = useState({ d: '00', h: '00', m: '00', s: '00' })

  useEffect(() => {
    if (!endTime) return
    const id = setInterval(() => {
      const now = Math.floor(Date.now() / 1000)
      const secondsLeft = Number(endTime) - now
      setRemaining(formatRemaining(secondsLeft))
    }, 1000)
    return () => clearInterval(id)
  }, [endTime])

  if (!endTime) return null

  return (
    <div className="w-full max-w-xl rounded-card bg-card p-5">
        <div className="flex justify-between items-center">
            <div>
                <h3 className="text-xl font-semibold text-primary-text">Deadline</h3>
                <p className="text-sm text-secondary-text mt-1">Please be sure to exchange within this time period.</p>
            </div>
            <div className="flex items-center gap-2 text-primary-text">
              <CountdownSegment value={remaining.d} label="Days" />
              <span>:</span>
              <CountdownSegment value={remaining.h} label="Hours" />
              <span>:</span>
              <CountdownSegment value={remaining.m} label="Mins" />
              <span>:</span>
              <CountdownSegment value={remaining.s} label="Secs" />
            </div>
        </div>
    </div>
  )
} 