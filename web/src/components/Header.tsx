'use client'

import React, { useState } from 'react'
import { ConnectWallet } from './ConnectWallet'
import Image from 'next/image'
import Link from 'next/link'

export function Header() {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null)

  const navItems = [
    {
      name: 'Learn',
      hasDropdown: true,
      items: ['Documentation', 'Tutorials', 'FAQ']
    },
    {
      name: 'Developers',
      hasDropdown: true,
      items: ['API Reference', 'SDK', 'GitHub']
    },
    {
      name: 'Ecosystem',
      hasDropdown: true,
      items: ['Partners', 'Integrations', 'Community']
    },
    {
      name: 'Node',
      hasDropdown: true,
      items: ['Run a Node', 'Validator Guide', 'Network Stats']
    },
    {
      name: 'Media',
      hasDropdown: true,
      items: ['Blog', 'News', 'Press Kit']
    }
  ]

  return (
    <header className="bg-main/60 backdrop-blur-[20px] border-b border-gray-800 fixed top-0 left-0 right-0 z-50">
      <div className="max-w-7xl mx-auto px-12">
        <div className="flex justify-between items-center h-20">
          <div className="flex items-center gap-12">
            <div className="flex-shrink-0">
              <Image
                src="/images/bitroot-logo.svg"
                alt="Bitroot Logo"
                width={121}
                height={28}
                className="h-7 w-auto"
              />
            </div>

            <nav className="hidden md:block">
              <div className="flex items-center gap-6">
                {navItems.map((item) => (
                  <div key={item.name} className="relative">
                    <button
                      className="flex items-center gap-2 text-white hover:text-primary px-2 py-1 text-base font-medium transition-colors"
                      onMouseEnter={() => setOpenDropdown(item.name)}
                      onMouseLeave={() => setOpenDropdown(null)}
                    >
                      {item.name}
                      {item.hasDropdown && (
                        <Image
                          src="/images/dropdown-arrow.svg"
                          alt="Dropdown"
                          width={12}
                          height={6}
                          className="opacity-60"
                        />
                      )}
                    </button>

                    {item.hasDropdown && openDropdown === item.name && (
                      <div
                        className="absolute top-full left-0 mt-1 w-48 bg-card border border-gray-700 rounded-lg shadow-lg z-50"
                        onMouseEnter={() => setOpenDropdown(item.name)}
                        onMouseLeave={() => setOpenDropdown(null)}
                      >
                        {item.items.map((subItem) => (
                          <a
                            key={subItem}
                            href="#"
                            className="block px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 first:rounded-t-lg last:rounded-b-lg"
                          >
                            {subItem}
                          </a>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </nav>
          </div>

          <div className="flex items-center gap-4">
            <Link
              href="/settings"
              className="p-2 text-gray-400 hover:text-white transition-colors"
              title="Settings"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="3"/>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m11-7a4 4 0 0 0-8 0m8 0a4 4 0 0 0 8 0m-8 14a4 4 0 0 0-8 0m8 0a4 4 0 0 0 8 0"/>
              </svg>
            </Link>
            <ConnectWallet />
          </div>
        </div>
      </div>
    </header>
  )
}