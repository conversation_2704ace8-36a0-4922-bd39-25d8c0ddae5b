import React from 'react'
import { ConnectWallet } from './ConnectWallet'
import Image from 'next/image'

export function Header() {
  return (
    <header className="flex justify-between items-center p-6 border-b border-border">
      <div className="flex items-center gap-8">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
            <span className="text-main font-bold text-sm">B</span>
          </div>
          <h1 className="text-xl font-bold text-primary-text">Bitroot</h1>
        </div>

        <nav className="hidden md:flex items-center gap-6">
          <a href="#exchange" className="text-primary-text hover:text-primary transition-colors font-medium">
            Exchange
          </a>
          <a href="#history" className="text-secondary-text hover:text-primary-text transition-colors">
            History
          </a>
          <a href="#docs" className="text-secondary-text hover:text-primary-text transition-colors">
            Docs
          </a>
        </nav>
      </div>

      <ConnectWallet />
    </header>
  )
}