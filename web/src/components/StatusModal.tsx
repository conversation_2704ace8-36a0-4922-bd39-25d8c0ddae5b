'use client'

import { Modal } from './Modal'
import Image from 'next/image'

interface Props {
  open: boolean
  success?: boolean
  message: string
  onClose: () => void
}

export function StatusModal({ open, success = true, message, onClose }: Props) {
  const icon = success ? '/assets/success.svg' : '/assets/error.svg'
  return (
    <Modal open={open} onClose={onClose}>
      <div className="flex flex-col items-center text-center">
        <Image src={icon} alt="status" width={48} height={48} />
        <h3 className="mt-4 text-lg font-semibold text-gray-900 dark:text-gray-100">
          {success ? '交易成功' : '交易失败'}
        </h3>
        <p className="mt-2 text-gray-600 dark:text-gray-400">{message}</p>
        <button onClick={onClose} className="mt-6 px-4 py-2 rounded-md bg-primary text-white hover:bg-primary/90 transition-transform active:scale-95">
          关闭
        </button>
      </div>
    </Modal>
  )
} 