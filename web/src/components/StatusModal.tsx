'use client'

interface Props {
  open: boolean
  success?: boolean
  message: string
  onClose: () => void
}

export function StatusModal({ open, success = true, message, onClose }: Props) {
  if (!open) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-card rounded-[20px] p-8 max-w-md w-full mx-4">
        {/* Status Icon */}
        <div className="flex justify-center mb-6">
          <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
            success ? 'bg-primary' : 'bg-red-500'
          }`}>
            {success ? (
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                className="text-card"
              >
                <path
                  d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            ) : (
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                className="text-card"
              >
                <path
                  d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </div>
        </div>

        {/* Title */}
        <h2 className="text-2xl font-bold text-white text-center mb-2">
          {success ? '操作成功' : '操作失败'}
        </h2>

        {/* Message */}
        <p className="text-gray-400 text-center mb-6">
          {message}
        </p>

        {/* Action Button */}
        <button
          onClick={onClose}
          className="w-full py-3 bg-primary rounded-[16px] text-card font-medium hover:opacity-90 transition-opacity"
        >
          {success ? '继续' : '重试'}
        </button>
      </div>
    </div>
  )
}