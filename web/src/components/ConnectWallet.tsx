'use client'

import { useAccount, useConnect, useDisconnect } from 'wagmi'
import { useEffect, useState } from 'react'

export function ConnectWallet() {
  const { address, isConnected } = useAccount()
  const { connectors, connect } = useConnect()
  const { disconnect } = useDisconnect()
  const [isMounted, setIsMounted] = useState(false)
  const [showWalletModal, setShowWalletModal] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    // Render a placeholder with the same dimensions
    return <div className="h-10 w-40 rounded-lg bg-gray-800 animate-pulse" />
  }

  if (isConnected) {
    return (
      <div className="flex items-center gap-3">
        <p className="text-sm font-medium text-primary-text">
          {`${address?.slice(0, 6)}...${address?.slice(-4)}`}
        </p>
        <button
          onClick={() => disconnect()}
          className="px-4 py-2 rounded-lg bg-gray-700/60 text-secondary-text hover:bg-gray-600/60 transition-all active:scale-95"
        >
          Disconnect
        </button>
      </div>
    )
  }

  const handleWalletConnect = (connector: any) => {
    connect({ connector })
    setShowWalletModal(false)
  }

  return (
    <>
      <button
        onClick={() => setShowWalletModal(true)}
        className="px-6 py-2 rounded-lg bg-primary font-semibold text-main hover:opacity-90 transition-all active:scale-95"
        disabled={!connectors.length}
      >
        Connect Wallet
      </button>

      {/* Wallet Selection Modal */}
      {showWalletModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-card rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-primary-text">Connect Wallet</h3>
              <button
                onClick={() => setShowWalletModal(false)}
                className="text-secondary-text hover:text-primary-text"
              >
                ✕
              </button>
            </div>
            <div className="space-y-3">
              {connectors.map((connector) => (
                <button
                  key={connector.id}
                  onClick={() => handleWalletConnect(connector)}
                  className="w-full flex items-center gap-3 p-3 rounded-lg bg-main hover:bg-gray-700/60 transition-all text-left"
                >
                  <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-main font-semibold">
                    {connector.name.charAt(0)}
                  </div>
                  <span className="text-primary-text font-medium">{connector.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  )
}