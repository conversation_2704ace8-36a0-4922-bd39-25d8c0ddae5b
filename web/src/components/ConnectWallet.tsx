'use client'

import { useAccount, useConnect, useDisconnect } from 'wagmi'
import { useEffect, useState } from 'react'

export function ConnectWallet() {
  const { address, isConnected } = useAccount()
  const { connectors, connect } = useConnect()
  const { disconnect } = useDisconnect()
  const [isMounted, setIsMounted] = useState(false)
  // const [showWalletModal, setShowWalletModal] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    // Render a placeholder with the same dimensions
    return <div className="h-10 w-40 rounded-lg bg-gray-800 animate-pulse" />
  }

  if (isConnected) {
    return (
      <div className="flex items-center gap-3">
        <p className="text-sm font-medium text-primary-text">
          {`${address?.slice(0, 6)}...${address?.slice(-4)}`}
        </p>
        <button
          onClick={() => disconnect()}
          className="px-4 py-2 rounded-lg bg-gray-700/60 text-secondary-text hover:bg-gray-600/60 transition-all active:scale-95"
        >
          Disconnect
        </button>
      </div>
    )
  }

  const handleDirectConnect = () => {
    // Find MetaMask connector and connect directly
    const metaMaskConnector = connectors.find(connector =>
      connector.name.toLowerCase().includes('metamask') ||
      connector.id === 'metaMask' ||
      connector.id === 'injected'
    )

    if (metaMaskConnector) {
      connect({ connector: metaMaskConnector })
    } else {
      // Fallback to first available connector
      if (connectors.length > 0) {
        connect({ connector: connectors[0] })
      }
    }
  }

  return (
    <button
      onClick={handleDirectConnect}
      className="px-6 py-2 rounded-lg bg-primary font-semibold text-main hover:opacity-90 transition-all active:scale-95"
      disabled={!connectors.length}
    >
      Connect Wallet
    </button>
  )
}