'use client'

import { useEffect, useState } from 'react';

interface IndexerProviderProps {
    children: React.ReactNode;
}

interface HealthStatus {
    status: string;
    indexer?: {
        isRunning: boolean;
        clients: number[];
        lastProcessedBlock: number | null;
    };
}

export function IndexerProvider({ children }: IndexerProviderProps) {
    const [, setHealthStatus] = useState<HealthStatus | null>(null);

    useEffect(() => {
        let mounted = true;

        const checkHealth = async () => {
            try {
                console.log('🔍 Checking server health and indexer status...');

                const response = await fetch('/api/health');

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (mounted) {
                    setHealthStatus(result);
                    console.log('✅ Server health check completed:', {
                        status: result.status,
                        indexerRunning: result.indexer?.isRunning,
                        supportedChains: result.indexer?.clients,
                        lastBlock: result.indexer?.lastProcessedBlock
                    });
                }
            } catch (error) {
                console.error('❌ Health check failed:', error);
            }
        };

        // 立即检查一次，然后定期检查
        checkHealth();
        const interval = setInterval(checkHealth, 30000); // 每30秒检查一次

        return () => {
            mounted = false;
            clearInterval(interval);
        };
    }, []);

    // 返回children，不显示任何UI
    // 健康状态信息只在控制台显示
    return <>{children}</>;
}