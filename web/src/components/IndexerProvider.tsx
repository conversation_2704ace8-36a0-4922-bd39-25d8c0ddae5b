'use client'

import { useEffect, useState } from 'react';

interface IndexerProviderProps {
    children: React.ReactNode;
}

export function IndexerProvider({ children }: IndexerProviderProps) {
    const [, setIsStarted] = useState(false);

    useEffect(() => {
        let mounted = true;

        const startIndexer = async () => {
            try {
                console.log('🚀 Auto-starting indexer service via API...');
                
                const response = await fetch('/api/indexer/start', {
                    method: 'POST',
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                
                if (mounted) {
                    setIsStarted(true);
                    console.log('✅ Indexer service auto-started successfully:', result.message);
                }
            } catch (error) {
                console.error('❌ Failed to auto-start indexer service:', error);
            }
        };

        // 延迟启动以确保应用完全加载
        const timeout = setTimeout(startIndexer, 2000);

        return () => {
            mounted = false;
            clearTimeout(timeout);
        };
    }, []);

    // 返回children，不显示任何UI
    return <>{children}</>;
} 