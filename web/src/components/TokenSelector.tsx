'use client'

import Image from 'next/image'

interface Option {
  label: string
  value: 'A' | 'B'
  icon: string
}

const options: Option[] = [
  { label: 'Token A', value: 'A', icon: '/assets/tokenA.svg' },
  { label: 'Token B', value: 'B', icon: '/assets/tokenB.svg' },
]

interface Props {
  selected: 'A' | 'B'
  onChange: (v: 'A' | 'B') => void
}

export function TokenSelector({ selected, onChange }: Props) {
  return (
    <div className="relative">
      <select
        value={selected}
        onChange={(e) => onChange(e.target.value as 'A' | 'B')}
        className="appearance-none bg-transparent pr-8 pl-3 py-2 text-sm font-medium"
      >
        {options.map((o) => (
          <option key={o.value} value={o.value} className="text-black dark:text-white">
            {o.label}
          </option>
        ))}
      </select>
      <Image src="/assets/arrow-down.svg" alt="arrow" width={16} height={16} className="absolute right-2 top-1/2 -translate-y-1/2 pointer-events-none" />
    </div>
  )
} 