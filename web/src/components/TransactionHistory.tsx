'use client'

import React, { useState } from 'react'
import { useAccount } from 'wagmi'
import { formatUnits } from 'viem'
import { useTransactionHistory } from '@/hooks/useTransactionHistory'

export function TransactionHistory() {
  const { address, isConnected } = useAccount()
  const { transactions, isLoading, error, refreshTransactions } = useTransactionHistory()
  const [showAll, setShowAll] = useState(false)

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString()
  }

  const truncateAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`
  }

  const truncateHash = (hash: string) => {
    return `${hash.slice(0, 10)}...${hash.slice(-8)}`
  }

  // Filter transactions based on showAll setting
  const filteredTransactions = showAll ? 
    transactions : 
    transactions.filter(tx => tx.userAddress.toLowerCase() === address?.toLowerCase())

  if (!isConnected) {
    return (
      <div className="w-full max-w-4xl mx-auto rounded-card bg-card p-6">
        <h2 className="text-xl font-semibold text-primary-text mb-4">Transaction History</h2>
        <p className="text-secondary-text">Please connect your wallet to view transaction history.</p>
      </div>
    )
  }

  return (
    <div className="w-full max-w-4xl mx-auto rounded-card bg-card p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-primary-text">Transaction History</h2>
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={showAll}
              onChange={(e) => setShowAll(e.target.checked)}
              className="rounded border-border bg-main"
            />
            <span className="text-secondary-text">Show all transactions</span>
          </label>
          <button
            onClick={refreshTransactions}
            disabled={isLoading}
            className="px-4 py-2 bg-primary text-main rounded-lg font-medium hover:opacity-90 disabled:opacity-50 transition-all"
          >
            {isLoading ? 'Loading...' : 'Refresh'}
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
          <p className="text-red-400 text-sm">Error: {error}</p>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : filteredTransactions.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-secondary-text">No transactions found.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="text-left py-3 px-4 text-secondary-text font-medium">Transaction</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">User</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">BRT Amount</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">BRT(one) Amount</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">Date</th>
              </tr>
            </thead>
            <tbody>
              {filteredTransactions.map((tx) => (
                <tr key={tx.transactionHash} className="border-b border-border/50 hover:bg-main/50 transition-colors">
                  <td className="py-3 px-4">
                    <a
                      href={`https://arbiscan.io/tx/${tx.transactionHash}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline font-mono text-sm"
                    >
                      {truncateHash(tx.transactionHash)}
                    </a>
                  </td>
                  <td className="py-3 px-4">
                    <span className="font-mono text-sm text-primary-text">
                      {truncateAddress(tx.userAddress)}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-primary-text font-medium">
                      {parseFloat(formatUnits(BigInt(tx.oldTokenAmount), 18)).toFixed(4)} BRT
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-primary font-medium">
                      {parseFloat(formatUnits(BigInt(tx.newTokenAmount), 18)).toFixed(4)} BRT(one)
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-secondary-text text-sm">
                      {formatDate(tx.timestamp)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}
