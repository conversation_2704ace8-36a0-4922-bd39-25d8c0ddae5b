'use client'

import React, { useState, useEffect } from 'react'
import { useAccount, usePublicClient } from 'wagmi'
import { contracts } from '@/config/contracts'
import { formatUnits } from 'viem'
import tokenExchangeAbi from '@/abis/TokenExchange.json'

interface Transaction {
  hash: string
  user: string
  oldTokenAmount: string
  newTokenAmount: string
  timestamp: number
  blockNumber: number
}

export function TransactionHistory() {
  const { address, isConnected } = useAccount()
  const publicClient = usePublicClient()
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(false)
  const [showAll, setShowAll] = useState(false)

  useEffect(() => {
    if (isConnected && address && publicClient) {
      fetchTransactions()
    }
  }, [isConnected, address, publicClient])

  const fetchTransactions = async () => {
    if (!publicClient || !address) return
    
    setLoading(true)
    try {
      // Get Exchange events for the current user
      const logs = await publicClient.getLogs({
        address: contracts.tokenExchange.address,
        event: {
          type: 'event',
          name: 'Exchange',
          inputs: [
            { name: 'user', type: 'address', indexed: true },
            { name: 'oldTokenAmount', type: 'uint256', indexed: false },
            { name: 'newTokenAmount', type: 'uint256', indexed: false },
            { name: 'timestamp', type: 'uint256', indexed: false },
          ],
        },
        args: showAll ? undefined : { user: address },
        fromBlock: 'earliest',
        toBlock: 'latest',
      })

      const txs: Transaction[] = logs.map((log) => ({
        hash: log.transactionHash,
        user: log.args.user as string,
        oldTokenAmount: formatUnits(log.args.oldTokenAmount as bigint, 18),
        newTokenAmount: formatUnits(log.args.newTokenAmount as bigint, 18),
        timestamp: Number(log.args.timestamp),
        blockNumber: Number(log.blockNumber),
      }))

      // Sort by timestamp descending
      txs.sort((a, b) => b.timestamp - a.timestamp)
      setTransactions(txs)
    } catch (error) {
      console.error('Error fetching transactions:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString()
  }

  const truncateAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`
  }

  const truncateHash = (hash: string) => {
    return `${hash.slice(0, 10)}...${hash.slice(-8)}`
  }

  if (!isConnected) {
    return (
      <div className="w-full max-w-4xl rounded-card bg-card p-6">
        <h2 className="text-xl font-semibold text-primary-text mb-4">Transaction History</h2>
        <p className="text-secondary-text">Please connect your wallet to view transaction history.</p>
      </div>
    )
  }

  return (
    <div className="w-full max-w-4xl rounded-card bg-card p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-primary-text">Transaction History</h2>
        <div className="flex items-center gap-4">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={showAll}
              onChange={(e) => setShowAll(e.target.checked)}
              className="rounded border-border bg-main"
            />
            <span className="text-secondary-text">Show all transactions</span>
          </label>
          <button
            onClick={fetchTransactions}
            disabled={loading}
            className="px-4 py-2 bg-primary text-main rounded-lg font-medium hover:opacity-90 disabled:opacity-50 transition-all"
          >
            {loading ? 'Loading...' : 'Refresh'}
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : transactions.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-secondary-text">No transactions found.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="text-left py-3 px-4 text-secondary-text font-medium">Transaction</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">User</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">BRT Amount</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">BRT(one) Amount</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">Date</th>
              </tr>
            </thead>
            <tbody>
              {transactions.map((tx, index) => (
                <tr key={tx.hash} className="border-b border-border/50 hover:bg-main/50 transition-colors">
                  <td className="py-3 px-4">
                    <a
                      href={`https://arbiscan.io/tx/${tx.hash}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline font-mono text-sm"
                    >
                      {truncateHash(tx.hash)}
                    </a>
                  </td>
                  <td className="py-3 px-4">
                    <span className="font-mono text-sm text-primary-text">
                      {truncateAddress(tx.user)}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-primary-text font-medium">
                      {parseFloat(tx.oldTokenAmount).toFixed(4)} BRT
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-primary font-medium">
                      {parseFloat(tx.newTokenAmount).toFixed(4)} BRT(one)
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-secondary-text text-sm">
                      {formatDate(tx.timestamp)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}
