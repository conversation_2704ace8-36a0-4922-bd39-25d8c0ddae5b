'use client'

import React, { useState, useMemo, useEffect } from 'react'
import { useAccount, useBalance, useReadContract, useWriteContract, useWaitForTransactionReceipt } from 'wagmi'
import { contracts } from '@/config/contracts'
import { formatUnits, parseUnits } from 'viem'
import tokenExchangeAbi from '@/abis/TokenExchange.json'
import tokenAbi from '@/abis/Token.json'
import { ConfirmModal } from '@/components/ConfirmModal'
import { StatusModal } from '@/components/StatusModal'
import Image from 'next/image'

export function ExchangeForm() {
  const { address, isConnected } = useAccount()
  const [amount, setAmount] = useState('')
  const [isApproving, setIsApproving] = useState(false)
  const [isExchanging, setIsExchanging] = useState(false)
  const [txHash, setTxHash] = useState<`0x${string}` | undefined>()

  // modal states
  const [showConfirm, setShowConfirm] = useState(false)
  const [showStatus, setShowStatus] = useState(false)
  const [statusSuccess, setStatusSuccess] = useState(true)
  const [statusMsg, setStatusMsg] = useState('')

  const { data: tokenABalance, refetch: refetchBalance } = useBalance({
    address,
    token: contracts.tokenA.address,
  })

  const { data: allowance, refetch: refetchAllowance } = useReadContract({
    address: contracts.tokenA.address,
    abi: tokenAbi.abi,
    functionName: 'allowance',
    args: [address, contracts.tokenExchange.address],
    query: {
      enabled: isConnected && !!address,
    }
  })

  const { data: exchangeRate } = useReadContract({
    address: contracts.tokenExchange.address,
    abi: tokenExchangeAbi.abi,
    functionName: 'getExchangeRate',
  })

  const { writeContractAsync: approve } = useWriteContract()
  const { writeContractAsync: exchange } = useWriteContract()

  const { isSuccess, isError } = useWaitForTransactionReceipt({ hash: txHash })

  useEffect(() => {
    if (isSuccess) {
      refetchAllowance()
      refetchBalance()
      setIsApproving(false)
      setIsExchanging(false)
      setTxHash(undefined)
      setAmount('')

      setStatusSuccess(true)
      setStatusMsg('交易已成功提交并链上确认。')
      setShowStatus(true)
    }
    if (isError) {
      setIsApproving(false)
      setIsExchanging(false)
      setTxHash(undefined)

      setStatusSuccess(false)
      setStatusMsg('交易失败，请稍后重试。')
      setShowStatus(true)
    }
  }, [isSuccess, isError, refetchAllowance, refetchBalance])

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (/^\d*\.?\d*$/.test(value)) {
      setAmount(value)
    }
  }

  const handleMaxClick = () => {
    if (tokenABalance) {
      setAmount(formatUnits(tokenABalance.value, tokenABalance.decimals))
    }
  }

  const amountToReceive = useMemo(() => {
    if (!amount || !exchangeRate) return '0.0'
    try {
      const amountBN = parseUnits(amount, 18)
      const exchangeRateBN = BigInt(exchangeRate.toString())
      const result = (amountBN * exchangeRateBN) / BigInt(1e18)
      return formatUnits(result, 18)
    } catch {
      return '0.0'
    }
  }, [amount, exchangeRate])

  const needsApproval = useMemo(() => {
    if (!amount || typeof allowance !== 'bigint') return false
    try {
      const amountBN = parseUnits(amount, 18)
      return amountBN > allowance
    } catch {
      return false
    }
  }, [amount, allowance])

  const handleSwap = async () => {
    if (!amount) return

    try {
      const amountBN = parseUnits(amount, 18)
      if (needsApproval) {
        setIsApproving(true)
        const approveHash = await approve({
          address: contracts.tokenA.address,
          abi: tokenAbi.abi,
          functionName: 'approve',
          args: [contracts.tokenExchange.address, amountBN],
        })
        setTxHash(approveHash)
      } else {
        setIsExchanging(true)
        const exchangeHash = await exchange({
          address: contracts.tokenExchange.address,
          abi: tokenExchangeAbi.abi,
          functionName: 'exchange',
          args: [amountBN],
        })
        setTxHash(exchangeHash)
      }
    } catch (_err) {
      // eslint-disable-next-line no-console
      console.error(_err)
      setIsApproving(false)
      setIsExchanging(false)

      setStatusSuccess(false)
      setStatusMsg('交易提交失败，可能被用户拒绝或发生错误。')
      setShowStatus(true)
    }
  }

  // open confirm modal first
  const onPrimaryClick = () => {
    if (!isConnected) return // connect handled elsewhere
    if (!amount) return
    setShowConfirm(true)
  }

  const buttonText = () => {
    if (!isConnected) return 'Connect Wallet'
    if (!amount) return 'Enter Amount'
    if (isApproving) return 'Approving...'
    if (needsApproval) return 'Approve BRT'
    if (isExchanging) return 'Exchanging...'
    return 'Exchange Tokens'
  }

  const isButtonDisabled = () => {
    if (!isConnected || !amount || isApproving || isExchanging) return true
    if (tokenABalance && amount) {
      try {
        const amountBN = parseUnits(amount, 18)
        return amountBN > tokenABalance.value
      } catch {
        return true
      }
    }
    return false
  }

  return (
    <div className="w-full max-w-lg rounded-card bg-card p-6 shadow-lg">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-primary-text mb-2">Token Exchange</h2>
        <p className="text-sm text-secondary-text">Exchange your BRT tokens for BRT(one) tokens</p>
      </div>

      <div className="space-y-4">
        {/* From Section */}
        <div className="rounded-lg bg-main p-4 border border-border">
          <div className="flex justify-between items-center mb-2">
            <p className="text-sm text-secondary-text">From</p>
            <p className="text-sm text-secondary-text">
              Balance: {tokenABalance ? formatUnits(tokenABalance.value, tokenABalance.decimals) : '0.0'} BRT
            </p>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                <span className="text-main font-bold">B</span>
              </div>
              <div>
                <p className="text-lg font-semibold text-primary-text">BRT</p>
                <p className="text-xs text-secondary-text">Bitroot Token</p>
              </div>
            </div>
          </div>
        </div>

        {/* Swap Arrow */}
        <div className="flex justify-center">
          <div className="w-10 h-10 rounded-full bg-main border border-border flex items-center justify-center">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="text-secondary-text">
              <path d="M8 2L8 14M8 14L12 10M8 14L4 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        </div>

        {/* To Section */}
        <div className="rounded-lg bg-main p-4 border border-border">
          <div className="flex justify-between items-center mb-2">
            <p className="text-sm text-secondary-text">To</p>
            <p className="text-sm text-secondary-text">
              You will receive: {amountToReceive} BRT(one)
            </p>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-secondary rounded-full flex items-center justify-center">
                <span className="text-main font-bold">B</span>
              </div>
              <div>
                <p className="text-lg font-semibold text-primary-text">BRT(one)</p>
                <p className="text-xs text-secondary-text">Bitroot One Token</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Input Section */}
      <div className="mt-6">
        <label className="text-sm font-medium text-secondary-text mb-2 block">Amount to Exchange</label>
        <div className="relative">
          <input
            type="text"
            className="w-full bg-main rounded-lg p-4 pr-20 text-primary-text text-lg focus:ring-2 focus:ring-primary/50 outline-none border border-border focus:border-primary transition-colors"
            placeholder="0.0"
            value={amount}
            onChange={handleAmountChange}
          />
          <button
            onClick={handleMaxClick}
            className="absolute right-3 top-1/2 -translate-y-1/2 bg-primary/20 px-3 py-1 rounded text-sm text-primary hover:bg-primary/30 transition-colors font-medium"
          >
            MAX
          </button>
        </div>
      </div>

      {/* Exchange Info */}
      <div className="mt-6 p-4 bg-main rounded-lg border border-border">
        <div className="space-y-3 text-sm">
          <div className="flex justify-between items-center">
            <span className="text-secondary-text">Exchange Rate</span>
            <span className="text-primary-text font-medium">
              1 BRT = {exchangeRate ? Number(exchangeRate) : '...'} BRT(one)
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-secondary-text">Network Fee</span>
            <span className="text-primary-text font-medium">~0.0001 ARB</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-secondary-text">You will receive</span>
            <span className="text-primary font-semibold">{amountToReceive} BRT(one)</span>
          </div>
        </div>
      </div>

      {/* Action Button */}
      <button
        className="mt-6 w-full py-4 text-main bg-primary rounded-lg font-semibold hover:opacity-90 disabled:opacity-40 transition-all active:scale-95 text-lg disabled:cursor-not-allowed"
        disabled={isButtonDisabled()}
        onClick={onPrimaryClick}
      >
        {(isApproving || isExchanging) && (
          <div className="inline-flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-main"></div>
            {buttonText()}
          </div>
        )}
        {!isApproving && !isExchanging && buttonText()}
      </button>

      {/* Balance Warning */}
      {amount && tokenABalance && (() => {
        try {
          const amountBN = parseUnits(amount, 18)
          if (amountBN > tokenABalance.value) {
            return (
              <p className="mt-2 text-sm text-red-400">
                Insufficient balance. You have {formatUnits(tokenABalance.value, tokenABalance.decimals)} BRT
              </p>
            )
          }
        } catch {}
        return null
      })()}

      {/* Modals */}
      <ConfirmModal open={showConfirm} amount={amount} onCancel={() => setShowConfirm(false)} onConfirm={handleSwap} />
      <StatusModal open={showStatus} success={statusSuccess} message={statusMsg} onClose={() => setShowStatus(false)} />
    </div>
  )
} 