'use client'

import React, { useState, useMemo, useEffect } from 'react'
import { useAccount, useBalance, useReadContract, useWriteContract, useWaitForTransactionReceipt } from 'wagmi'
import { contracts } from '@/config/contracts'
import { formatUnits, parseUnits } from 'viem'
import tokenExchangeAbi from '@/abis/TokenExchange.json'
import tokenAbi from '@/abis/Token.json'
import { ConfirmModal } from '@/components/ConfirmModal'
import { StatusModal } from '@/components/StatusModal'
import { RateInfoRow } from '@/components/RateInfoRow'
import { TokenSelector } from '@/components/TokenSelector'
import Image from 'next/image'

export function ExchangeForm() {
  const { address, isConnected } = useAccount()
  const [amount, setAmount] = useState('')
  const [isApproving, setIsApproving] = useState(false)
  const [isExchanging, setIsExchanging] = useState(false)
  const [txHash, setTxHash] = useState<`0x${string}` | undefined>()

  // modal states
  const [showConfirm, setShowConfirm] = useState(false)
  const [showStatus, setShowStatus] = useState(false)
  const [statusSuccess, setStatusSuccess] = useState(true)
  const [statusMsg, setStatusMsg] = useState('')

  // token selection (future-proof, currently only A to B)
  const [fromToken, setFromToken] = useState<'A' | 'B'>('A')

  const { data: tokenABalance, refetch: refetchBalance } = useBalance({
    address,
    token: contracts.tokenA.address,
  })

  const { data: allowance, refetch: refetchAllowance } = useReadContract({
    address: contracts.tokenA.address,
    abi: tokenAbi.abi,
    functionName: 'allowance',
    args: [address, contracts.tokenExchange.address],
    query: {
      enabled: isConnected && !!address,
    }
  })

  const { data: exchangeRate } = useReadContract({
    address: contracts.tokenExchange.address,
    abi: tokenExchangeAbi.abi,
    functionName: 'getExchangeRate',
  })

  const { writeContractAsync: approve } = useWriteContract()
  const { writeContractAsync: exchange } = useWriteContract()

  const { isSuccess, isError } = useWaitForTransactionReceipt({ hash: txHash })

  useEffect(() => {
    if (isSuccess) {
      refetchAllowance()
      refetchBalance()
      setIsApproving(false)
      setIsExchanging(false)
      setTxHash(undefined)
      setAmount('')

      setStatusSuccess(true)
      setStatusMsg('交易已成功提交并链上确认。')
      setShowStatus(true)
    }
    if (isError) {
      setIsApproving(false)
      setIsExchanging(false)
      setTxHash(undefined)

      setStatusSuccess(false)
      setStatusMsg('交易失败，请稍后重试。')
      setShowStatus(true)
    }
  }, [isSuccess, isError, refetchAllowance, refetchBalance])

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (/^\d*\.?\d*$/.test(value)) {
      setAmount(value)
    }
  }

  const amountToReceive = useMemo(() => {
    if (!amount || !exchangeRate) return '0.0'
    try {
      const amountBN = parseUnits(amount, 18)
      const exchangeRateBN = BigInt(exchangeRate.toString())
      const result = (amountBN * exchangeRateBN) / BigInt(1e18)
      return formatUnits(result, 18)
    } catch {
      return '0.0'
    }
  }, [amount, exchangeRate])

  const needsApproval = useMemo(() => {
    if (!amount || typeof allowance !== 'bigint') return false
    try {
      const amountBN = parseUnits(amount, 18)
      return amountBN > allowance
    } catch {
      return false
    }
  }, [amount, allowance])

  const handleSwap = async () => {
    if (!amount) return

    try {
      const amountBN = parseUnits(amount, 18)
      if (needsApproval) {
        setIsApproving(true)
        const approveHash = await approve({
          address: contracts.tokenA.address,
          abi: tokenAbi.abi,
          functionName: 'approve',
          args: [contracts.tokenExchange.address, amountBN],
        })
        setTxHash(approveHash)
      } else {
        setIsExchanging(true)
        const exchangeHash = await exchange({
          address: contracts.tokenExchange.address,
          abi: tokenExchangeAbi.abi,
          functionName: 'exchange',
          args: [amountBN],
        })
        setTxHash(exchangeHash)
      }
    } catch (_err) {
      // eslint-disable-next-line no-console
      console.error(_err)
      setIsApproving(false)
      setIsExchanging(false)

      setStatusSuccess(false)
      setStatusMsg('交易提交失败，可能被用户拒绝或发生错误。')
      setShowStatus(true)
    }
  }

  // open confirm modal first
  const onPrimaryClick = () => {
    if (!isConnected) return // connect handled elsewhere
    if (!amount) return
    setShowConfirm(true)
  }

  const buttonText = () => {
    if (!isConnected) return 'Connect Wallet'
    if (!amount) return 'Enter amount'
    if (isApproving) return 'Approving...'
    if (needsApproval) return 'Approve'
    if (isExchanging) return 'Swapping...'
    return 'Swap'
  }

  return (
    <div className="w-full max-w-lg rounded-card bg-card p-6">
      <div className="flex justify-between items-center gap-4">
        {/* From Section */}
        <div className="flex-1 rounded-lg bg-main p-4">
          <p className="text-sm text-secondary-text">Form</p>
          <div className="flex items-center justify-between mt-2">
            <p className="text-2xl font-semibold text-primary-text">BRT</p>
            <Image src="/assets/tokenA.svg" alt="TokenA" width={40} height={40} />
          </div>
        </div>

        {/* Swap Arrow */}
        <div className="flex-shrink-0">
          <Image src="/assets/swap-arrows.svg" alt="swap" width={32} height={32} />
        </div>

        {/* To Section */}
        <div className="flex-1 rounded-lg bg-main p-4">
          <p className="text-sm text-secondary-text">To</p>
          <div className="flex items-center justify-between mt-2">
            <p className="text-2xl font-semibold text-primary-text">BRT(one)</p>
            <Image src="/assets/tokenB.svg" alt="TokenB" width={40} height={40} />
          </div>
        </div>
      </div>
      
      {/* Input Section */}
      <div className="mt-6">
        <label className="text-sm font-medium text-secondary-text">Exchange</label>
        <div className="relative mt-1">
          <input
            type="text"
            className="w-full bg-main rounded-lg p-3 pr-16 text-primary-text focus:ring-2 focus:ring-primary/50 outline-none"
            placeholder="Please enter the exchange quantity"
            value={amount}
            onChange={handleAmountChange}
          />
          <button className="absolute right-2 top-1/2 -translate-y-1/2 bg-gray-700/60 px-2 py-1 rounded text-sm text-secondary-text hover:bg-gray-600/60">
            Max
          </button>
        </div>
      </div>

      {/* Rate & Gas info */}
      <div className="mt-4 space-y-2 text-sm font-medium">
        <div className="flex justify-between text-secondary-text">
          <span>Exchange Rate</span>
          <span>{`1 BRT = ${exchangeRate ? Number(exchangeRate) : '...'} BRT(one)`}</span>
        </div>
        <div className="flex justify-between text-secondary-text">
          <span>Gas: 0.0001 Arb</span>
        </div>
      </div>

      {/* Action Button */}
      <button 
        className="mt-5 w-full py-4 text-main bg-primary rounded-lg font-semibold hover:opacity-90 disabled:opacity-40 transition-all active:scale-95" 
        disabled={!isConnected || !amount || isApproving || isExchanging}
        onClick={onPrimaryClick}
      >
        {buttonText()}
      </button>

      {/* Modals */}
      <ConfirmModal open={showConfirm} amount={amount} onCancel={() => setShowConfirm(false)} onConfirm={handleSwap} />
      <StatusModal open={showStatus} success={statusSuccess} message={statusMsg} onClose={() => setShowStatus(false)} />
    </div>
  )
} 