'use client'

import { Modal } from './Modal'
import Image from 'next/image'

interface Props {
  open: boolean
  amount: string
  amountToReceive: string
  onConfirm: () => void
  onCancel: () => void
}

export function ConfirmModal({ open, amount, amountToReceive, onConfirm, onCancel }: Props) {
  return (
    <Modal open={open} onClose={onCancel}>
      <div className="bg-card rounded-[20px] p-6">
        <h3 className="text-lg font-semibold mb-6 text-primary-text text-center">确认兑换</h3>
        
        {/* Exchange Details */}
        <div className="bg-main rounded-[20px] p-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center">
                <Image 
                  src="/images/brt-old-token.svg" 
                  alt="BRT Token" 
                  width={18} 
                  height={21}
                  className="w-[18px] h-[21px]"
                />
              </div>
              <div>
                <p className="text-xs text-secondary-text">From</p>
                <p className="text-base font-semibold text-primary-text">{amount} BRT</p>
              </div>
            </div>
            
            <div className="w-6 h-6 flex items-center justify-center">
              <Image 
                src="/images/swap-arrow.svg" 
                alt="Swap" 
                width={8} 
                height={14}
                className="text-primary-text"
              />
            </div>
            
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-gray-600 to-gray-800 flex items-center justify-center">
                <Image 
                  src="/images/brt-new-token.svg" 
                  alt="BRT(one) Token" 
                  width={18} 
                  height={21}
                  className="w-[18px] h-[21px]"
                />
              </div>
              <div>
                <p className="text-xs text-secondary-text">To</p>
                <p className="text-base font-semibold text-primary-text">{amountToReceive} BRT(one)</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <button 
            onClick={onCancel} 
            className="flex-1 py-3 rounded-2xl bg-main text-secondary-text font-medium hover:bg-gray-700 transition-colors"
          >
            取消
          </button>
          <button 
            onClick={onConfirm} 
            className="flex-1 py-3 rounded-2xl bg-primary text-card font-medium hover:opacity-90 transition-opacity"
          >
            确认兑换
          </button>
        </div>
      </div>
    </Modal>
  )
} 