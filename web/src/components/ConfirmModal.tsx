'use client'

import { Modal } from './Modal'
import Image from 'next/image'

interface Props {
  open: boolean
  amount: string
  onConfirm: () => void
  onCancel: () => void
}

export function ConfirmModal({ open, amount, onConfirm, onCancel }: Props) {
  return (
    <Modal open={open} onClose={onCancel}>
      <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">确认兑换</h3>
      <div className="flex items-center gap-2 mb-6">
        <Image src="/assets/tokenA.svg" alt="TokenA" width={24} height={24} />
        <span>{amount}</span>
        <span className="text-gray-500">→</span>
        <Image src="/assets/tokenB.svg" alt="TokenB" width={24} height={24} />
      </div>
      <div className="flex justify-end gap-3">
        <button onClick={onCancel} className="px-4 py-2 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-transform active:scale-95">
          取消
        </button>
        <button onClick={onConfirm} className="px-4 py-2 rounded-md bg-primary text-white hover:bg-primary/90 transition-transform active:scale-95">
          确认
        </button>
      </div>
    </Modal>
  )
} 