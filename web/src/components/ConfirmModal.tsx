'use client'

import { Modal } from './Modal'
import Image from 'next/image'

interface Props {
  open: boolean
  amount: string
  amountToReceive: string
  onConfirm: () => void
  onCancel: () => void
  needsApproval?: boolean
}

export function ConfirmModal({ open, amount, amountToReceive, onConfirm, onCancel, needsApproval = false }: Props) {
  return (
    <Modal open={open} onClose={onCancel}>
      <div className="bg-card rounded-[20px] p-6">
        <h3 className="text-lg font-semibold mb-6 text-primary-text text-center">确认兑换</h3>
        
        {/* Exchange Details */}
        <div className="bg-main rounded-[20px] p-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center">
                <Image 
                  src="/images/brt-old-token.svg" 
                  alt="BRT Token" 
                  width={18} 
                  height={21}
                  className="w-[18px] h-[21px]"
                />
              </div>
              <div>
                <p className="text-xs text-secondary-text">From</p>
                <p className="text-base font-semibold text-primary-text">{amount} BRT</p>
              </div>
            </div>
            
            <div className="w-6 h-6 flex items-center justify-center">
              <Image 
                src="/images/swap-arrow.svg" 
                alt="Swap" 
                width={8} 
                height={14}
                className="text-primary-text"
              />
            </div>
            
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-gray-600 to-gray-800 flex items-center justify-center">
                <Image 
                  src="/images/brt-new-token.svg" 
                  alt="BRT(one) Token" 
                  width={18} 
                  height={21}
                  className="w-[18px] h-[21px]"
                />
              </div>
              <div>
                <p className="text-xs text-secondary-text">To</p>
                <p className="text-base font-semibold text-primary-text">{amountToReceive} BRT(one)</p>
              </div>
            </div>
          </div>
        </div>

        {/* Authorization Warning */}
        {needsApproval && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-[16px] p-4 mb-6">
            <div className="flex items-center gap-2 text-red-400">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <span className="text-sm font-medium">需要先完成授权</span>
            </div>
            <p className="text-xs text-red-300 mt-1">请先授权 BRT 代币，然后再进行兑换操作</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          <button
            onClick={onCancel}
            className="flex-1 py-3 rounded-2xl bg-main text-secondary-text font-medium hover:bg-gray-700 transition-colors"
          >
            取消
          </button>
          <button
            onClick={onConfirm}
            disabled={needsApproval}
            className={`flex-1 py-3 rounded-2xl font-medium transition-opacity ${
              needsApproval
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-primary text-card hover:opacity-90'
            }`}
          >
            {needsApproval ? '需要授权' : '确认兑换'}
          </button>
        </div>
      </div>
    </Modal>
  )
} 