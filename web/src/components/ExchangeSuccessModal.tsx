'use client'

import React from 'react'
import Image from 'next/image'

interface ExchangeSuccessModalProps {
  open: boolean
  amount: string
  receivedAmount: string
  txHash?: string
  onClose: () => void
}

export function ExchangeSuccessModal({ 
  open, 
  amount, 
  receivedAmount, 
  txHash, 
  onClose 
}: ExchangeSuccessModalProps) {
  if (!open) return null

  const handleViewTransaction = () => {
    if (txHash) {
      // Open Arbitrum explorer
      window.open(`https://arbiscan.io/tx/${txHash}`, '_blank')
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-card rounded-[20px] p-8 max-w-md w-full mx-4">
        {/* Success Icon */}
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
            <svg 
              width="32" 
              height="32" 
              viewBox="0 0 24 24" 
              fill="none" 
              className="text-card"
            >
              <path 
                d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>

        {/* Title */}
        <h2 className="text-2xl font-bold text-white text-center mb-2">
          Exchange Successful!
        </h2>
        
        {/* Description */}
        <p className="text-gray-400 text-center mb-6">
          Your tokens have been successfully exchanged
        </p>

        {/* Exchange Details */}
        <div className="bg-main rounded-[16px] p-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                <Image 
                  src="/images/brt-old-token.svg" 
                  alt="BRT Token" 
                  width={14} 
                  height={16}
                  className="w-[14px] h-[16px]"
                />
              </div>
              <div>
                <p className="text-white font-medium">BRT</p>
                <p className="text-xs text-gray-400">Exchanged</p>
              </div>
            </div>
            <p className="text-white font-semibold">-{amount}</p>
          </div>

          <div className="flex items-center justify-center mb-4">
            <div className="w-6 h-6 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" className="text-gray-400">
                <path d="M8 2L8 14M8 14L12 10M8 14L4 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-gray-600 to-gray-800 flex items-center justify-center">
                <Image 
                  src="/images/brt-new-token.svg" 
                  alt="BRT(one) Token" 
                  width={14} 
                  height={16}
                  className="w-[14px] h-[16px]"
                />
              </div>
              <div>
                <p className="text-white font-medium">BRT(one)</p>
                <p className="text-xs text-gray-400">Received</p>
              </div>
            </div>
            <p className="text-primary font-semibold">+{receivedAmount}</p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          {txHash && (
            <button
              onClick={handleViewTransaction}
              className="w-full py-3 bg-main rounded-[16px] text-white font-medium hover:bg-gray-700 transition-colors"
            >
              View Transaction
            </button>
          )}
          
          <button
            onClick={onClose}
            className="w-full py-3 bg-primary rounded-[16px] text-card font-medium hover:opacity-90 transition-opacity"
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  )
}
