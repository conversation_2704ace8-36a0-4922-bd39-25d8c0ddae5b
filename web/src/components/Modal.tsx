'use client'

import { ReactNode, useEffect } from 'react'

interface ModalProps {
  open: boolean
  onClose: () => void
  children: ReactNode
  dismissable?: boolean
}

export function Modal({ open, onClose, children, dismissable = true }: ModalProps) {
  useEffect(() => {
    if (!open) return
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && dismissable) {
        onClose()
      }
    }
    window.addEventListener('keydown', handleEsc)
    return () => window.removeEventListener('keydown', handleEsc)
  }, [open, dismissable, onClose])

  if (!open) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 p-4 animate-fade-in" onClick={dismissable ? onClose : undefined}>
      <div className="w-full max-w-md rounded-card bg-white dark:bg-gray-800 p-6 animate-scale-in" onClick={(e) => e.stopPropagation()}>
        {children}
      </div>
    </div>
  )
} 