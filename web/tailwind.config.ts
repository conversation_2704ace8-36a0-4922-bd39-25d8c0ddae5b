import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './app/**/*.{ts,tsx,mdx}',
    './components/**/*.{ts,tsx,mdx}',
    './src/**/*.{ts,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#00FF8E', // Green primary color from globals.css
          text: '#FFFFFF',
        },
        secondary: {
          DEFAULT: '#FFAD36', // Orange secondary color from globals.css
          text: '#A7A9AB',
        },
        'accent-blue': '#1E88E5',
        main: '#111318', // Dark main background
        card: '#080A0C', // Card background
        border: '#4B5563',
        'primary-text': '#FFFFFF',
        'secondary-text': '#A7A9AB',
      },
      borderRadius: {
        card: '20px', // Updated to match globals.css
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
    },
  },
  plugins: [],
}

export default config