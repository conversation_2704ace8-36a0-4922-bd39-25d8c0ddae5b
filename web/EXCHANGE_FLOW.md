# Token Exchange Flow Documentation

## 完整兑换流程

### 1. 初始状态
- 用户未连接钱包
- 显示 "Connect Wallet" 按钮
- 输入框和Max按钮被禁用
- 不显示余额信息

### 2. 连接钱包
- 用户点击 "Connect Wallet" 按钮
- 弹出钱包选择界面（MetaMask、OKX等）
- 用户选择钱包并授权连接
- 连接成功后更新UI状态

### 3. 网络验证
- 检查当前钱包网络是否为 Bitroot Test Network (Chain ID: 1337)
- 如果网络不正确：
  - 显示网络警告横幅
  - 按钮文本变为 "Switch to Bitroot Test Network"
  - 用户点击后自动切换网络

### 4. 显示余额和启用输入
- 网络正确后：
  - 获取并显示 BRT (tokenA) 余额
  - 获取并显示 BRT(one) (tokenB) 余额
  - 启用输入框和Max按钮
  - 按钮文本变为 "Exchange"

### 5. 输入兑换金额
- 用户输入兑换金额或点击Max按钮
- 实时计算并显示将要接收的代币数量
- 验证输入金额不超过余额
- 根据输入状态更新按钮可用性

### 6. 授权流程（如果需要）
- 检查当前授权额度是否足够
- 如果授权不足：
  - 按钮文本变为 "Approve BRT"
  - 用户点击后发起授权交易
  - 显示 "Approving..." 状态和loading动画
  - 等待授权交易确认
  - 授权完成后按钮文本变为 "Exchange"

### 7. 兑换确认
- 用户点击 "Exchange" 按钮
- 弹出确认模态框，显示：
  - 兑换金额
  - 将要接收的代币数量
  - 兑换比例
  - "Cancel" 和 "Confirm" 按钮

### 8. 发送兑换交易
- 用户点击 "Confirm"
- 关闭确认模态框
- 调用合约的 exchangeAToB 函数
- 按钮文本变为 "Exchanging..."
- 显示loading动画
- 等待用户在钱包中确认交易

### 9. 交易处理
- 交易发送成功后获取交易哈希
- 等待交易在区块链上确认
- 期间保持loading状态
- 用户可以在钱包中查看交易状态

### 10. 交易完成
- 交易确认后：
  - 刷新用户余额
  - 清空输入框
  - 显示成功模态框，包含：
    - 交易哈希
    - 兑换金额
    - 接收金额
    - 区块链浏览器链接
  - 按钮恢复正常状态

### 11. 错误处理
- 网络错误：显示网络切换提示
- 授权失败：显示错误消息，允许重试
- 交易失败：显示错误消息，允许重试
- 余额不足：禁用按钮，显示提示
- 输入无效：禁用按钮，显示提示

## 状态管理

### 按钮状态
- `loading`: 组件初始化中
- `Connect Wallet`: 未连接钱包
- `Switch to Bitroot Test Network`: 网络不正确
- `Approve BRT`: 需要授权
- `Approving...`: 授权中
- `Exchange`: 可以兑换
- `Exchanging...`: 兑换中

### 模态框状态
- 确认模态框：显示兑换详情
- 成功模态框：显示交易结果
- 错误模态框：显示错误信息

### 数据更新
- 连接钱包后：获取余额、授权额度
- 网络切换后：重新获取数据
- 交易成功后：刷新余额、清空输入
- 地址切换后：重新获取所有数据

## 技术实现要点

### 1. 使用 wagmi hooks
- `useAccount`: 获取连接状态和地址
- `useBalance`: 获取代币余额
- `useReadContract`: 读取合约数据（授权额度、兑换比例）
- `useWriteContract`: 发送交易（授权、兑换）
- `useWaitForTransactionReceipt`: 等待交易确认
- `useSwitchChain`: 切换网络

### 2. 状态同步
- 地址变化时重新获取数据
- 网络变化时重新获取数据
- 交易完成时刷新相关数据

### 3. 用户体验
- 清晰的按钮状态提示
- 实时的loading动画
- 详细的错误信息
- 交易进度反馈 