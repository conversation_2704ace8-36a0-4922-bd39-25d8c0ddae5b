# Token Exchange Flow Documentation

## 完整兑换流程

### 1. 初始状态
- 用户未连接钱包
- 显示 "Connect Wallet" 按钮
- 输入框和Max按钮被禁用
- 不显示余额信息

### 2. 连接钱包
- 用户点击 "Connect Wallet" 按钮
- 弹出钱包选择界面（MetaMask、OKX等）
- 用户选择钱包并授权连接
- 连接成功后更新UI状态

### 3. 网络验证
- 检查当前钱包网络是否在支持的网络列表中（使用 chainID 进行检查）
- 支持的网络：Bitroot Test Network (1337), Arbitrum One (42161), Arbitrum Goerli (421614)
- 如果网络不在支持列表中：
  - 显示网络警告横幅
  - 按钮文本变为 "Switch to [目标网络名称]"
  - 用户点击后自动切换到支持的网络

### 4. 显示余额和启用输入
- 网络正确后：
  - 获取并显示 BRT (tokenA) 余额
  - 获取并显示 BRT(one) (tokenB) 余额
  - 启用输入框和Max按钮
  - 按钮文本变为 "Exchange"

### 5. 输入兑换金额
- 用户输入兑换金额或点击Max按钮
- 调用合约的 getExchangeAmount 函数实时计算并显示将要接收的代币数量
- 验证输入金额不超过余额
- 根据输入状态更新按钮可用性

### 6. ERC20 授权流程（关键步骤）
- **默认状态**：用户对任何合约的代币授权额度默认为 0
- **授权检查**：调用 `tokenA.allowance(userAddress, exchangeContractAddress)` 检查当前授权额度
- **授权机制说明**：
  - 合约使用 `oldToken.transferFrom(msg.sender, address(this), amount)` 转移用户代币
  - 用户必须先调用 BRT 代币的 `approve(spender, amount)` 函数授权给 TokenExchange 合约
  - 授权额度必须 >= 兑换金额，否则 `transferFrom` 会失败
- **授权流程判断**：
  - 如果 `allowance < amount`：显示 "Approve BRT" 按钮
  - 如果 `allowance >= amount`：显示 "Exchange" 按钮
- **授权操作流程**：
  - 用户点击 "Approve BRT" 按钮
  - 调用 `tokenA.approve(tokenExchangeAddress, amount)`
  - 显示 "Approving..." 状态和loading动画
  - 等待授权交易在区块链上确认
  - 授权完成后自动调用 `refetchAllowance()` 刷新授权额度
  - 按钮文本自动变为 "Exchange"

### 7. 兑换确认
- 用户点击 "Exchange" 按钮（仅在授权充足时可用）
- 弹出确认模态框，显示：
  - 兑换金额
  - 将要接收的代币数量
  - 兑换比例
  - 授权状态检查（如果授权不足，显示警告并禁用确认按钮）
  - "Cancel" 和 "Confirm" 按钮

### 8. 发送兑换交易
- 用户点击 "Confirm"（仅在授权充足时可用）
- 关闭确认模态框
- 调用 TokenExchange 合约的 `exchange(amount)` 函数
- 合约内部执行：
  - `oldToken.transferFrom(msg.sender, address(this), amount)` - 转移用户的 BRT 代币
  - `Token(address(oldToken)).burn(amount)` - 销毁转移的代币
  - `newToken.transfer(msg.sender, newTokenAmount)` - 发放新代币给用户
- 按钮文本变为 "Exchanging..."
- 显示loading动画
- 等待用户在钱包中确认交易

### 9. 交易处理
- 交易发送成功后获取交易哈希
- 等待交易在区块链上确认
- 期间保持loading状态
- 用户可以在钱包中查看交易状态

### 10. 交易完成
- 交易确认后：
  - 刷新用户余额
  - 清空输入框
  - 显示成功模态框，包含：
    - 交易哈希
    - 兑换金额
    - 接收金额
    - 区块链浏览器链接
  - 按钮恢复正常状态

### 11. 错误处理
- 网络错误：显示网络切换提示
- 授权失败：显示统一样式的错误模态框，允许重试
- 交易失败：显示统一样式的错误模态框，允许重试
- 余额不足：禁用按钮，显示提示
- 输入无效：禁用按钮，显示提示
- ERC20 授权不足：严格检查 allowance，防止 transferFrom 失败
- 确认模态框：显示 ERC20 授权警告，禁用确认按钮直到完成 approve 操作

## 状态管理

### 按钮状态
- `loading`: 组件初始化中
- `Connect Wallet`: 未连接钱包
- `Switch to [网络名称]`: 网络不在支持列表中
- `Approve BRT`: 需要授权
- `Approving...`: 授权中
- `Exchange`: 可以兑换
- `Exchanging...`: 兑换中

### 模态框状态
- 确认模态框：显示兑换详情，包含 ERC20 授权状态检查和警告
- 成功模态框：显示交易结果
- 状态模态框：统一样式显示成功/错误信息，包含重试功能

### 数据更新
- 连接钱包后：获取余额、授权额度
- 网络切换后：重新获取数据
- 交易成功后：刷新余额、清空输入
- 地址切换后：重新获取所有数据

## 技术实现要点

### 1. 使用 wagmi hooks
- `useAccount`: 获取连接状态和地址
- `useBalance`: 获取代币余额
- `useReadContract`: 读取合约数据（授权额度、兑换比例）
- `useWriteContract`: 发送交易（授权、兑换）
- `useWaitForTransactionReceipt`: 等待交易确认
- `useSwitchChain`: 切换网络

### 2. 状态同步
- 地址变化时重新获取数据（余额、授权额度）
- 网络变化时重新获取数据
- 授权交易完成时调用 `refetchAllowance()` 刷新授权额度
- 兑换交易完成时刷新余额数据

### 3. 授权状态检查逻辑
```typescript
const needsApproval = useMemo(() => {
  if (!amount) return false // 没有输入金额时不需要授权
  if (!allowance) return true // 没有授权额度时需要授权
  try {
    const amountBN = parseUnits(amount, 18)
    return amountBN > (allowance as bigint) // 授权额度不足时需要授权
  } catch {
    return true // 解析错误时默认需要授权
  }
}, [amount, allowance])
```

### 3. 用户体验
- 清晰的按钮状态提示
- 实时的loading动画
- 详细的错误信息
- 交易进度反馈 