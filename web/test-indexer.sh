#!/bin/bash

# 测试索引器功能的脚本

echo "🧪 Testing Transaction History Indexer"
echo "======================================"

BASE_URL="http://localhost:3000"

echo ""
echo "1. 📊 Getting indexer status..."
curl -s "$BASE_URL/api/indexer/status" | jq '.'

echo ""
echo "2. 🚀 Starting indexer service..."
curl -s -X POST "$BASE_URL/api/indexer/start" | jq '.'

echo ""
echo "3. ⏰ Waiting 5 seconds for indexer to sync..."
sleep 5

echo ""
echo "4. 📊 Getting updated indexer status..."
curl -s "$BASE_URL/api/indexer/status" | jq '.'

echo ""
echo "5. 🔄 Triggering manual refresh..."
curl -s -X POST "$BASE_URL/api/transactions/refresh" | jq '.'

echo ""
echo "6. 📊 Final indexer status..."
curl -s "$BASE_URL/api/indexer/status" | jq '.'

echo ""
echo "7. 📜 Getting all transactions..."
curl -s "$BASE_URL/api/transactions?userAddress=" | jq '.'

echo ""
echo "8. 🔄 Testing reset functionality..."
curl -s -X POST "$BASE_URL/api/indexer/reset" \
  -H "Content-Type: application/json" \
  -d '{"startBlock": 1, "clearTransactions": false}' | jq '.'

echo ""
echo "✅ Testing completed!"
echo ""
echo "💡 如果你有具体的用户地址，可以这样查询："
echo "curl '$BASE_URL/api/transactions?userAddress=0x...' | jq '.'" 