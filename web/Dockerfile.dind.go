# 基于 Ubuntu dind 镜像
FROM cruizba/ubuntu-dind:jammy-latest

# 安装构建依赖（使用 apt-get）
RUN apt-get update -qq && \
    apt-get install -y -qq \
    make \
    g++ \
    gcc \
    git \
    curl \
    ca-certificates \
    gnupg \
    unzip \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 安装 Go 1.24.x（可根据需要调整版本）
ENV GO_VERSION=1.24.0
RUN curl -fsSL "https://go.dev/dl/go${GO_VERSION}.linux-amd64.tar.gz" -o go.tar.gz \
    && tar -C /usr/local -xzf go.tar.gz \
    && rm go.tar.gz

# 配置 Go 环境变量
ENV PATH="/usr/local/go/bin:${PATH}"

# 设置工作目录
WORKDIR /app

# 标签信息
LABEL maintainer="Bitroot Team"
LABEL description="Golang dind base image with build tools for CI/CD"
LABEL version="1.0.0" 