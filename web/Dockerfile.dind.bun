
FROM cruizba/ubuntu-dind:jammy-latest

# 安装构建依赖（使用 Debian 包管理器）
RUN apt-get update -qq && \
    apt-get install -y -qq \
    python3 \
    make \
    g++ \
    git \
    curl \
    ca-certificates \
    gnupg \
    unzip \
    build-essential \
    && ln -sf python3 /usr/bin/python \
    && rm -rf /var/lib/apt/lists/*

# 安装 Node.js 20.x
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# 安装 Bun
RUN curl -fsSL https://bun.sh/install | bash

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV BUN_INSTALL="/root/.bun"
ENV PATH="$BUN_INSTALL/bin:$PATH"

# 标签信息
LABEL maintainer="Bitroot Team"
LABEL description="Bun base image with build tools for Node.js projects"
LABEL version="1.1.42"
