
## **产品实现文档：TokenExchange 合约交易历史服务**

### **1. 概述**

本文档旨在设计并实现一个后台服务，用于索引（indexing）`TokenExchange.sol` 合约的 `Exchange` 事件。该服务将提供一个可靠、高效的方式来获取和查询用户的历史交易记录，并最终通过 API 将数据提供给前端应用。

**核心目标：**
*   为用户提供一个完整的、可查询的个人兑换历史。
*   确保数据实时性，在用户完成新交易后能快速看到记录。
*   构建一个可独立运行、可维护的后台索引器服务。

### **2. 系统架构**

该服务将作为 `web` 项目内的一个独立模块，主要由以下三部分组成：

1.  **后台索引器 (Backend Indexer Service)**
    *   **职责**: 持续监听、获取和解析 `TokenExchange` 合约的 `Exchange` 事件。
    *   **技术**: 使用 ts 编写，作为 Next.js API 路由的一部分。
    *   **核心逻辑**:
        *   通过 RPC 批量拉取事件日志。
        *   管理轮询频率，以避免 API 限速。
        *   解析事件数据并存入数据库。
        *   维护自身状态（如已处理的最新区块号）。

2.  **数据存储 (SQLite Database)**
    *   **职责**: 持久化存储交易记录和索引器状态。
    *   **技术**: 使用 SQLite，因其轻量、无需独立服务、易于集成。数据库文件将存放在 `*/data/` 目录下。
    

3.  **API 服务 (API Layer)**
    *   **职责**: 向前端提供查询交易历史的接口。
    *   **技术**: 基于 Next.js API Routes (`/pages/api/`) 实现。

### **3. 核心组件详解**

#### **3.1. 后台索引器服务**

这是整个系统的核心，其工作流程如下：

*   **初始化**:
    1.  服务启动时，首先连接到 SQLite 数据库。
    2.  查询 `IndexerState` 表，获取 `last_processed_block`（上一次处理到的区块号）。
    3.  如果表为空，则从配置文件中读取 `START_BLOCK`（合约部署区块）作为起始区块。

*   **轮询与同步逻辑**:
    1.  **批量追赶模式 (Catch-up Mode)**:
        *   获取当前最新的区块号 `latest_block`。
        *   如果 `latest_block - last_processed_block > 1000`，则以 **1000个区块** 为一批次进行处理 (`fromBlock = last_processed_block`, `toBlock = last_processed_block + 999`)。
        *   每次批处理完成后，**暂停 1 秒**。
    2.  **近实时同步模式 (Real-time Mode)**:
        *   当 `latest_block - last_processed_block <= 1000` 时，说明已追上最新区块。
        *   将批处理的区块范围缩小为 `fromBlock = last_processed_block`, `toBlock = latest_block`。
        *   每次同步完成后，**暂停 10 秒**。
    3.  **事件获取**: 使用 `viem` 或 `ethers.js` 的 `getFilterLogs` 方法，只拉取 `TokenExchange` 合约地址和 `Exchange` 事件主题（Topic）相关的日志。
    4.  **数据解析与存储**:
        *   遍历获取到的事件日志。
        *   解析出 `user` (地址), `oldTokenAmount` (数量), `newTokenAmount` (数量) 以及 `transactionHash` 等信息。
        *   将解析后的数据写入 `Transactions` 表。
    5.  **状态更新**: 一批次的事件全部成功存入数据库后，更新 `IndexerState` 表中的 `last_processed_block` 为 `toBlock`。

#### **3.2. 数据库设计 (SQLite)**

需要设计两张表：

1.  **`Transactions` 表**
    *   **用途**: 存储每一笔 `Exchange` 交易的详细信息。
    *   **表结构**:
        ```sql
        CREATE TABLE IF NOT EXISTS Transactions (
            transactionHash TEXT PRIMARY KEY, -- 交易哈希，唯一
            userAddress TEXT NOT NULL,         -- 用户地址
            oldTokenAmount TEXT NOT NULL,      -- 兑换的旧Token数量 (以字符串存储大数)
            newTokenAmount TEXT NOT NULL,      -- 收到的新Token数量 (以字符串存储大数)
            blockNumber INTEGER NOT NULL,      -- 区块号
            timestamp INTEGER NOT NULL         -- 区块时间戳
        );
        -- 为用户地址创建索引，加速查询
        CREATE INDEX IF NOT EXISTS idx_user_address ON Transactions(userAddress);
        ```

2.  **`IndexerState` 表**
    *   **用途**: 记录索引器当前的处理进度，确保服务重启后能从断点处继续。
    *   **表结构**:
        ```sql
        CREATE TABLE IF NOT EXISTS IndexerState (
            id INTEGER PRIMARY KEY CHECK (id = 1), -- 确保只有一行数据
            last_processed_block INTEGER NOT NULL
        );
        ```

#### **3.3. API 接口设计**

1.  **获取用户交易历史**
    *   **Endpoint**: `GET /api/transactions`
    *   **Query Params**: `userAddress: string`
    *   **功能**: 根据 `userAddress` 查询 `Transactions` 表，返回该用户的所有交易记录，按 `timestamp` 降序排列。
    *   **成功响应 (200 OK)**:
        ```json
        [
          {
            "transactionHash": "0x...",
            "userAddress": "0x...",
            "oldTokenAmount": "1000000000000000000",
            "newTokenAmount": "9523800000000000000000",
            "timestamp": 1672531200
          }
        ]
        ```

2.  **触发交易记录刷新（可选，用于优化体验）**
    *   **Endpoint**: `POST /api/transactions/refresh`
    *   **功能**: 这个接口可以被前端在用户完成一笔新交易后调用。它会立即触发一次索引器服务的数据同步，而不是等待 10 秒的轮询周期。这能让用户更快地看到最新的交易记录。

### **4. 核心用户流程**

1.  **首次加载**:
    *   前端页面加载时，调用 `GET /api/transactions?userAddress=<...>` 获取当前用户的历史交易并展示。

2.  **用户完成新交易**:
    *   前端使用 wagmi/viem 的 hook 监听到用户交易成功（`isSuccess` 为 `true`）。
    *   交易成功后，前端立即调用 `POST /api/transactions/refresh` (如果实现该接口) 来请求后端立即同步。
    *   然后，再次调用 `GET /api/transactions?userAddress=<...>` 来刷新前端显示的交易列表。

### **5. 配置文件**

在 `web` 目录下创建或使用 `.env.local` 文件来管理配置项：

```env
# 索引器相关配置
INDEXER_RPC_URL="your_rpc_url"
INDEXER_CONTRACT_ADDRESS="0x..."
INDEXER_START_BLOCK=1234567
INDEXER_DB_PATH="./data/transactions.db"
```

---

这份文档详细定义了服务的架构、组件、数据流和接口，AI 可以根据此文档直接进行代码实现。