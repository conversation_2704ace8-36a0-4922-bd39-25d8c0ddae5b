when:
  event: manual
  evaluate: CI_MANUAL_TARGET == "frontend"
steps:
  - name: docker
    image: woodpeckerci/plugin-docker-buildx
    settings:
      repo: coinflow/token-exchange
      dockerfile: web/Dockerfile
      context: web/
      platforms:
        - linux/amd64
        # - linux/arm64/v8
      tags:
        - latest
      username:
        from_secret: DOC<PERSON>RHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
      cache_from:
        - coinflow/token-exchange:latest
      cache_to:
        - type=inline
    volumes:
      - bun_cache:/root/.bun/install/cache
      - node_modules_cache:/app/node_modules
      - docker_cache:/tmp/.buildx-cache
