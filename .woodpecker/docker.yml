# Woodpecker CI 配置 - Docker 镜像构建
when:
  - event: [push, tag, manual]
  - branch: [main, develop]

variables:
  - &buildx_image 'woodpeckerci/plugin-docker-buildx'

steps:
  # 克隆代码
  clone:
    image: woodpeckerci/plugin-git
    settings:
      depth: 1

  # 准备构建环境
  prepare:
    image: alpine:latest
    commands:
      - echo "Building Docker image for commit ${CI_COMMIT_SHA:0:8}"
      - echo "Branch: ${CI_COMMIT_BRANCH}"
      - echo "Event: ${CI_PIPELINE_EVENT}"
      - |
        if [ "${CI_PIPELINE_EVENT}" = "tag" ]; then
          echo "DOCKER_TAG=${CI_COMMIT_TAG}" >> $CI_ENV
        elif [ "${CI_COMMIT_BRANCH}" = "main" ]; then
          echo "DOCKER_TAG=latest" >> $CI_ENV
        else
          echo "DOCKER_TAG=${CI_COMMIT_BRANCH}-${CI_COMMIT_SHA:0:8}" >> $CI_ENV
        fi
      - echo "Docker tag will be: ${DOCKER_TAG}"

  # 构建多架构 Docker 镜像
  build-docker:
    image: *buildx_image
    settings:
      registry: 
        from_secret: docker_registry
      username:
        from_secret: docker_username
      password:
        from_secret: docker_password
      repo: bitroot/token-exchange
      dockerfile: web/Dockerfile
      context: web/
      platforms:
        - linux/amd64
        - linux/arm64/v8
      tags:
        - ${DOCKER_TAG}
        - ${CI_COMMIT_SHA:0:8}
      build_args:
        - NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=${WALLETCONNECT_PROJECT_ID}
        - NODE_ENV=production
      cache_from:
        - bitroot/token-exchange:buildcache
      cache_to:
        - bitroot/token-exchange:buildcache
    environment:
      WALLETCONNECT_PROJECT_ID:
        from_secret: walletconnect_project_id
    when:
      - event: [push, tag, manual]

  # 安全扫描
  security-scan:
    image: aquasec/trivy:latest
    commands:
      - trivy image --exit-code 0 --severity HIGH,CRITICAL bitroot/token-exchange:${DOCKER_TAG}
    when:
      - event: [push, tag, manual]
    depends_on:
      - build-docker

  # 推送到生产环境 (仅限 main 分支和 tag)
  deploy-production:
    image: alpine/curl:latest
    commands:
      - |
        if [ "${CI_PIPELINE_EVENT}" = "tag" ] || [ "${CI_COMMIT_BRANCH}" = "main" ]; then
          echo "Triggering production deployment..."
          curl -X POST \
            -H "Authorization: Bearer ${DEPLOY_TOKEN}" \
            -H "Content-Type: application/json" \
            -d "{\"image\":\"bitroot/token-exchange:${DOCKER_TAG}\",\"commit\":\"${CI_COMMIT_SHA}\"}" \
            "${DEPLOY_WEBHOOK_URL}"
        else
          echo "Skipping production deployment for branch ${CI_COMMIT_BRANCH}"
        fi
    environment:
      DEPLOY_TOKEN:
        from_secret: deploy_token
      DEPLOY_WEBHOOK_URL:
        from_secret: deploy_webhook_url
    when:
      - event: [push, tag, manual]
      - branch: [main]
    depends_on:
      - security-scan

  # 清理旧镜像
  cleanup:
    image: alpine/curl:latest
    commands:
      - |
        echo "Cleaning up old Docker images..."
        # 这里可以添加清理逻辑，比如删除超过 30 天的镜像
        echo "Cleanup completed"
    when:
      - event: [push, tag, manual]
      - status: [success, failure]

  # 通知结果
  notify:
    image: plugins/webhook
    settings:
      urls:
        from_secret: webhook_url
      content_type: application/json
      template: |
        {
          "text": "Docker Build {{ build.status }} for {{ repo.name }}#{{ build.number }}",
          "attachments": [{
            "color": "{{ #success build.status }}good{{ else }}danger{{ /success }}",
            "title": "{{ repo.name }} - Docker Build #{{ build.number }}",
            "title_link": "{{ build.link }}",
            "text": "{{ build.message }}",
            "fields": [{
              "title": "Branch",
              "value": "{{ build.branch }}",
              "short": true
            }, {
              "title": "Tag",
              "value": "${DOCKER_TAG}",
              "short": true
            }, {
              "title": "Platforms",
              "value": "linux/amd64, linux/arm64/v8",
              "short": true
            }, {
              "title": "Registry",
              "value": "bitroot/token-exchange:${DOCKER_TAG}",
              "short": false
            }]
          }]
        }
    when:
      - event: [push, tag, manual]
      - status: [success, failure]

# 环境变量
environment:
  DOCKER_BUILDKIT: 1
  BUILDX_NO_DEFAULT_ATTESTATIONS: 1
