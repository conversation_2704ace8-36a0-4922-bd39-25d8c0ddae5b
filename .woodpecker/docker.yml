steps:
  docker:
    image: woodpeckerci/plugin-docker-buildx
    settings:
      repo: coinflow/token-exchange
      dockerfile: web/Dockerfile
      context: web/
      platforms:
        - linux/amd64
        - linux/arm64/v8
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
    volumes:
      - bun_cache:/root/.bun/install/cache
      - node_modules_cache:/app/node_modules
    when:
      event: manual
