steps:
  docker:
    image: woodpeckerci/plugin-docker-buildx
    # privileged: true
    settings:
      repo: coinflow/token-exchange
      dockerfile: web/Dockerfile
      context: web/
      platforms:
        - linux/amd64
        - linux/arm64/v8
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
    volumes:
      - name: bun_cache
        path: /root/.bun/install/cache
      - name: node_modules_cache
        path: /app/node_modules
    when:
      branch: main
      event: push
