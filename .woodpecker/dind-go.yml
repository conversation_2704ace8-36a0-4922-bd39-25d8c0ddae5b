when:
  event: manual
  environment: CI_MANUAL_TARGET == "dind-go"

steps:
  - name: dind-go
    image: woodpeckerci/plugin-docker-buildx
    settings:
      repo: coinflow/dind-go
      dockerfile: web/Dockerfile.dind.go
      context: web/
      platforms:
        - linux/amd64
        - linux/arm64/v8
      tags:
        - latest
      username:
        from_secret: DOCKERHUB_USERNAME
      password:
        from_secret: DOCKERHUB_TOKEN
    