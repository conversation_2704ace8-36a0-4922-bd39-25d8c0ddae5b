# Alternative configuration without volumes (use if trust level issues persist)
steps:
  test:
    image: ghcr.io/foundry-rs/foundry:latest
    commands:
      - cd contracts
      # Fix git safe directory issue in CI
      - git config --global --add safe.directory /woodpecker/src/github.com/brt-chain/redeemer
      # Install dependencies manually since we don't use git submodules
      - mkdir -p dependencies
      - |
        if [ ! -d "dependencies/forge-std-1.9.7" ]; then
          echo "Downloading forge-std..."
          curl -L https://github.com/foundry-rs/forge-std/archive/v1.9.7.tar.gz | tar -xz -C dependencies/
        fi
      - |
        if [ ! -d "dependencies/openzeppelin-contracts-5.3.0" ]; then
          echo "Downloading openzeppelin-contracts..."
          curl -L https://github.com/OpenZeppelin/openzeppelin-contracts/archive/v5.3.0.tar.gz | tar -xz -C dependencies/
        fi
      - forge fmt --check
      - forge build --sizes
      - forge test -vvv
    when:
      branch: main
      event: push
