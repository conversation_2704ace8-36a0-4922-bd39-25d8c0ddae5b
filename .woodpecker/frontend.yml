# Woodpecker CI 配置 - 前端 Lint 和 Build
when:
  - event: [push, pull_request, manual]
  - path:
      include: ['web/**', '.woodpecker/frontend.yml', 'package.json', 'bun.lockb']

variables:
  - &node_image 'oven/bun:1.1.34'

steps:
  # 克隆代码
  clone:
    image: woodpeckerci/plugin-git
    settings:
      depth: 1

  # 缓存 node_modules
  restore-cache:
    image: meltwater/drone-cache
    settings:
      backend: filesystem
      restore: true
      cache_key: 'bun-{{ checksum "web/bun.lockb" }}'
      archive_format: gzip
      mount:
        - web/node_modules
        - ~/.bun/install/cache
    volumes:
      - /tmp/woodpecker-cache:/tmp/cache

  # 安装依赖
  install:
    image: *node_image
    commands:
      - cd web
      - bun install --frozen-lockfile
    when:
      - event: [push, pull_request, manual]

  # 代码格式检查
  lint:
    image: *node_image
    commands:
      - cd web
      - bun run lint
    when:
      - event: [push, pull_request, manual]

  # TypeScript 类型检查
  typecheck:
    image: *node_image
    commands:
      - cd web
      - bun run type-check
    when:
      - event: [push, pull_request, manual]

  # 运行测试
  test:
    image: *node_image
    commands:
      - cd web
      - bun run test
    when:
      - event: [push, pull_request, manual]

  # 构建项目
  build:
    image: *node_image
    commands:
      - cd web
      - bun run build
    when:
      - event: [push, pull_request, manual]

  # 检查构建产物
  build-check:
    image: *node_image
    commands:
      - cd web
      - ls -la .next/
      - du -sh .next/
    when:
      - event: [push, pull_request, manual]

  # 保存缓存
  rebuild-cache:
    image: meltwater/drone-cache
    settings:
      backend: filesystem
      rebuild: true
      cache_key: 'bun-{{ checksum "web/bun.lockb" }}'
      archive_format: gzip
      mount:
        - web/node_modules
        - ~/.bun/install/cache
    volumes:
      - /tmp/woodpecker-cache:/tmp/cache
    when:
      - event: [push, pull_request, manual]

  # 通知结果
  notify:
    image: plugins/webhook
    settings:
      urls:
        from_secret: webhook_url
      content_type: application/json
      template: |
        {
          "text": "Frontend CI {{ build.status }} for {{ repo.name }}#{{ build.number }}",
          "attachments": [{
            "color": "{{ #success build.status }}good{{ else }}danger{{ /success }}",
            "title": "{{ repo.name }} - Build #{{ build.number }}",
            "title_link": "{{ build.link }}",
            "text": "{{ build.message }}",
            "fields": [{
              "title": "Branch",
              "value": "{{ build.branch }}",
              "short": true
            }, {
              "title": "Commit",
              "value": "{{ build.commit }}",
              "short": true
            }, {
              "title": "Event",
              "value": "{{ build.event }}",
              "short": true
            }]
          }]
        }
    when:
      - event: [push, pull_request, manual]
      - status: [success, failure]

# 环境变量
environment:
  NODE_ENV: production
  NEXT_TELEMETRY_DISABLED: 1
