steps:
  test:
    image: ghcr.io/foundry-rs/foundry:latest
    commands:
      - cd contracts
      # Fix git safe directory issue in CI
      - git config --global --add safe.directory /woodpecker/src/github.com/brt-chain/redeemer
      # Install curl if not available
      - apt-get update -qq && apt-get install -y -qq curl
      # Clean and create dependencies directory
      - rm -rf dependencies
      - mkdir -p dependencies
      # Download forge-std v1.9.7 (matches foundry.toml)
      - curl -L https://github.com/foundry-rs/forge-std/archive/v1.9.7.tar.gz | tar -xz -C dependencies/
      # Download OpenZeppelin contracts v5.3.0 (matches foundry.toml)
      - curl -L https://github.com/OpenZeppelin/openzeppelin-contracts/archive/v5.3.0.tar.gz | tar -xz -C dependencies/
      - forge fmt --check
      - forge build --sizes
      - forge test -vvv
    when:
      branch: main
      event: push
