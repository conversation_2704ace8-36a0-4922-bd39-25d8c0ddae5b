steps:
  test:
    image: ghcr.io/foundry-rs/foundry:latest
    commands:
      - cd contracts
      # Fix git safe directory issue in CI
      - git config --global --add safe.directory /woodpecker/src/github.com/brt-chain/redeemer
      - rm -rf dependencies
      - forge install
      - forge install foundry-rs/forge-std
      - forge install OpenZeppelin/openzeppelin-contracts@v5.3.0
      - forge fmt --check
      - forge build --sizes
      - forge test -vvv
    when:
      branch: main
      event: push
