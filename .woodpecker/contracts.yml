# Woodpecker CI 配置 - 合约测试
when:
  - event: [push, pull_request, manual]
  - path:
      include: ['contracts/**', '.woodpecker/contracts.yml']

variables:
  - &foundry_image 'ghcr.io/foundry-rs/foundry:latest'

steps:
  # 克隆代码
  clone:
    image: woodpeckerci/plugin-git
    settings:
      depth: 1

  # 缓存 Foundry 依赖
  restore-cache:
    image: meltwater/drone-cache
    settings:
      backend: filesystem
      restore: true
      cache_key: 'foundry-{{ checksum "contracts/foundry.toml" }}'
      archive_format: gzip
      mount:
        - contracts/lib
        - ~/.foundry
    volumes:
      - /tmp/woodpecker-cache:/tmp/cache

  # 安装 Foundry 依赖
  install-deps:
    image: *foundry_image
    commands:
      - cd contracts
      - forge install --no-commit
    when:
      - event: [push, pull_request, manual]

  # 检查代码格式
  format-check:
    image: *foundry_image
    commands:
      - cd contracts
      - forge fmt --check
    when:
      - event: [push, pull_request, manual]

  # 构建合约
  build:
    image: *foundry_image
    commands:
      - cd contracts
      - forge build --sizes
    when:
      - event: [push, pull_request, manual]

  # 运行测试
  test:
    image: *foundry_image
    commands:
      - cd contracts
      - forge test -vvv
    when:
      - event: [push, pull_request, manual]

  # 生成测试覆盖率报告
  coverage:
    image: *foundry_image
    commands:
      - cd contracts
      - forge coverage --report lcov
    when:
      - event: [push, pull_request, manual]

  # 保存缓存
  rebuild-cache:
    image: meltwater/drone-cache
    settings:
      backend: filesystem
      rebuild: true
      cache_key: 'foundry-{{ checksum "contracts/foundry.toml" }}'
      archive_format: gzip
      mount:
        - contracts/lib
        - ~/.foundry
    volumes:
      - /tmp/woodpecker-cache:/tmp/cache
    when:
      - event: [push, pull_request, manual]

  # 通知结果
  notify:
    image: plugins/webhook
    settings:
      urls:
        from_secret: webhook_url
      content_type: application/json
      template: |
        {
          "text": "Contracts CI {{ build.status }} for {{ repo.name }}#{{ build.number }}",
          "attachments": [{
            "color": "{{ #success build.status }}good{{ else }}danger{{ /success }}",
            "title": "{{ repo.name }} - Build #{{ build.number }}",
            "title_link": "{{ build.link }}",
            "text": "{{ build.message }}",
            "fields": [{
              "title": "Branch",
              "value": "{{ build.branch }}",
              "short": true
            }, {
              "title": "Commit",
              "value": "{{ build.commit }}",
              "short": true
            }]
          }]
        }
    when:
      - event: [push, pull_request, manual]
      - status: [success, failure]

# 服务配置
services:
  # 可以添加数据库或其他服务
  # postgres:
  #   image: postgres:15
  #   environment:
  #     POSTGRES_DB: test
  #     POSTGRES_USER: test
  #     POSTGRES_PASSWORD: test
