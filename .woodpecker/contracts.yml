steps:
  test:
    image: ghcr.io/foundry-rs/foundry:latest
    # Note: If you get "Insufficient trust level to use volumes" error,
    # comment out the volumes section below and dependencies will be downloaded each time
    volumes:
      - foundry_dependencies_cache:/woodpecker/src/github.com/brt-chain/redeemer/contracts/dependencies
    commands:
      - cd contracts
      # Fix git safe directory issue in CI
      - git config --global --add safe.directory /woodpecker/src/github.com/brt-chain/redeemer
      # Install dependencies manually since we don't use git submodules
      - |
        if [ ! -d "dependencies/forge-std-1.9.7" ]; then
          mkdir -p dependencies
          curl -L https://github.com/foundry-rs/forge-std/archive/v1.9.7.tar.gz | tar -xz -C dependencies/
        fi
      - |
        if [ ! -d "dependencies/openzeppelin-contracts-5.3.0" ]; then
          mkdir -p dependencies
          curl -L https://github.com/OpenZeppelin/openzeppelin-contracts/archive/v5.3.0.tar.gz | tar -xz -C dependencies/
        fi
      - forge fmt --check
      - forge build --sizes
      - forge test -vvv
    when:
      branch: main
      event: push
