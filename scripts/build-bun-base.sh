#!/bin/bash

# 构建和推送 bun-base 镜像的脚本
# 使用方法: ./scripts/build-bun-base.sh

set -e

echo "🔨 构建 coinflow/bun-base 镜像..."
echo "📦 使用 Node.js 18.x + Bun latest 确保兼容性"

# 切换到 web 目录
cd web

# 先测试本地构建
echo "🧪 测试本地构建..."
docker build -f Dockerfile.bun-base -t coinflow/bun-base:test .

# 测试 Node.js 版本
echo "🔍 检查 Node.js 版本..."
docker run --rm coinflow/bun-base:test node --version

if docker buildx version >/dev/null 2>&1; then
  echo "🚀 使用 buildx 构建多架构镜像..."
  docker buildx build \
    --platform linux/amd64,linux/arm64/v8 \
    -f Dockerfile.bun-base \
    -t coinflow/bun-base:latest \
    --push \
    .
else
  echo "⚠️ 你的 Docker 不支持 buildx，使用单架构构建..."
  docker build -f Dockerfile.bun-base -t coinflow/bun-base:latest .
fi

echo "✅ coinflow/bun-base 镜像构建并推送成功！"
echo ""
echo "修复的问题："
echo "- contracts.yml: ✅ 安装了 curl"
echo "- frontend.yml: ✅ 使用 Node.js 18.x"
echo "- docker.yml: ✅ 使用 Node.js 18.x"
echo "- package.json: ✅ lint 和 build 命令现在兼容"
