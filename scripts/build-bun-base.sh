#!/bin/bash

# 构建和推送 bun-base 镜像的脚本
# 使用方法: ./scripts/build-bun-base.sh

set -e

echo "🔨 构建 coinflow/bun-base 镜像..."

# 切换到 web 目录
cd web

if docker buildx version >/dev/null 2>&1; then
  echo "🔨 使用 buildx 构建多架构镜像..."
  docker buildx build \
    --platform linux/amd64,linux/arm64/v8 \
    -f Dockerfile.bun-base \
    -t coinflow/bun-base:latest \
    --push \
    .
else
  echo "⚠️ 你的 Docker 不支持 buildx，使用单架构构建..."
  docker build -f Dockerfile.bun-base -t coinflow/bun-base:latest .
fi

echo "✅ coinflow/bun-base 镜像构建并推送成功！"
