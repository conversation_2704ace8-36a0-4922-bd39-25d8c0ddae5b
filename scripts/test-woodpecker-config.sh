#!/bin/bash

# Test script to validate Woodpecker configurations

echo "🔍 Testing Woodpecker configurations..."

# Check if configuration files exist
configs=(".woodpecker/contracts.yml" ".woodpecker/frontend.yml" ".woodpecker/docker.yml")
backup_configs=(".woodpecker/contracts-no-volumes.yml" ".woodpecker/frontend-no-volumes.yml" ".woodpecker/docker-no-volumes.yml")

echo "📁 Checking configuration files..."
for config in "${configs[@]}"; do
    if [ -f "$config" ]; then
        echo "✅ $config exists"
    else
        echo "❌ $config missing"
    fi
done

echo "📁 Checking backup configurations..."
for config in "${backup_configs[@]}"; do
    if [ -f "$config" ]; then
        echo "✅ $config exists"
    else
        echo "❌ $config missing"
    fi
done

# Validate YAML syntax (if yq is available)
if command -v yq &> /dev/null; then
    echo "🔍 Validating YAML syntax..."
    for config in "${configs[@]}" "${backup_configs[@]}"; do
        if [ -f "$config" ]; then
            if yq eval . "$config" > /dev/null 2>&1; then
                echo "✅ $config has valid YAML syntax"
            else
                echo "❌ $config has invalid YAML syntax"
            fi
        fi
    done
else
    echo "⚠️  yq not found, skipping YAML validation"
fi

echo "📋 Configuration summary:"
echo "- Main configs use volumes for caching (requires trusted repo)"
echo "- Backup configs don't use volumes (works without trust level)"
echo "- If you get 'Insufficient trust level' errors, rename backup configs to replace main ones"

echo "🎯 Next steps:"
echo "1. Push changes and test with main configurations"
echo "2. If trust level errors occur, use backup configurations"
echo "3. Configure Woodpecker server trust settings if needed"
