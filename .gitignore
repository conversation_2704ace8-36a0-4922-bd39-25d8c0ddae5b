# Foundry
out/
cache/
broadcast/
lib/

# Environment variables
.env
.env.local
.env.production

# Node modules (if using frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Coverage
coverage/
coverage.json

# Gas reports
gas-report.txt

# Deployment artifacts
deployments/

# Test artifacts
test-artifacts/

# Temporary files
*.tmp
*.temp

# Documentation build
docs/build/

# Local development
.local/ 
lcov.info
 