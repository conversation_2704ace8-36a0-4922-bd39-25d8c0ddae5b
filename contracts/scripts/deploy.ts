import { ethers } from "hardhat";
import { writeFileSync } from "fs";
import { join } from "path";

async function main() {
  const [deployer] = await ethers.getSigners();
  const network = await ethers.provider.getNetwork();
  
  console.log("Deploying contracts with the account:", deployer.address);
  console.log("Account balance:", ethers.formatEther(await ethers.provider.getBalance(deployer.address)));
  console.log("Network:", network.name, "Chain ID:", network.chainId);

  // Deploy Old Token (for testing purposes)
  console.log("\n🚀 Deploying Old Token...");
  const OldToken = await ethers.getContractFactory("Token");
  const oldTokenSupply = ethers.parseEther("1000000"); // 1M tokens
  const oldToken = await OldToken.deploy(
    "BitRoot Old Token",
    "BRTOLD",
    oldTokenSupply,
    deployer.address
  );
  await oldToken.waitForDeployment();
  const oldTokenAddress = await oldToken.getAddress();
  console.log("✅ Old Token deployed to:", oldTokenAddress);

  // Deploy New Token
  console.log("\n🚀 Deploying New Token...");
  const NewToken = await ethers.getContractFactory("Token");
  const newTokenSupply = ethers.parseEther("2000000"); // 2M tokens (to support 2:1 exchange rate)
  const newToken = await NewToken.deploy(
    "BitRoot New Token",
    "BRTNEW",
    newTokenSupply,
    deployer.address
  );
  await newToken.waitForDeployment();
  const newTokenAddress = await newToken.getAddress();
  console.log("✅ New Token deployed to:", newTokenAddress);

  // Deploy Token Exchange
  console.log("\n🚀 Deploying Token Exchange...");
  const TokenExchange = await ethers.getContractFactory("TokenExchange");
  
  const tokenExchange = await TokenExchange.deploy(
    oldTokenAddress,
    newTokenAddress,
    deployer.address
  );
  await tokenExchange.waitForDeployment();
  const tokenExchangeAddress = await tokenExchange.getAddress();
  console.log("✅ Token Exchange deployed to:", tokenExchangeAddress);

  // Transfer new tokens to exchange contract
  console.log("\n💰 Funding Token Exchange...");
  const exchangeFunding = ethers.parseEther("1500000"); // 1.5M new tokens
  await newToken.transfer(tokenExchangeAddress, exchangeFunding);
  console.log("✅ Transferred", ethers.formatEther(exchangeFunding), "new tokens to exchange contract");

  // Verify balances
  console.log("\n📊 Final Balances:");
  console.log("Deployer old tokens:", ethers.formatEther(await oldToken.balanceOf(deployer.address)));
  console.log("Deployer new tokens:", ethers.formatEther(await newToken.balanceOf(deployer.address)));
  console.log("Exchange new tokens:", ethers.formatEther(await newToken.balanceOf(tokenExchangeAddress)));

  // Save deployment info
  const deploymentInfo = {
    network: {
      name: network.name,
      chainId: Number(network.chainId),
    },
    deployer: deployer.address,
    contracts: {
      oldToken: {
        address: oldTokenAddress,
        name: "BitRoot Old Token",
        symbol: "BRTOLD",
        supply: ethers.formatEther(oldTokenSupply),
      },
      newToken: {
        address: newTokenAddress,
        name: "BitRoot New Token",
        symbol: "BRTNEW",
        supply: ethers.formatEther(newTokenSupply),
      },
      tokenExchange: {
        address: tokenExchangeAddress,
      },
    },
    timestamp: new Date().toISOString(),
  };

  const deploymentPath = join(__dirname, `../deployments/${network.name}-${network.chainId}.json`);
  writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
  console.log("\n📄 Deployment info saved to:", deploymentPath);

  console.log("\n🎉 Deployment completed successfully!");
  console.log("\n📋 Summary:");
  console.log("Old Token:", oldTokenAddress);
  console.log("New Token:", newTokenAddress);
  console.log("Token Exchange:", tokenExchangeAddress);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
