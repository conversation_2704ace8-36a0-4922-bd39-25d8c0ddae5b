// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/math/Math.sol";
import "./Token.sol";

contract TokenExchange is Ownable, ReentrancyGuard {
    using Math for uint256;

    IERC20 public immutable oldToken;
    IERC20 public immutable newToken;
    uint256 public exchangeRate;
    uint256 public immutable exchangeEndTime;

    uint256 public totalExchanged;
    uint256 public totalUsers;
    mapping(address => bool) public hasExchanged;
    mapping(address => uint256) public userExchangeAmount;

    event Exchange(
        address indexed user,
        uint256 oldTokenAmount,
        uint256 newTokenAmount,
        uint256 timestamp
    );
    event ExchangeRateUpdated(uint256 oldRate, uint256 newRate);
    event UnclaimedTokensReclaimed(address indexed owner, uint256 amount);
    event EmergencyWithdraw(
        address indexed token,
        address indexed to,
        uint256 amount
    );

    constructor(
        address _oldToken,
        address _newToken,
        address _owner
    ) Ownable(_owner) {
        require(_oldToken != address(0), "oldToken=0");
        require(_newToken != address(0), "newToken=0");
        require(_oldToken != _newToken, "same token");

        oldToken = IERC20(_oldToken);
        newToken = IERC20(_newToken);
        exchangeRate = 9523800000000000000000;
        exchangeEndTime = block.timestamp + 5184000;
    }

    function exchange(uint256 amount) external nonReentrant {
        require(amount > 0, "amount=0");
        require(!isExchangePeriodEnded(), "period ended");
        require(oldToken.balanceOf(msg.sender) >= amount, "oldToken lack");

        uint256 newTokenAmount = getExchangeAmount(amount);
        require(
            newToken.balanceOf(address(this)) >= newTokenAmount,
            "newToken lack"
        );

        if (!hasExchanged[msg.sender]) {
            hasExchanged[msg.sender] = true;
            totalUsers++;
        }
        userExchangeAmount[msg.sender] += amount;
        totalExchanged += amount;

        oldToken.transferFrom(msg.sender, address(this), amount);
        Token(address(oldToken)).burn(amount);
        newToken.transfer(msg.sender, newTokenAmount);

        emit Exchange(msg.sender, amount, newTokenAmount, block.timestamp);
    }

    function getExchangeAmount(uint256 amount) public view returns (uint256) {
        return amount.mulDiv(exchangeRate, 1e18);
    }

    function isExchangePeriodEnded() public view returns (bool) {
        return block.timestamp >= exchangeEndTime;
    }

    function updateExchangeRate(uint256 newRate) external onlyOwner {
        require(!isExchangePeriodEnded(), "period ended");
        require(newRate > 0, "rate=0");

        uint256 oldRate = exchangeRate;
        exchangeRate = newRate;

        emit ExchangeRateUpdated(oldRate, newRate);
    }

    function reclaimUnclaimed() external onlyOwner {
        require(isExchangePeriodEnded(), "period not end");

        uint256 balance = newToken.balanceOf(address(this));
        require(balance > 0, "no tokens");

        newToken.transfer(owner(), balance);
        emit UnclaimedTokensReclaimed(owner(), balance);
    }

    function emergencyWithdraw(
        address token,
        address to,
        uint256 amount
    ) external onlyOwner nonReentrant {
        require(token != address(0), "token=0");
        require(to != address(0), "to=0");

        IERC20(token).transfer(to, amount);
        emit EmergencyWithdraw(token, to, amount);
    }

    function getContractInfo()
        external
        view
        returns (
            uint256 _totalExchanged,
            uint256 _totalUsers,
            uint256 _exchangeRate,
            uint256 _exchangeEndTime,
            bool _isExchangePeriodEnded,
            uint256 _remainingNewTokens
        )
    {
        return (
            totalExchanged,
            totalUsers,
            exchangeRate,
            exchangeEndTime,
            isExchangePeriodEnded(),
            newToken.balanceOf(address(this))
        );
    }
}
