// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/math/Math.sol";
import "./Token.sol";

contract TokenExchange is Ownable, ReentrancyGuard {
    using SafeERC20 for IERC20;
    using SafeERC20 for Token;
    using Math for uint256;

    Token public immutable oldToken;
    IERC20 public immutable newToken;
    // exchangeRate is stored with 1e18 precision.
    // e.g., for a 1:10 rate, exchangeRate should be 10 * 1e18
    uint256 public exchangeRate;
    uint256 public immutable exchangeStartTime;
    uint256 public immutable exchangeEndTime;

    event Exchange(address indexed user, uint256 oldTokenAmount, uint256 newTokenAmount, uint256 timestamp);
    event ExchangeRateUpdated(uint256 oldRate, uint256 newRate);
    event UnclaimedTokensReclaimed(address indexed owner, uint256 amount);

    constructor(address oldToken_, address newToken_, uint256 exchangeRate_, uint256 exchangeEndTime_, address owner_)
        Ownable(owner_)
    {
        require(oldToken_ != address(0), "TokenExchange: oldToken cannot be zero address");
        require(newToken_ != address(0), "TokenExchange: newToken cannot be zero address");
        require(oldToken_ != newToken_, "TokenExchange: oldToken and newToken cannot be the same");
        require(exchangeRate_ > 0, "TokenExchange: exchange rate must be greater than 0");
        require(exchangeEndTime_ > block.timestamp, "TokenExchange: end time must be in the future");
        require(owner_ != address(0), "TokenExchange: owner cannot be zero address");

        oldToken = Token(oldToken_);
        newToken = IERC20(newToken_);
        exchangeRate = exchangeRate_;
        exchangeStartTime = block.timestamp;
        exchangeEndTime = exchangeEndTime_;
    }

    function updateExchangeRate(uint256 newRate) external onlyOwner {
        require(newRate > 0, "TokenExchange: exchange rate must be greater than 0");
        require(!isExchangePeriodEnded(), "TokenExchange: exchange period ended");

        uint256 oldRate = exchangeRate;
        exchangeRate = newRate;

        emit ExchangeRateUpdated(oldRate, newRate);
    }

    function exchange(uint256 oldTokenAmount) external nonReentrant {
        require(oldTokenAmount > 0, "TokenExchange: amount must be greater than 0");
        require(!isExchangePeriodEnded(), "TokenExchange: exchange period ended");

        uint256 newTokenAmount = getExchangeAmount(oldTokenAmount);
        require(newTokenAmount > 0, "TokenExchange: calculated new token amount is zero");

        require(oldToken.balanceOf(msg.sender) >= oldTokenAmount, "TokenExchange: insufficient old token balance");
        require(newToken.balanceOf(address(this)) >= newTokenAmount, "TokenExchange: insufficient new token balance");

        oldToken.safeTransferFrom(msg.sender, address(this), oldTokenAmount);
        oldToken.burn(oldTokenAmount);

        newToken.safeTransfer(msg.sender, newTokenAmount);

        emit Exchange(msg.sender, oldTokenAmount, newTokenAmount, block.timestamp);
    }

    function reclaimUnclaimed() external onlyOwner nonReentrant {
        require(isExchangePeriodEnded(), "TokenExchange: exchange period not ended");

        uint256 remainingBalance = newToken.balanceOf(address(this));
        require(remainingBalance > 0, "TokenExchange: no tokens to reclaim");

        newToken.safeTransfer(owner(), remainingBalance);

        emit UnclaimedTokensReclaimed(owner(), remainingBalance);
    }

    function getExchangeAmount(uint256 oldTokenAmount) public view returns (uint256) {
        // Assumes exchangeRate is stored with 1e18 precision
        return Math.mulDiv(oldTokenAmount, exchangeRate, 1e18);
    }

    function isExchangePeriodEnded() public view returns (bool) {
        return block.timestamp >= exchangeEndTime;
    }

    function getNewTokenBalance() external view returns (uint256) {
        return newToken.balanceOf(address(this));
    }

    function emergencyWithdraw(address token, address to, uint256 amount) external onlyOwner nonReentrant {
        require(token != address(oldToken), "TokenExchange: cannot withdraw oldToken");
        require(token != address(newToken), "TokenExchange: cannot withdraw newToken");
        require(to != address(0), "TokenExchange: withdraw to zero address");
        require(token != address(0), "TokenExchange: token cannot be zero address");

        IERC20(token).safeTransfer(to, amount);
    }

    function getExchangeRate() external view returns (uint256) {
        return exchangeRate;
    }

    function getExchangeEndTime() external view returns (uint256) {
        return exchangeEndTime;
    }
}
