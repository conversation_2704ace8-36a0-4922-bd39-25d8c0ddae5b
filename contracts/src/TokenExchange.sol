// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/math/Math.sol";
import "./Token.sol";

contract TokenExchange is Ownable, ReentrancyGuard {
    using Math for uint256;

    // 状态变量
    IERC20 public immutable oldToken;
    IERC20 public immutable newToken;
    uint256 public exchangeRate; // 兑换率，以18位小数精度表示
    uint256 public immutable exchangeEndTime;

    // 统计数据
    uint256 public totalExchanged;
    uint256 public totalUsers;
    mapping(address => bool) public hasExchanged;
    mapping(address => uint256) public userExchangeAmount;

    // 事件
    event Exchange(address indexed user, uint256 oldTokenAmount, uint256 newTokenAmount, uint256 timestamp);
    event ExchangeRateUpdated(uint256 oldRate, uint256 newRate);
    event UnclaimedTokensReclaimed(address indexed owner, uint256 amount);
    event EmergencyWithdraw(address indexed token, address indexed to, uint256 amount);

    constructor(address _oldToken, address _newToken, uint256 _exchangeRate, uint256 _exchangeEndTime, address _owner)
        Ownable(_owner)
    {
        require(_oldToken != address(0), "TokenExchange: oldToken cannot be zero address");
        require(_newToken != address(0), "TokenExchange: newToken cannot be zero address");
        require(_oldToken != _newToken, "TokenExchange: oldToken and newToken cannot be the same");
        require(_exchangeRate > 0, "TokenExchange: exchange rate must be greater than 0");
        require(_exchangeEndTime > block.timestamp, "TokenExchange: end time must be in the future");

        oldToken = IERC20(_oldToken);
        newToken = IERC20(_newToken);
        exchangeRate = _exchangeRate;
        exchangeEndTime = _exchangeEndTime;
    }

    // 兑换功能
    function exchange(uint256 amount) external nonReentrant {
        require(amount > 0, "TokenExchange: amount must be greater than 0");
        require(!isExchangePeriodEnded(), "TokenExchange: exchange period ended");
        require(oldToken.balanceOf(msg.sender) >= amount, "TokenExchange: insufficient old token balance");

        uint256 newTokenAmount = getExchangeAmount(amount);
        require(newToken.balanceOf(address(this)) >= newTokenAmount, "TokenExchange: insufficient new token balance");

        // 更新统计数据
        if (!hasExchanged[msg.sender]) {
            hasExchanged[msg.sender] = true;
            totalUsers++;
        }
        userExchangeAmount[msg.sender] += amount;
        totalExchanged += amount;

        // 执行兑换：销毁旧代币，转移新代币
        oldToken.transferFrom(msg.sender, address(this), amount);
        Token(address(oldToken)).burn(amount);
        newToken.transfer(msg.sender, newTokenAmount);

        emit Exchange(msg.sender, amount, newTokenAmount, block.timestamp);
    }

    // 计算兑换数量
    function getExchangeAmount(uint256 amount) public view returns (uint256) {
        return amount.mulDiv(exchangeRate, 1e18);
    }

    // 检查兑换期是否结束
    function isExchangePeriodEnded() public view returns (bool) {
        return block.timestamp >= exchangeEndTime;
    }

    // 更新兑换率（仅在兑换期内）
    function updateExchangeRate(uint256 newRate) external onlyOwner {
        require(!isExchangePeriodEnded(), "TokenExchange: exchange period ended");
        require(newRate > 0, "TokenExchange: exchange rate must be greater than 0");

        uint256 oldRate = exchangeRate;
        exchangeRate = newRate;

        emit ExchangeRateUpdated(oldRate, newRate);
    }

    // 回收未兑换的代币（仅在兑换期结束后）
    function reclaimUnclaimed() external onlyOwner {
        require(isExchangePeriodEnded(), "TokenExchange: exchange period not ended");

        uint256 balance = newToken.balanceOf(address(this));
        require(balance > 0, "TokenExchange: no tokens to reclaim");

        newToken.transfer(owner(), balance);
        emit UnclaimedTokensReclaimed(owner(), balance);
    }

    // 紧急提取误发代币功能
    function emergencyWithdraw(address token, address to, uint256 amount) external onlyOwner nonReentrant {
        require(token != address(0), "TokenExchange: token cannot be zero address");
        require(to != address(0), "TokenExchange: withdraw to zero address");

        IERC20(token).transfer(to, amount);
        emit EmergencyWithdraw(token, to, amount);
    }

    // 获取合约状态信息
    function getContractInfo()
        external
        view
        returns (
            uint256 _totalExchanged,
            uint256 _totalUsers,
            uint256 _exchangeRate,
            uint256 _exchangeEndTime,
            bool _isExchangePeriodEnded,
            uint256 _remainingNewTokens
        )
    {
        return (
            totalExchanged,
            totalUsers,
            exchangeRate,
            exchangeEndTime,
            isExchangePeriodEnded(),
            newToken.balanceOf(address(this))
        );
    }
}
