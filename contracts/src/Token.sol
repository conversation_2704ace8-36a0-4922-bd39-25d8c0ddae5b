// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

contract Token is ERC20, ERC20Burnable, Ownable, ReentrancyGuard {
    mapping(address => bool) private _blacklisted;

    event BlacklistUpdated(address indexed account, bool isBlacklisted);

    constructor(string memory name_, string memory symbol_, uint256 initialSupply_, address owner_)
        ERC20(name_, symbol_)
        Ownable(owner_)
    {
        require(owner_ != address(0), "Token: owner cannot be zero address");
        require(initialSupply_ > 0, "Token: initial supply must be greater than 0");

        _mint(owner_, initialSupply_);
    }

    function addToBlacklist(address account) external onlyOwner {
        require(account != address(0), "Token: cannot blacklist zero address");
        require(!_blacklisted[account], "Token: account already blacklisted");

        _blacklisted[account] = true;
        emit BlacklistUpdated(account, true);
    }

    function removeFromBlacklist(address account) external onlyOwner {
        require(_blacklisted[account], "Token: account not blacklisted");

        _blacklisted[account] = false;
        emit BlacklistUpdated(account, false);
    }

    function isBlacklisted(address account) external view returns (bool) {
        return _blacklisted[account];
    }

    function burn(uint256 amount) public override {
        super.burn(amount);
    }

    function _update(address from, address to, uint256 value) internal override {
        require(!_blacklisted[from], "Token: sender is blacklisted");
        require(!_blacklisted[to], "Token: recipient is blacklisted");

        super._update(from, to, value);
    }

    function emergencyWithdraw(address token, address to, uint256 amount) external onlyOwner nonReentrant {
        require(to != address(0), "Token: withdraw to zero address");
        require(token != address(0), "Token: token cannot be zero address");

        IERC20(token).transfer(to, amount);
    }
}
