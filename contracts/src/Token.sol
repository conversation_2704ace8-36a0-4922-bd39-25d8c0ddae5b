// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract Token is ERC20, ERC20Burnable, Ownable, ReentrancyGuard {
    mapping(address => bool) private _blacklisted;

    event BlacklistAdded(address indexed account);
    event BlacklistRemoved(address indexed account);
    event EmergencyWithdraw(
        address indexed token,
        address indexed to,
        uint256 amount
    );

    constructor(
        string memory name,
        string memory symbol,
        uint256 initialSupply,
        address owner
    ) ERC20(name, symbol) Ownable(owner) {
        require(
            initialSupply > 0,
            "Token: initial supply must be greater than 0"
        );
        _mint(owner, initialSupply);
    }

    function addToBlacklist(address account) external onlyOwner {
        require(account != address(0), "Token: cannot blacklist zero address");
        require(!_blacklisted[account], "Token: account already blacklisted");

        _blacklisted[account] = true;
        emit BlacklistAdded(account);
    }

    function removeFromBlacklist(address account) external onlyOwner {
        require(_blacklisted[account], "Token: account not blacklisted");

        _blacklisted[account] = false;
        emit BlacklistRemoved(account);
    }

    function isBlacklisted(address account) external view returns (bool) {
        return _blacklisted[account];
    }

    function _update(
        address from,
        address to,
        uint256 value
    ) internal override {
        require(!_blacklisted[to], "Token: recipient is blacklisted");
        super._update(from, to, value);
    }

    function emergencyWithdraw(
        address token,
        address to,
        uint256 amount
    ) external onlyOwner nonReentrant {
        require(token != address(0), "Token: token cannot be zero address");
        require(to != address(0), "Token: withdraw to zero address");

        IERC20(token).transfer(to, amount);
        emit EmergencyWithdraw(token, to, amount);
    }
}
