// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@forge-std/Script.sol";
import "../src/Token.sol";
import "../src/TokenExchange.sol";

contract DeployMultiNetwork is Script {
    // 网络配置结构
    struct NetworkConfig {
        uint256 chainId;
        string name;
        string tokenAName;
        string tokenASymbol;
        string tokenBName;
        string tokenBSymbol;
        uint256 exchangeRate;
        uint256 exchangeDuration;
    }

    // 部署结果结构
    struct DeploymentResult {
        address tokenA;
        address tokenB;
        address exchange;
        uint256 chainId;
        string networkName;
    }

    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);

        console.log("Deploying with account:", deployer);
        console.log("Account balance:", deployer.balance);
        console.log("Chain ID:", block.chainid);

        NetworkConfig memory config = getNetworkConfig(block.chainid);

        console.log("Deploying on network:", config.name);
        console.log("Chain ID:", config.chainId);

        vm.startBroadcast(deployerPrivateKey);

        DeploymentResult memory result = deployContracts(config, deployer);

        vm.stopBroadcast();

        logDeploymentResult(result);
    }

    function getNetworkConfig(uint256 chainId) internal pure returns (NetworkConfig memory) {
        if (chainId == 1) {
            // Ethereum Mainnet
            return NetworkConfig({
                chainId: 1,
                name: "Ethereum Mainnet",
                tokenAName: "Bitroot Token",
                tokenASymbol: "BRT",
                tokenBName: "Bitroot Token One",
                tokenBSymbol: "BRT1",
                exchangeRate: 2000, // 1 BRT = 2000 BRT1
                exchangeDuration: 60 * 24 * 60 * 60 // 60 days
            });
        } else if (chainId == 42161) {
            // Arbitrum One
            return NetworkConfig({
                chainId: 42161,
                name: "Arbitrum One",
                tokenAName: "Bitroot Token",
                tokenASymbol: "BRT",
                tokenBName: "Bitroot Token One",
                tokenBSymbol: "BRT1",
                exchangeRate: 2000,
                exchangeDuration: 60 * 24 * 60 * 60
            });
        } else if (chainId == 421614) {
            // Arbitrum Goerli
            return NetworkConfig({
                chainId: 421614,
                name: "Arbitrum Goerli",
                tokenAName: "Bitroot Token",
                tokenASymbol: "BRT",
                tokenBName: "Bitroot Token One",
                tokenBSymbol: "BRT1",
                exchangeRate: 2000,
                exchangeDuration: 60 * 24 * 60 * 60
            });
        } else if (chainId == 1337) {
            // Bitroot Test Network
            return NetworkConfig({
                chainId: 1337,
                name: "Bitroot Test Network",
                tokenAName: "Bitroot Token",
                tokenASymbol: "BRT",
                tokenBName: "Bitroot Token One",
                tokenBSymbol: "BRT1",
                exchangeRate: 2000,
                exchangeDuration: 60 * 24 * 60 * 60
            });
        } else if (chainId == 11155111) {
            // Sepolia
            return NetworkConfig({
                chainId: 11155111,
                name: "Sepolia",
                tokenAName: "Bitroot Token",
                tokenASymbol: "BRT",
                tokenBName: "Bitroot Token One",
                tokenBSymbol: "BRT1",
                exchangeRate: 2000,
                exchangeDuration: 60 * 24 * 60 * 60
            });
        } else {
            // Default/Local
            return NetworkConfig({
                chainId: chainId,
                name: "Local/Unknown",
                tokenAName: "Bitroot Token",
                tokenASymbol: "BRT",
                tokenBName: "Bitroot Token One",
                tokenBSymbol: "BRT1",
                exchangeRate: 2000,
                exchangeDuration: 60 * 24 * 60 * 60
            });
        }
    }

    function deployContracts(NetworkConfig memory config, address deployer)
        internal
        returns (DeploymentResult memory)
    {
        // 根据网络调整供应量
        uint256 tokenASupply = getTokenASupply(config.chainId);
        uint256 tokenBSupply = getTokenBSupply(config.chainId);
        uint256 tokenBForExchange = getTokenBForExchange(config.chainId);

        // 1. 部署Token A
        console.log("Deploying Token A...");
        Token tokenA = new Token(config.tokenAName, config.tokenASymbol, tokenASupply, deployer);
        console.log("Token A deployed at:", address(tokenA));

        // 2. 部署Token B
        console.log("Deploying Token B...");
        Token tokenB = new Token(config.tokenBName, config.tokenBSymbol, tokenBSupply, deployer);
        console.log("Token B deployed at:", address(tokenB));

        // 3. 部署TokenExchange合约
        console.log("Deploying TokenExchange...");
        TokenExchange exchange = new TokenExchange(
            address(tokenA), address(tokenB), config.exchangeRate, block.timestamp + config.exchangeDuration, deployer
        );
        console.log("TokenExchange deployed at:", address(exchange));

        // 4. 向兑换合约转入B代币
        console.log("Transferring B tokens to exchange contract...");
        tokenB.transfer(address(exchange), tokenBForExchange);
        console.log("Transferred", tokenBForExchange / 10 ** 18, "B tokens to exchange");

        return DeploymentResult({
            tokenA: address(tokenA),
            tokenB: address(tokenB),
            exchange: address(exchange),
            chainId: config.chainId,
            networkName: config.name
        });
    }

    function getTokenASupply(uint256 chainId) internal pure returns (uint256) {
        if (chainId == 1 || chainId == 42161) {
            // 主网使用实际供应量
            return 1000000 * 10 ** 18; // 1M tokens
        } else {
            // 测试网使用较小供应量
            return 100000 * 10 ** 18; // 100K tokens
        }
    }

    function getTokenBSupply(uint256 chainId) internal pure returns (uint256) {
        if (chainId == 1 || chainId == 42161) {
            // 主网使用实际供应量
            return 2000000000 * 10 ** 18; // 2B tokens
        } else {
            // 测试网使用较小供应量
            return 200000000 * 10 ** 18; // 200M tokens
        }
    }

    function getTokenBForExchange(uint256 chainId) internal pure returns (uint256) {
        if (chainId == 1 || chainId == 42161) {
            // 主网
            return 600000000 * 10 ** 18; // 600M tokens
        } else {
            // 测试网
            return 60000000 * 10 ** 18; // 60M tokens
        }
    }

    function logDeploymentResult(DeploymentResult memory result) internal view {
        console.log("\n=== Deployment Summary ===");
        console.log("Network:", result.networkName);
        console.log("Chain ID:", result.chainId);
        console.log("Token A:", result.tokenA);
        console.log("Token B:", result.tokenB);
        console.log("TokenExchange:", result.exchange);

        console.log("\n=== Next Steps ===");
        console.log("1. Update frontend config with these addresses");
        console.log("2. Verify contracts on block explorer");
        console.log("3. Test exchange functionality");
    }
}
