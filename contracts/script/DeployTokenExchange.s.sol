// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@forge-std/Script.sol";
import "../src/Token.sol";
import "../src/TokenExchange.sol";

contract DeployTokenExchange is Script {
    // 部署参数配置
    struct DeployConfig {
        string tokenAName;
        string tokenASymbol;
        uint256 tokenASupply;
        string tokenBName;
        string tokenBSymbol;
        uint256 tokenBSupply;
        uint256 exchangeRate;
        uint256 exchangeDuration;
        uint256 tokenBForExchange;
    }

    function run() external {
        // 读取私钥
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);

        console.log("Deploying with account:", deployer);
        console.log("Account balance:", deployer.balance);

        // 部署配置
        DeployConfig memory config = DeployConfig({
            tokenAName: "TokenA",
            tokenASymbol: "TKA",
            tokenASupply: 1000000 * 10 ** 18, // 1M tokens
            tokenBName: "TokenB",
            tokenBSymbol: "TKB",
            tokenBSupply: ********** * 10 ** 18, // 1B tokens
            exchangeRate: 1000, // 1 A = 1000 B
            exchangeDuration: 60 * 24 * 60 * 60, // 60 days
            tokenBForExchange: ********* * 10 ** 18 // 300M tokens for exchange
        });

        vm.startBroadcast(deployerPrivateKey);

        // 1. 部署Token A
        console.log("Deploying Token A...");
        Token tokenA = new Token(config.tokenAName, config.tokenASymbol, config.tokenASupply, deployer);
        console.log("Token A deployed at:", address(tokenA));

        // 2. 部署Token B
        console.log("Deploying Token B...");
        Token tokenB = new Token(config.tokenBName, config.tokenBSymbol, config.tokenBSupply, deployer);
        console.log("Token B deployed at:", address(tokenB));

        // 3. 部署TokenExchange合约
        console.log("Deploying TokenExchange...");
        TokenExchange exchange =
            new TokenExchange(address(tokenA), address(tokenB), config.exchangeRate, block.timestamp + config.exchangeDuration, deployer);
        console.log("TokenExchange deployed at:", address(exchange));

        // 4. 向兑换合约转入B代币
        console.log("Transferring B tokens to exchange contract...");
        tokenB.transfer(address(exchange), config.tokenBForExchange);
        console.log("Transferred", config.tokenBForExchange / 10 ** 18, "B tokens to exchange");

        vm.stopBroadcast();

        // 输出部署信息
        console.log("\n=== Deployment Summary ===");
        console.log("Deployer:", deployer);
        console.log("Token A:", address(tokenA));
        console.log("Token B:", address(tokenB));
        console.log("TokenExchange:", address(exchange));

        console.log("\n=== Configuration ===");
        console.log("Exchange Rate:", config.exchangeRate, "(1 A = 1000 B)");
        console.log("Exchange Duration:", config.exchangeDuration / (24 * 60 * 60), "days");
        console.log("B tokens in exchange:", config.tokenBForExchange / 10 ** 18);

        console.log("\n=== Verification Commands ===");
        console.log("Token A verification:");
        console.log("forge verify-contract", address(tokenA), "src/Token.sol:Token --watch");

        console.log("Token B verification:");
        console.log("forge verify-contract", address(tokenB), "src/Token.sol:Token --watch");

        console.log("TokenExchange verification:");
        console.log("forge verify-contract", address(exchange), "src/TokenExchange.sol:TokenExchange --watch");
    }

    // 本地测试部署函数
    function deployLocal() external returns (address tokenA, address tokenB, address exchange) {
        address deployer = address(this);

        DeployConfig memory config = DeployConfig({
            tokenAName: "TokenA",
            tokenASymbol: "TKA",
            tokenASupply: 1000000 * 10 ** 18,
            tokenBName: "TokenB",
            tokenBSymbol: "TKB",
            tokenBSupply: ********** * 10 ** 18,
            exchangeRate: 1000,
            exchangeDuration: 60 * 24 * 60 * 60,
            tokenBForExchange: ********* * 10 ** 18
        });

        // 部署代币
        Token _tokenA = new Token(config.tokenAName, config.tokenASymbol, config.tokenASupply, deployer);
        Token _tokenB = new Token(config.tokenBName, config.tokenBSymbol, config.tokenBSupply, deployer);

        // 部署兑换合约
        TokenExchange _exchange = new TokenExchange(
            address(_tokenA),
            address(_tokenB),
            config.exchangeRate,
            block.timestamp + config.exchangeDuration,
            deployer
        );

        // 向兑换合约转入B代币
        _tokenB.transfer(address(_exchange), config.tokenBForExchange);

        return (address(_tokenA), address(_tokenB), address(_exchange));
    }
}
