// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@forge-std/Script.sol";
import "../src/Token.sol";
import "../src/TokenExchange.sol";

contract DeployDemo is Script {
    function run() external {
        // Use the provided private key from command line
        uint256 deployerPrivateKey =
            vm.envOr("PRIVATE_KEY", uint256(0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80));
        address deployer = vm.addr(deployerPrivateKey);

        console.log("=== Demo Deployment ===");
        console.log("Deployer account:", deployer);
        console.log("Chain ID:", block.chainid);

        vm.startBroadcast(deployerPrivateKey);

        // 1. Deploy Token A (Old Token)
        console.log("\n1. Deploying Token A (Old Token)...");
        Token tokenA = new Token("Bitroot Token Old", "BRT_OLD", 1000000 * 10 ** 18, deployer);
        console.log("Token A deployed at:", address(tokenA));

        // 2. Deploy Token B (New Token)
        console.log("\n2. Deploying Token B (New Token)...");
        Token tokenB = new Token("Bitroot Token New", "BRT_NEW", ********** * 10 ** 18, deployer);
        console.log("Token B deployed at:", address(tokenB));

        // 3. Deploy Exchange Contract
        console.log("\n3. Deploying Exchange Contract...");
        TokenExchange exchange = new TokenExchange(
            address(tokenA),
            address(tokenB),
            1000 * 10 ** 18, // 1 OLD = 1000 NEW
            block.timestamp + 60 * 24 * 60 * 60, // 60 days
            deployer
        );
        console.log("Exchange contract deployed at:", address(exchange));

        // 4. Transfer Token B to Exchange Contract
        console.log("\n4. Transferring Token B to Exchange Contract...");
        tokenB.transfer(address(exchange), 300000000 * 10 ** 18); // 300M tokens
        console.log("Transfer completed");

        vm.stopBroadcast();

        // Output deployment summary
        console.log("\n=== Deployment Summary ===");
        console.log("Deployer:", deployer);
        console.log("Token A (Old):", address(tokenA));
        console.log("Token B (New):", address(tokenB));
        console.log("Exchange Contract:", address(exchange));

        console.log("\n=== Verification ===");
        console.log("Token A balance of deployer:", tokenA.balanceOf(deployer) / 10 ** 18);
        console.log("Token B balance of deployer:", tokenB.balanceOf(deployer) / 10 ** 18);
        console.log("Token B balance in exchange:", tokenB.balanceOf(address(exchange)) / 10 ** 18);

        console.log("\n=== Exchange Configuration ===");
        console.log("Exchange Rate: 1 OLD = 1000 NEW");
        console.log("Exchange Duration: 60 days");
        console.log("Exchange End Time:", exchange.exchangeEndTime());

        console.log("\n=== Deployment Successful! ===");
    }
}
