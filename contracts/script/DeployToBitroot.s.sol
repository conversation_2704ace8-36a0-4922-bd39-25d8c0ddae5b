// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@forge-std/Script.sol";
import "../src/Token.sol";
import "../src/TokenExchange.sol";

contract DeployToBitroot is Script {
    function run() external {
        // Read private key
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");
        address deployer = vm.addr(deployerPrivateKey);

        console.log("=== Bitroot Test Network Deployment ===");
        console.log("Deployer account:", deployer);
        console.log("Account balance:", deployer.balance);
        console.log("Chain ID:", block.chainid);

        vm.startBroadcast(deployerPrivateKey);

        // 1. Deploy Token A (Old Token)
        console.log("\n1. Deploying Token A (Old Token)...");
        Token tokenA = new Token("Bitroot Token Old", "BRT_OLD", 1000000 * 10 ** 18, deployer);
        console.log("Token A deployed at:", address(tokenA));

        // 2. Deploy Token B (New Token)
        console.log("\n2. Deploying Token B (New Token)...");
        Token tokenB = new Token("Bitroot Token New", "BRT_NEW", ********** * 10 ** 18, deployer);
        console.log("Token B deployed at:", address(tokenB));

        // 3. Deploy Exchange Contract
        console.log("\n3. Deploying Exchange Contract...");
        TokenExchange exchange = new TokenExchange(
            address(tokenA),
            address(tokenB),
            1000 * 10 ** 18, // 1 OLD = 1000 NEW
            block.timestamp + 60 * 24 * 60 * 60, // 60 days
            deployer
        );
        console.log("Exchange contract deployed at:", address(exchange));

        // 4. Transfer Token B to Exchange Contract
        console.log("\n4. Transferring Token B to Exchange Contract...");
        tokenB.transfer(address(exchange), 300000000 * 10 ** 18); // 300M tokens
        console.log("Transfer completed");

        vm.stopBroadcast();

        // Output deployment summary
        console.log("\n=== Deployment Summary ===");
        console.log("Network: Bitroot Test Network");
        console.log("Deployer:", deployer);
        console.log("Token A (Old):", address(tokenA));
        console.log("Token B (New):", address(tokenB));
        console.log("Exchange Contract:", address(exchange));

        console.log("\n=== Configuration ===");
        console.log("Exchange Rate: 1 OLD = 1000 NEW");
        console.log("Exchange Duration: 60 days");
        console.log("Token B available for exchange: 300M");

        console.log("\n=== Next Steps ===");
        console.log("1. Update frontend config with contract addresses");
        console.log("2. Test exchange functionality");
        console.log("3. Add addresses to blacklist if needed");
    }
}
