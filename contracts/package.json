{"name": "@bitroot/contracts", "version": "1.0.0", "description": "Bitroot Token Exchange Smart Contracts", "main": "index.js", "scripts": {"build": "hardhat compile", "test": "hardhat test", "test:coverage": "hardhat coverage", "deploy:local": "hardhat run scripts/deploy.ts --network localhost", "deploy:bitroot": "hardhat run scripts/deploy.ts --network bitroot", "verify": "hardhat verify", "clean": "hardhat clean", "lint": "solhint 'src/**/*.sol'", "lint:fix": "solhint 'src/**/*.sol' --fix", "typecheck": "tsc --noEmit"}, "keywords": ["ethereum", "smart-contracts", "hardhat", "solidity", "token-exchange"], "author": "Bitroot Team", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.0", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "@typechain/ethers-v6": "^0.5.0", "@typechain/hardhat": "^9.0.0", "@types/chai": "^4.2.0", "@types/mocha": ">=9.1.0", "@types/node": ">=18.0.0", "chai": "^4.2.0", "ethers": "^6.4.0", "hardhat": "^2.22.0", "hardhat-gas-reporter": "^1.0.8", "solhint": "^5.0.0", "solidity-coverage": "^0.8.1", "ts-node": ">=8.0.0", "typechain": "^8.3.0", "typescript": ">=4.5.0"}, "dependencies": {"@openzeppelin/contracts": "^5.3.0"}}