#!/bin/bash

# 多网络部署脚本
# 使用方法: ./deploy.sh [network] [verify]
# 示例: ./deploy.sh bitroot_test true

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境变量
check_env() {
    if [ ! -f .env ]; then
        print_error ".env file not found. Please copy env.example to .env and configure it."
        exit 1
    fi
    
    source .env
    
    if [ -z "$PRIVATE_KEY" ]; then
        print_error "PRIVATE_KEY not set in .env file"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Usage: $0 [network] [verify]"
    echo ""
    echo "Available networks:"
    echo "  mainnet         - Ethereum Mainnet"
    echo "  arbitrum        - Arbitrum One"
    echo "  arbitrum_goerli - Arbitrum Goerli Testnet"
    echo "  sepolia         - Sepolia Testnet"
    echo "  bitroot_test    - Bitroot Test Network"
    echo "  local           - Local Anvil"
    echo ""
    echo "Options:"
    echo "  verify          - Set to 'true' to verify contracts after deployment"
    echo ""
    echo "Examples:"
    echo "  $0 bitroot_test true    - Deploy to Bitroot test network and verify"
    echo "  $0 arbitrum false       - Deploy to Arbitrum without verification"
    echo "  $0 local                - Deploy to local network"
}

# 获取网络配置
get_network_config() {
    case $1 in
        mainnet)
            NETWORK_NAME="mainnet"
            CHAIN_ID=1
            EXPLORER_URL="https://etherscan.io"
            ;;
        arbitrum)
            NETWORK_NAME="arbitrum"
            CHAIN_ID=42161
            EXPLORER_URL="https://arbiscan.io"
            ;;
        arbitrum_goerli)
            NETWORK_NAME="arbitrum_goerli"
            CHAIN_ID=421614
            EXPLORER_URL="https://goerli.arbiscan.io"
            ;;
        sepolia)
            NETWORK_NAME="sepolia"
            CHAIN_ID=11155111
            EXPLORER_URL="https://sepolia.etherscan.io"
            ;;
        bitroot_test)
            NETWORK_NAME="bitroot_test"
            CHAIN_ID=1337
            EXPLORER_URL="https://explorer.bitroot.co"
            ;;
        local)
            NETWORK_NAME="local"
            CHAIN_ID=31337
            EXPLORER_URL="http://localhost:8545"
            ;;
        *)
            print_error "Unknown network: $1"
            show_help
            exit 1
            ;;
    esac
}

# 部署合约
deploy_contracts() {
    local network=$1
    local verify=${2:-false}
    
    print_info "Starting deployment to $network..."
    
    # 构建项目
    print_info "Building contracts..."
    forge build
    
    if [ $? -ne 0 ]; then
        print_error "Build failed"
        exit 1
    fi
    
    # 部署合约
    print_info "Deploying contracts to $network..."
    
    if [ "$network" = "local" ]; then
        # 本地部署不需要指定 RPC URL
        forge script script/DeployMultiNetwork.s.sol:DeployMultiNetwork \
            --broadcast \
            --legacy
    else
        # 远程网络部署
        forge script script/DeployMultiNetwork.s.sol:DeployMultiNetwork \
            --rpc-url $network \
            --broadcast \
            --legacy
    fi
    
    if [ $? -ne 0 ]; then
        print_error "Deployment failed"
        exit 1
    fi
    
    print_success "Deployment completed successfully!"
    
    # 验证合约
    if [ "$verify" = "true" ] && [ "$network" != "local" ] && [ "$network" != "bitroot_test" ]; then
        print_info "Contract verification will be done manually due to custom verification requirements"
        print_info "Please check the deployment output for verification commands"
    fi
}

# 保存部署信息
save_deployment_info() {
    local network=$1
    local chain_id=$2
    
    # 创建部署信息目录
    mkdir -p deployments/$network
    
    # 从广播文件中提取部署地址
    BROADCAST_FILE="broadcast/DeployMultiNetwork.s.sol/$chain_id/run-latest.json"
    
    if [ -f "$BROADCAST_FILE" ]; then
        print_info "Saving deployment information..."
        
        # 提取合约地址并保存到配置文件
        cat > deployments/$network/addresses.json << EOF
{
  "chainId": $chain_id,
  "network": "$network",
  "contracts": {
    "tokenA": "$(jq -r '.transactions[] | select(.contractName == "Token") | .contractAddress' $BROADCAST_FILE | head -1)",
    "tokenB": "$(jq -r '.transactions[] | select(.contractName == "Token") | .contractAddress' $BROADCAST_FILE | tail -1)",
    "tokenExchange": "$(jq -r '.transactions[] | select(.contractName == "TokenExchange") | .contractAddress' $BROADCAST_FILE)"
  },
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF
        
        print_success "Deployment info saved to deployments/$network/addresses.json"
    else
        print_warning "Broadcast file not found, skipping deployment info save"
    fi
}

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    local network=$1
    local verify=${2:-false}
    
    # 检查环境
    check_env
    
    # 获取网络配置
    get_network_config $network
    
    print_info "Deployment Configuration:"
    print_info "Network: $NETWORK_NAME"
    print_info "Chain ID: $CHAIN_ID"
    print_info "Explorer: $EXPLORER_URL"
    print_info "Verify: $verify"
    print_info ""
    
    # 确认部署
    read -p "Continue with deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Deployment cancelled"
        exit 0
    fi
    
    # 执行部署
    deploy_contracts $network $verify
    
    # 保存部署信息
    save_deployment_info $NETWORK_NAME $CHAIN_ID
    
    print_success "All done! 🎉"
}

# 运行主函数
main "$@"
