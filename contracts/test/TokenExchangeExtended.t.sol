// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@forge-std/Test.sol";
import "../src/TokenExchange.sol";
import "../src/Token.sol";

contract TokenExchangeExtendedTest is Test {
    TokenExchange public exchange;
    Token public oldToken;
    Token public newToken;

    address public owner;
    address public user1;
    address public user2;

    uint256 constant INITIAL_SUPPLY = 1_000_000 * 1e18;
    uint256 constant EXCHANGE_RATE = 1000 * 1e18; // 1 old = 1000 new, with 1e18 precision
    uint256 constant EXCHANGE_DURATION = 60 days;
    uint256 public exchangeEndTime;
    uint256 constant NEW_TOKEN_FOR_EXCHANGE = 300_000 * 1e18;

    event Exchange(address indexed user, uint256 tokenAAmount, uint256 tokenBAmount, uint256 timestamp);
    event UnclaimedTokensReclaimed(address indexed owner, uint256 amount);
    event ExchangeRateUpdated(uint256 oldRate, uint256 newRate);

    function setUp() public {
        owner = makeAddr("owner");
        user1 = makeAddr("user1");
        user2 = makeAddr("user2");

        exchangeEndTime = block.timestamp + EXCHANGE_DURATION;

        vm.startPrank(owner);

        oldToken = new Token("Old Token", "OLD", INITIAL_SUPPLY, owner);
        newToken = new Token("New Token", "NEW", INITIAL_SUPPLY, owner);

        exchange = new TokenExchange(address(oldToken), address(newToken), EXCHANGE_RATE, exchangeEndTime, owner);

        newToken.transfer(address(exchange), NEW_TOKEN_FOR_EXCHANGE);

        oldToken.transfer(user1, 10_000 * 1e18);
        oldToken.transfer(user2, 20_000 * 1e18);

        vm.stopPrank();
    }

    // --- Advanced Exchange Scenarios ---
    function test_succeeds_multipleUserExchange() public {
        uint256 amount1 = 100 * 1e18;
        uint256 amount2 = 200 * 1e18;

        uint256 newAmount1 = exchange.getExchangeAmount(amount1);
        uint256 newAmount2 = exchange.getExchangeAmount(amount2);

        // User 1 exchanges
        vm.startPrank(user1);
        oldToken.approve(address(exchange), amount1);
        exchange.exchange(amount1);
        vm.stopPrank();

        // User 2 exchanges
        vm.startPrank(user2);
        oldToken.approve(address(exchange), amount2);
        exchange.exchange(amount2);
        vm.stopPrank();

        assertEq(newToken.balanceOf(user1), newAmount1);
        assertEq(newToken.balanceOf(user2), newAmount2);
    }

    // --- Edge Case Tests ---
    function test_getExchangeAmount_zero() public view {
        assertEq(exchange.getExchangeAmount(0), 0);
    }

    function test_getExchangeAmount_large() public view {
        uint256 largeAmount = type(uint256).max / (1e18); // A large number that won't overflow mulDiv's intermediate multiplication
        uint256 expected = exchange.getExchangeAmount(largeAmount);
        // We can't easily assert the exact value due to potential precision loss with such large numbers,
        // but we can ensure it executed without reverting. A non-zero result is a good sign.
        assertTrue(expected > 0);
    }

    function test_exchangePeriodEnded_byTime() public {
        assertFalse(exchange.isExchangePeriodEnded());
        vm.warp(exchangeEndTime - 1);
        assertFalse(exchange.isExchangePeriodEnded());
        vm.warp(exchangeEndTime);
        assertTrue(exchange.isExchangePeriodEnded());
        vm.warp(exchangeEndTime + 1);
        assertTrue(exchange.isExchangePeriodEnded());
    }

    // --- Reclaim Functionality Tests ---
    function test_reclaimUnclaimed_afterExchange() public {
        uint256 exchangeAmount = 100 * 1e18;

        vm.startPrank(user1);
        oldToken.approve(address(exchange), exchangeAmount);
        exchange.exchange(exchangeAmount);
        vm.stopPrank();

        vm.warp(exchangeEndTime + 1);

        uint256 remainingBalance = newToken.balanceOf(address(exchange));
        uint256 ownerBalanceBefore = newToken.balanceOf(owner);

        vm.prank(owner);
        exchange.reclaimUnclaimed();

        assertEq(newToken.balanceOf(address(exchange)), 0);
        assertEq(newToken.balanceOf(owner), ownerBalanceBefore + remainingBalance);
    }

    // --- Emergency Withdraw Edge Cases ---
    function test_reverts_emergencyWithdraw_toZeroAddress() public {
        Token otherToken = new Token("Other", "OT", 1000 * 1e18, owner);
        vm.prank(owner);
        vm.expectRevert("TokenExchange: withdraw to zero address");
        exchange.emergencyWithdraw(address(otherToken), address(0), 100);
    }

    function test_reverts_emergencyWithdraw_withZeroTokenAddress() public {
        vm.prank(owner);
        vm.expectRevert("TokenExchange: token cannot be zero address");
        exchange.emergencyWithdraw(address(0), owner, 100);
    }

    // --- Fuzz Tests ---
    function testFuzz_exchange(uint256 amount) public {
        vm.assume(amount > 0 && amount <= 5000 * 1e18); // Limit within user's balance
        uint256 newAmount = exchange.getExchangeAmount(amount);
        vm.assume(newAmount <= newToken.balanceOf(address(exchange))); // Ensure enough new tokens

        vm.startPrank(user1);
        uint256 oldBalanceBefore = oldToken.balanceOf(user1);
        uint256 newBalanceBefore = newToken.balanceOf(user1);

        oldToken.approve(address(exchange), amount);
        exchange.exchange(amount);

        assertEq(oldToken.balanceOf(user1), oldBalanceBefore - amount);
        assertEq(newToken.balanceOf(user1), newBalanceBefore + newAmount);
        vm.stopPrank();
    }

    function testFuzz_getExchangeAmount(uint256 amount) public view {
        // Prevent overflow in mulDiv: amount * rate <= type(uint256).max
        vm.assume(amount <= type(uint256).max / EXCHANGE_RATE);

        uint256 result = exchange.getExchangeAmount(amount);
        assertEq(result, (amount * EXCHANGE_RATE) / 1e18);
    }
}
