// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import {Test, console} from "forge-std/Test.sol";
import {Token} from "../src/Token.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";

contract TokenTest is Test {
    Token internal tokenA;
    address internal owner = address(0x1);
    address internal user1 = address(0x2);
    address internal user2 = address(0x3);
    uint256 internal initialSupply = 1_000_000 * 10 ** 18;

    function setUp() public {
        vm.prank(owner);
        tokenA = new Token("TokenA", "TKA", initialSupply, owner);
    }

    // --- Constructor Tests ---

    function testConstructor() public {
        assertEq(tokenA.owner(), owner, "Owner should be set correctly");
        assertEq(tokenA.balanceOf(owner), initialSupply, "Initial supply should be minted to owner");
        assertEq(tokenA.name(), "TokenA", "Name should be set correctly");
        assertEq(tokenA.symbol(), "TKA", "Symbol should be set correctly");
    }

    function testConstructorWithZeroOwner() public {
        vm.expectRevert(abi.encodeWithSelector(Ownable.OwnableInvalidOwner.selector, address(0)));
        new Token("TokenA", "TKA", initialSupply, address(0));
    }

    function testConstructorWithZeroSupply() public {
        vm.expectRevert("Token: initial supply must be greater than 0");
        new Token("TokenA", "TKA", 0, owner);
    }

    // --- Blacklist Tests ---

    function testAddToBlacklist() public {
        vm.prank(owner);
        tokenA.addToBlacklist(user1);
        assertTrue(tokenA.isBlacklisted(user1), "User1 should be blacklisted");
    }

    function testAddToBlacklistNonOwner() public {
        vm.prank(user1);
        vm.expectRevert(abi.encodeWithSelector(Ownable.OwnableUnauthorizedAccount.selector, user1));
        tokenA.addToBlacklist(user2);
    }

    function testAddToBlacklistZeroAddress() public {
        vm.prank(owner);
        vm.expectRevert("Token: cannot blacklist zero address");
        tokenA.addToBlacklist(address(0));
    }

    function testAddToBlacklistAlreadyBlacklisted() public {
        vm.prank(owner);
        tokenA.addToBlacklist(user1);
        vm.prank(owner);
        vm.expectRevert("Token: account already blacklisted");
        tokenA.addToBlacklist(user1);
    }

    function testRemoveFromBlacklist() public {
        vm.prank(owner);
        tokenA.addToBlacklist(user1);
        vm.prank(owner);
        tokenA.removeFromBlacklist(user1);
        assertFalse(tokenA.isBlacklisted(user1), "User1 should not be blacklisted");
    }

    function testRemoveFromBlacklistNotBlacklisted() public {
        vm.prank(owner);
        vm.expectRevert("Token: account not blacklisted");
        tokenA.removeFromBlacklist(user1);
    }

    // --- Transfer Tests ---

    function testTransfer() public {
        vm.prank(owner);
        tokenA.transfer(user1, 100);
        assertEq(tokenA.balanceOf(user1), 100, "User1 balance should be 100");
    }

    function testTransferFrom() public {
        vm.prank(owner);
        tokenA.approve(user1, 100);
        vm.prank(user1);
        tokenA.transferFrom(owner, user2, 100);
        assertEq(tokenA.balanceOf(user2), 100, "User2 balance should be 100");
    }

    function testTransferToBlacklistedRecipient() public {
        vm.prank(owner);
        tokenA.addToBlacklist(user1);
        vm.expectRevert("Token: recipient is blacklisted");
        tokenA.transfer(user1, 100);
    }

    function testTransferFromBlacklistedSender() public {
        vm.prank(owner);
        tokenA.addToBlacklist(user1);
        vm.prank(user1);
        vm.expectRevert("Token: sender is blacklisted");
        tokenA.transfer(user2, 0); // Amount doesn't matter, it's the sender
    }

    // --- Burn Tests ---

    function testBurn() public {
        vm.prank(owner);
        tokenA.burn(100);
        assertEq(tokenA.balanceOf(owner), initialSupply - 100, "Owner balance should be reduced");
    }

    function testFuzzBurn(uint256 amount) public {
        amount = bound(amount, 1, initialSupply);
        vm.prank(owner);
        tokenA.burn(amount);
        assertEq(tokenA.balanceOf(owner), initialSupply - amount);
    }

    // --- Emergency Withdraw Tests ---

    function testEmergencyWithdrawToken() public {
        Token otherToken = new Token("OtherToken", "OTK", 1000, owner);
        vm.prank(owner);
        otherToken.transfer(address(tokenA), 500);

        assertEq(otherToken.balanceOf(address(tokenA)), 500);

        vm.prank(owner);
        tokenA.emergencyWithdraw(address(otherToken), user1, 500);

        assertEq(otherToken.balanceOf(address(tokenA)), 0);
        assertEq(otherToken.balanceOf(user1), 500);
    }

    function testEmergencyWithdrawNonOwner() public {
        vm.prank(user1);
        vm.expectRevert(abi.encodeWithSelector(Ownable.OwnableUnauthorizedAccount.selector, user1));
        tokenA.emergencyWithdraw(address(this), user2, 0);
    }

    function testEmergencyWithdrawToZeroAddress() public {
        vm.prank(owner);
        vm.expectRevert("Token: withdraw to zero address");
        tokenA.emergencyWithdraw(address(this), address(0), 0);
    }

    function testEmergencyWithdrawZeroTokenAddress() public {
        vm.prank(owner);
        vm.expectRevert("Token: token cannot be zero address");
        tokenA.emergencyWithdraw(address(0), user1, 0);
    }
}
