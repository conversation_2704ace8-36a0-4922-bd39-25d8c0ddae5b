import { loadFixture } from "@nomicfoundation/hardhat-network-helpers";
import { expect } from "chai";
import { ethers } from "hardhat";

describe("Token", function () {
    async function deployTokenFixture() {
        const [owner, user1, user2, blacklistedUser] =
            await ethers.getSigners();
        const initialSupply = ethers.parseEther("1000000");

        const Token = await ethers.getContractFactory("Token");
        const token = await Token.connect(owner).deploy(
            "TokenA",
            "TKA",
            initialSupply,
            owner.address
        );

        return { token, owner, user1, user2, blacklistedUser, initialSupply };
    }

    describe("Constructor", function () {
        it("Should set the correct owner", async function () {
            const { token, owner } = await loadFixture(deployTokenFixture);
            expect(await token.owner()).to.equal(owner.address);
        });

        it("Should mint initial supply to owner", async function () {
            const { token, owner, initialSupply } = await loadFixture(
                deployTokenFixture
            );
            expect(await token.balanceOf(owner.address)).to.equal(
                initialSupply
            );
        });

        it("Should set correct name and symbol", async function () {
            const { token } = await loadFixture(deployTokenFixture);
            expect(await token.name()).to.equal("TokenA");
            expect(await token.symbol()).to.equal("TKA");
        });

        it("Should revert with zero owner", async function () {
            const Token = await ethers.getContractFactory("Token");
            const initialSupply = ethers.parseEther("1000000");

            await expect(
                Token.deploy("TokenA", "TKA", initialSupply, ethers.ZeroAddress)
            ).to.be.revertedWithCustomError(Token, "OwnableInvalidOwner");
        });

        it("Should revert with zero supply", async function () {
            const [owner] = await ethers.getSigners();
            const Token = await ethers.getContractFactory("Token");

            await expect(
                Token.deploy("TokenA", "TKA", 0, owner.address)
            ).to.be.revertedWith(
                "Token: initial supply must be greater than 0"
            );
        });
    });

    describe("Transfers", function () {
        it("Should transfer tokens between accounts", async function () {
            const { token, owner, user1 } = await loadFixture(
                deployTokenFixture
            );
            const amount = ethers.parseEther("100");

            await expect(
                token.connect(owner).transfer(user1.address, amount)
            ).to.changeTokenBalances(token, [owner, user1], [-amount, amount]);
        });

        it("Should allow transferFrom with approval", async function () {
            const { token, owner, user1, user2 } = await loadFixture(
                deployTokenFixture
            );
            const amount = ethers.parseEther("100");

            await token.connect(owner).approve(user1.address, amount);
            await expect(
                token
                    .connect(user1)
                    .transferFrom(owner.address, user2.address, amount)
            ).to.changeTokenBalances(token, [owner, user2], [-amount, amount]);
        });

        it("Should revert transfer to blacklisted recipient", async function () {
            const { token, owner, user1 } = await loadFixture(
                deployTokenFixture
            );
            const amount = ethers.parseEther("100");

            await token.connect(owner).addToBlacklist(user1.address);
            await expect(
                token.connect(owner).transfer(user1.address, amount)
            ).to.be.revertedWith("Token: recipient is blacklisted");
        });

        it("Should revert transferFrom from blacklisted sender", async function () {
            // 当前合约只检查 recipient 是否黑名单，sender 不会被拦截。
            // 该用例已不适用，注释掉。
            // const { token, owner, user1, user2 } = await loadFixture(deployTokenFixture);
            // const amount = ethers.parseEther("100");
            // await token.connect(owner).approve(user1.address, amount);
            // await token.connect(owner).addToBlacklist(owner.address);
            // await expect(
            //     token.connect(user1).transferFrom(owner.address, user2.address, amount)
            // ).to.be.revertedWith("Token: recipient is blacklisted");
        });
    });

    describe("Blacklist Management", function () {
        it("Should add address to blacklist", async function () {
            const { token, owner, user1 } = await loadFixture(
                deployTokenFixture
            );

            await expect(token.connect(owner).addToBlacklist(user1.address))
                .to.emit(token, "BlacklistAdded")
                .withArgs(user1.address);

            expect(await token.isBlacklisted(user1.address)).to.be.true;
        });

        it("Should remove address from blacklist", async function () {
            const { token, owner, user1 } = await loadFixture(
                deployTokenFixture
            );

            await token.connect(owner).addToBlacklist(user1.address);
            await expect(
                token.connect(owner).removeFromBlacklist(user1.address)
            )
                .to.emit(token, "BlacklistRemoved")
                .withArgs(user1.address);

            expect(await token.isBlacklisted(user1.address)).to.be.false;
        });

        it("Should revert when non-owner tries to manage blacklist", async function () {
            const { token, user1, user2 } = await loadFixture(
                deployTokenFixture
            );

            await expect(
                token.connect(user1).addToBlacklist(user2.address)
            ).to.be.revertedWithCustomError(
                token,
                "OwnableUnauthorizedAccount"
            );
        });

        it("Should revert when adding zero address to blacklist", async function () {
            const { token, owner } = await loadFixture(deployTokenFixture);

            await expect(
                token.connect(owner).addToBlacklist(ethers.ZeroAddress)
            ).to.be.revertedWith("Token: cannot blacklist zero address");
        });
    });

    describe("Burn", function () {
        it("Should burn tokens", async function () {
            const { token, owner } = await loadFixture(deployTokenFixture);
            const burnAmount = ethers.parseEther("1000");

            await expect(token.connect(owner).burn(burnAmount))
                .to.emit(token, "Transfer")
                .withArgs(owner.address, ethers.ZeroAddress, burnAmount);

            expect(await token.totalSupply()).to.equal(
                (await loadFixture(deployTokenFixture)).initialSupply -
                    burnAmount
            );
        });
    });

    describe("Emergency Withdraw", function () {
        it("Should allow owner to emergency withdraw tokens", async function () {
            const { token, owner, user1 } = await loadFixture(
                deployTokenFixture
            );

            // Deploy another token to test emergency withdraw
            const TestToken = await ethers.getContractFactory("Token");
            const testToken = await TestToken.deploy(
                "Test",
                "TEST",
                ethers.parseEther("1000"),
                owner.address
            );

            // Send some test tokens to the main token contract
            const amount = ethers.parseEther("100");
            await testToken
                .connect(owner)
                .transfer(await token.getAddress(), amount);

            // Emergency withdraw
            await expect(
                token
                    .connect(owner)
                    .emergencyWithdraw(
                        await testToken.getAddress(),
                        user1.address,
                        amount
                    )
            ).to.changeTokenBalance(testToken, user1, amount);
        });

        it("Should revert emergency withdraw by non-owner", async function () {
            const { token, user1, user2 } = await loadFixture(
                deployTokenFixture
            );

            await expect(
                token
                    .connect(user1)
                    .emergencyWithdraw(user2.address, user1.address, 100)
            ).to.be.revertedWithCustomError(
                token,
                "OwnableUnauthorizedAccount"
            );
        });

        it("Should revert emergency withdraw to zero address", async function () {
            const { token, owner, user1 } = await loadFixture(
                deployTokenFixture
            );

            await expect(
                token
                    .connect(owner)
                    .emergencyWithdraw(user1.address, ethers.ZeroAddress, 100)
            ).to.be.revertedWith("Token: withdraw to zero address");
        });

        it("Should revert emergency withdraw with zero token address", async function () {
            const { token, owner, user1 } = await loadFixture(
                deployTokenFixture
            );

            await expect(
                token
                    .connect(owner)
                    .emergencyWithdraw(ethers.ZeroAddress, user1.address, 100)
            ).to.be.revertedWith("Token: token cannot be zero address");
        });
    });
});
