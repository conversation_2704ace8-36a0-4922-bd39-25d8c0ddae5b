// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@forge-std/Test.sol";
import "../src/TokenExchange.sol";
import "../src/Token.sol";

contract TokenExchangeTest is Test {
    TokenExchange public exchange;
    Token public oldToken;
    Token public newToken;

    address public owner;
    address public user1;
    address public user2;

    uint256 constant INITIAL_SUPPLY = 1_000_000 * 1e18; // 1M tokens
    // 1 oldToken = 1000 newToken. Rate is expressed with 18 decimals.
    uint256 constant EXCHANGE_RATE = 1000 * 1e18;
    uint256 constant EXCHANGE_DURATION = 60 days;
    uint256 public exchangeEndTime;
    uint256 constant NEW_TOKEN_FOR_EXCHANGE = 300_000 * 1e18; // 300K tokens for exchange

    event Exchange(address indexed user, uint256 oldTokenAmount, uint256 newTokenAmount, uint256 timestamp);
    event UnclaimedTokensReclaimed(address indexed owner, uint256 amount);
    event ExchangeRateUpdated(uint256 oldRate, uint256 newRate);

    function setUp() public {
        owner = makeAddr("owner");
        user1 = makeAddr("user1");
        user2 = makeAddr("user2");

        exchangeEndTime = block.timestamp + EXCHANGE_DURATION;

        vm.startPrank(owner);

        // Create tokens
        oldToken = new Token("Old Token", "OLD", INITIAL_SUPPLY, owner);
        newToken = new Token("New Token", "NEW", INITIAL_SUPPLY, owner);

        // Create exchange contract
        exchange = new TokenExchange(address(oldToken), address(newToken), EXCHANGE_RATE, exchangeEndTime, owner);

        // Fund the exchange contract with new tokens
        newToken.transfer(address(exchange), NEW_TOKEN_FOR_EXCHANGE);

        // Distribute old tokens to users
        oldToken.transfer(user1, 10_000 * 1e18);
        oldToken.transfer(user2, 20_000 * 1e18);

        vm.stopPrank();
    }

    // --- Constructor Tests ---
    function test_succeeds_constructor() public view {
        assertEq(address(exchange.oldToken()), address(oldToken));
        assertEq(address(exchange.newToken()), address(newToken));
        assertEq(exchange.exchangeRate(), EXCHANGE_RATE);
        assertEq(exchange.exchangeEndTime(), exchangeEndTime);
        assertEq(exchange.owner(), owner);
        assertFalse(exchange.isExchangePeriodEnded());
    }

    function test_reverts_constructor_withZeroOldToken() public {
        vm.expectRevert("TokenExchange: oldToken cannot be zero address");
        new TokenExchange(address(0), address(newToken), EXCHANGE_RATE, exchangeEndTime, owner);
    }

    function test_reverts_constructor_withZeroNewToken() public {
        vm.expectRevert("TokenExchange: newToken cannot be zero address");
        new TokenExchange(address(oldToken), address(0), EXCHANGE_RATE, exchangeEndTime, owner);
    }

    function test_reverts_constructor_withSameTokens() public {
        vm.expectRevert("TokenExchange: oldToken and newToken cannot be the same");
        new TokenExchange(address(oldToken), address(oldToken), EXCHANGE_RATE, exchangeEndTime, owner);
    }

    function test_reverts_constructor_withZeroExchangeRate() public {
        vm.expectRevert("TokenExchange: exchange rate must be greater than 0");
        new TokenExchange(address(oldToken), address(newToken), 0, exchangeEndTime, owner);
    }

    function test_reverts_constructor_withPastEndTime() public {
        vm.expectRevert("TokenExchange: end time must be in the future");
        new TokenExchange(address(oldToken), address(newToken), EXCHANGE_RATE, block.timestamp - 1, owner);
    }

    function test_reverts_constructor_withZeroOwner() public {
        vm.expectRevert(abi.encodeWithSelector(Ownable.OwnableInvalidOwner.selector, address(0)));
        new TokenExchange(address(oldToken), address(newToken), EXCHANGE_RATE, exchangeEndTime, address(0));
    }

    // --- Exchange Rate Update Tests ---
    function test_succeeds_updateExchangeRate() public {
        uint256 newRate = 2000 * 1e18;
        vm.prank(owner);
        vm.expectEmit(false, false, false, true);
        emit ExchangeRateUpdated(EXCHANGE_RATE, newRate);
        exchange.updateExchangeRate(newRate);
        assertEq(exchange.exchangeRate(), newRate);
    }

    function test_reverts_updateExchangeRate_whenNotOwner() public {
        vm.prank(user1);
        vm.expectRevert(abi.encodeWithSelector(Ownable.OwnableUnauthorizedAccount.selector, user1));
        exchange.updateExchangeRate(2000 * 1e18);
    }

    function test_reverts_updateExchangeRate_withZeroRate() public {
        vm.prank(owner);
        vm.expectRevert("TokenExchange: exchange rate must be greater than 0");
        exchange.updateExchangeRate(0);
    }

    function test_reverts_updateExchangeRate_afterPeriodEnded() public {
        vm.warp(exchangeEndTime + 1);
        vm.prank(owner);
        vm.expectRevert("TokenExchange: exchange period ended");
        exchange.updateExchangeRate(2000 * 1e18);
    }

    // --- Exchange Tests ---
    function test_succeeds_exchange() public {
        uint256 exchangeAmount = 100 * 1e18;
        uint256 expectedNewAmount = exchange.getExchangeAmount(exchangeAmount);

        vm.startPrank(user1);
        oldToken.approve(address(exchange), exchangeAmount);

        uint256 userOldBalanceBefore = oldToken.balanceOf(user1);
        uint256 userNewBalanceBefore = newToken.balanceOf(user1);
        uint256 exchangeNewBalanceBefore = newToken.balanceOf(address(exchange));

        vm.expectEmit(true, false, false, true);
        emit Exchange(user1, exchangeAmount, expectedNewAmount, block.timestamp);
        exchange.exchange(exchangeAmount);

        assertEq(oldToken.balanceOf(user1), userOldBalanceBefore - exchangeAmount);
        assertEq(newToken.balanceOf(user1), userNewBalanceBefore + expectedNewAmount);
        assertEq(newToken.balanceOf(address(exchange)), exchangeNewBalanceBefore - expectedNewAmount);
        assertEq(oldToken.balanceOf(address(exchange)), 0, "Burned tokens should not remain in contract");
        vm.stopPrank();
    }

    function test_succeeds_exchangeMultipleTimes() public {
        uint256 exchangeAmount1 = 50 * 1e18;
        uint256 exchangeAmount2 = 30 * 1e18;
        uint256 totalExchangeAmount = exchangeAmount1 + exchangeAmount2;

        uint256 expectedNewAmount1 = exchange.getExchangeAmount(exchangeAmount1);
        uint256 expectedNewAmount2 = exchange.getExchangeAmount(exchangeAmount2);
        uint256 totalExpectedNewAmount = expectedNewAmount1 + expectedNewAmount2;

        vm.startPrank(user1);
        oldToken.approve(address(exchange), totalExchangeAmount);

        uint256 userOldBalanceBefore = oldToken.balanceOf(user1);
        uint256 userNewBalanceBefore = newToken.balanceOf(user1);

        // First exchange
        exchange.exchange(exchangeAmount1);

        // Second exchange
        exchange.exchange(exchangeAmount2);

        assertEq(oldToken.balanceOf(user1), userOldBalanceBefore - totalExchangeAmount);
        assertEq(newToken.balanceOf(user1), userNewBalanceBefore + totalExpectedNewAmount);
        vm.stopPrank();
    }

    function test_reverts_exchange_withZeroAmount() public {
        vm.prank(user1);
        vm.expectRevert("TokenExchange: amount must be greater than 0");
        exchange.exchange(0);
    }

    function test_reverts_exchange_afterPeriodEnded() public {
        vm.warp(exchangeEndTime + 1);
        vm.prank(user1);
        oldToken.approve(address(exchange), 100 * 1e18);
        vm.expectRevert("TokenExchange: exchange period ended");
        exchange.exchange(100 * 1e18);
    }

    function test_reverts_exchange_insufficientOldTokenBalance() public {
        uint256 exchangeAmount = 20_000 * 1e18; // more than user1 has
        vm.prank(user1);
        oldToken.approve(address(exchange), exchangeAmount);
        vm.expectRevert("TokenExchange: insufficient old token balance");
        exchange.exchange(exchangeAmount);
    }

    function test_reverts_exchange_insufficientNewTokenBalance() public {
        vm.startPrank(owner);
        TokenExchange newExchange =
            new TokenExchange(address(oldToken), address(newToken), EXCHANGE_RATE, exchangeEndTime, owner);
        // Fund with a small amount
        newToken.transfer(address(newExchange), 1000 * 1e18);
        vm.stopPrank();

        uint256 exchangeAmount = 10 * 1e18; // Needs 10_000 * 1e18, but contract only has 1_000 * 1e18

        vm.startPrank(user1);
        oldToken.approve(address(newExchange), exchangeAmount);
        vm.expectRevert("TokenExchange: insufficient new token balance");
        newExchange.exchange(exchangeAmount);
        vm.stopPrank();
    }

    // --- Reclaim Unclaimed Tokens Tests ---
    function test_succeeds_reclaimUnclaimed() public {
        vm.warp(exchangeEndTime + 1);
        uint256 remainingBalance = newToken.balanceOf(address(exchange));
        uint256 ownerBalanceBefore = newToken.balanceOf(owner);

        vm.prank(owner);
        vm.expectEmit(true, false, false, true);
        emit UnclaimedTokensReclaimed(owner, remainingBalance);
        exchange.reclaimUnclaimed();

        assertEq(newToken.balanceOf(address(exchange)), 0);
        assertEq(newToken.balanceOf(owner), ownerBalanceBefore + remainingBalance);
    }

    function test_reverts_reclaimUnclaimed_beforePeriodEnded() public {
        vm.prank(owner);
        vm.expectRevert("TokenExchange: exchange period not ended");
        exchange.reclaimUnclaimed();
    }

    function test_reverts_reclaimUnclaimed_whenNoTokens() public {
        // Setup a new exchange contract with zero new tokens
        vm.startPrank(owner);
        TokenExchange emptyExchange =
            new TokenExchange(address(oldToken), address(newToken), EXCHANGE_RATE, exchangeEndTime, owner);
        vm.stopPrank();

        vm.warp(exchangeEndTime + 1);

        vm.prank(owner);
        vm.expectRevert("TokenExchange: no tokens to reclaim");
        emptyExchange.reclaimUnclaimed();
    }
}
