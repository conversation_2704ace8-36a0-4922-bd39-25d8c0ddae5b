[profile.default]
src = "src"
out = "out"
libs = ["lib"]
solc = "0.8.20"
optimizer = true
optimizer_runs = 200
via_ir = false
fuzz = { runs = 1000 }
invariant = { runs = 1000, depth = 15 }
gas_reports = ["*"]
remappings = [
    "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/",
    "@forge-std/=lib/forge-std/src/"
]

[dependencies]
"OpenZeppelin/openzeppelin-contracts" = "v5.3.0"
"foundry-rs/forge-std" = "v1.9.7"

[rpc_endpoints]
mainnet = "${MAINNET_RPC_URL}"
goerli = "${GOERLI_RPC_URL}"
sepolia = "${SEPOLIA_RPC_URL}"
arbitrum = "${ARBITRUM_RPC_URL}"
arbitrum_goerli = "${ARBITRUM_GOERLI_RPC_URL}"

[etherscan]
mainnet = { key = "${ETHERSCAN_API_KEY}" }
goerli = { key = "${ETHERSCAN_API_KEY}" }
sepolia = { key = "${ETHERSCAN_API_KEY}" }
arbitrum = { key = "${ARBISCAN_API_KEY}" }
arbitrum_goerli = { key = "${ARBISCAN_API_KEY}" } 