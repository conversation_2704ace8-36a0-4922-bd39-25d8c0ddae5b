[profile.default]
src = "src"
test = "test"
out = "out"
libs = ["dependencies"]
solc = "0.8.20"
optimizer = true
optimizer_runs = 200
via_ir = false
fuzz = { runs = 1000 }
invariant = { runs = 1000, depth = 15 }
gas_reports = ["*"]
remappings = [
    "@openzeppelin/contracts/=dependencies/openzeppelin-contracts-5.3.0/contracts/",
    "@forge-std/=dependencies/forge-std-1.9.7/src/",
    "forge-std/=dependencies/forge-std-1.9.7/src/"
]

[dependencies]
forge-std = "1.9.7"

[rpc_endpoints]

bitroot_test = "https://test-rpc.bitroot.co/"

