[profile.default]
src = "src"
out = "out"
libs = ["lib"]
solc = "0.8.20"
optimizer = true
optimizer_runs = 200
via_ir = false
fuzz = { runs = 1000 }
invariant = { runs = 1000, depth = 15 }
gas_reports = ["*"]
remappings = [
    "@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/",
    "@forge-std/=lib/forge-std/src/"
]

[dependencies]
"OpenZeppelin/openzeppelin-contracts" = "v5.3.0"
"foundry-rs/forge-std" = "v1.9.7"

[rpc_endpoints]

bitroot_test = "https://test-rpc.bitroot.co/"

