# 私钥配置
PRIVATE_KEY=your_private_key_here

# 项目方地址
PROJECT_OWNER=your_project_owner_address_here

# RPC节点配置
MAINNET_RPC_URL=https://eth-mainnet.alchemyapi.io/v2/your-api-key
GOERLI_RPC_URL=https://eth-goerli.alchemyapi.io/v2/your-api-key
SEPOLIA_RPC_URL=https://eth-sepolia.alchemyapi.io/v2/your-api-key
POLYGON_RPC_URL=https://polygon-rpc.com
BSC_RPC_URL=https://bsc-dataseed.binance.org
ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc
ARBITRUM_GOERLI_RPC_URL=https://goerli-rollup.arbitrum.io/rpc
BITROOT_TEST_RPC_URL=https://test-rpc.bitroot.co/

# API密钥配置
ETHERSCAN_API_KEY=your_etherscan_api_key_here

# 合约配置
EXCHANGE_RATE=1000  # A代币到B代币的兑换率
EXCHANGE_DURATION=5184000  # 60天的秒数 (60 * 24 * 60 * 60)

# 代币配置
TOKEN_NAME=YourToken
TOKEN_SYMBOL=YTK
TOKEN_DECIMALS=18
INITIAL_SUPPLY=1000000000000000000000000  # 1,000,000 tokens

# 存储配置
STORAGE_CONTRACT_1=******************************************
STORAGE_CONTRACT_2=******************************************
STORAGE_CONTRACT_3=******************************************

# 黑名单配置
BLACKLISTED_ADDRESSES=******************************************

# 测试配置
TEST_PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
TEST_ADDRESS=****************************************** 