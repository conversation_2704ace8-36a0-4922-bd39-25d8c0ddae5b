[{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "uint256", "name": "opIndex", "type": "uint256"}, {"internalType": "string", "name": "reason", "type": "string"}, {"internalType": "bytes", "name": "inner", "type": "bytes"}], "name": "FailedOpWithRevert", "type": "error"}, {"inputs": [{"internalType": "bytes", "name": "initCode", "type": "bytes"}], "name": "createSender", "outputs": [{"internalType": "address", "name": "sender", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "entryPoint", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "bytes", "name": "initCallData", "type": "bytes"}], "name": "initEip7702Sender", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]