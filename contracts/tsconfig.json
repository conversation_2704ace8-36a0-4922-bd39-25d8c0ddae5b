{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["./scripts", "./test", "./typechain-types"], "files": ["./hardhat.config.ts"]}