# Token兑换项目

基于Foundry框架实现的A代币到B代币兑换系统，支持销毁A代币并发放B代币的机制。

## 项目概述

本项目实现了一个安全的代币兑换机制，用户可以将A代币兑换为B代币，其中A代币会被销毁，B代币按照设定的兑换率从兑换合约中发放给用户。项目符合PRD需求，使用OpenZeppelin库确保安全性，并通过完整的测试覆盖率保证代码质量。

## 技术架构

### 核心合约

1. **Token.sol** - 带黑名单功能的ERC20代币合约
   - 继承OpenZeppelin的ERC20、ERC20Burnable、Ownable、ReentrancyGuard
   - 支持黑名单功能，防止向指定地址（如DEX）转账
   - 支持铸造、销毁、暂停转账功能
   - 紧急提取误发代币功能

2. **TokenExchange.sol** - 核心兑换逻辑合约
   - 实现A代币销毁和B代币发放机制
   - 60天兑换期限控制
   - 可配置兑换率（默认1 A代币 = 1000 B代币）
   - 统计功能：总兑换量、总用户数等
   - 项目方回收未兑换代币功能
   - 紧急提取误发代币功能

### 接口定义

- **ITokenWithBlacklist.sol** - 带黑名单功能的代币接口
- **ITokenExchange.sol** - 兑换合约接口

## 核心功能

### 兑换机制
- **兑换流程**: 用户A代币 → 销毁A代币 → 发放B代币
- **兑换率**: 可配置，默认1:1000
- **兑换期限**: 60天
- **用户限制**: 每个用户只能兑换一次

### 安全机制
- **黑名单功能**: 防止向指定地址转账
- **权限控制**: 仅项目方可管理合约
- **重入保护**: 使用OpenZeppelin的ReentrancyGuard
- **转账暂停**: 支持紧急情况下暂停转账

### 管理功能
- **兑换率更新**: 项目方可在兑换期内更新兑换率
- **超期回收**: 兑换期结束后回收未兑换的B代币
- **紧急提取**: 提取误发到合约的代币

## 安装和使用

### 前置要求
- [Foundry](https://book.getfoundry.sh/getting-started/installation)
- Node.js (可选，用于前端)

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd redeemer

# 安装Foundry依赖
forge install

# 安装OpenZeppelin合约库
forge install OpenZeppelin/openzeppelin-contracts
```

### 编译合约

```bash
forge build
```

### 运行测试

```bash
# 运行所有测试
forge test

# 运行特定测试文件
forge test --match-contract TokenTest

# 运行测试并显示详细输出
forge test -vvv

# 生成测试覆盖率报告
forge coverage
```

### 部署合约

1. 复制环境变量文件：
```bash
cp env.example .env
```

2. 填写必要的环境变量：
```env
PRIVATE_KEY=your_private_key
ARBITRUM_RPC_URL=your_arbitrum_rpc_url
ARBITRUM_GOERLI_RPC_URL=your_arbitrum_goerli_rpc_url
ARBISCAN_API_KEY=your_arbiscan_api_key
```

3. 部署到测试网：
```bash
# 部署到Arbitrum Goerli测试网
forge script script/DeployTokenExchange.s.sol --rpc-url arbitrum_goerli --broadcast --verify

# 部署到Arbitrum主网
forge script script/DeployTokenExchange.s.sol --rpc-url arbitrum --broadcast --verify
```

## 测试覆盖率

项目拥有完整的测试套件，包含72个测试用例：

### 测试文件
- **Token.t.sol** (31个测试) - Token合约功能测试
- **TokenExchange.t.sol** (28个测试) - TokenExchange基础功能测试  
- **TokenExchangeExtended.t.sol** (13个测试) - TokenExchange扩展功能测试

### 覆盖率统计
- **Token.sol**: 100%行覆盖率，95.83%分支覆盖率，100%函数覆盖率
- **TokenExchange.sol**: 100%行覆盖率，95.24%分支覆盖率，100%函数覆盖率
- **总体**: 95.45%分支覆盖率，91.67%函数覆盖率

### 测试类型
- 单元测试：测试各个函数的基本功能
- 集成测试：测试合约间的交互
- 边界测试：测试极值和边界条件
- 模糊测试：使用随机输入测试合约健壮性
- 安全测试：测试权限控制和重入攻击防护

## 使用示例

### 基本兑换流程

```solidity
// 1. 用户批准A代币转账
tokenA.approve(address(exchange), amount);

// 2. 执行兑换
exchange.exchange(amount);

// 3. 用户收到B代币，A代币被销毁
```

### 项目方管理

```solidity
// 更新兑换率
exchange.updateExchangeRate(newRate);

// 兑换期结束后回收剩余代币
exchange.reclaimUnclaimed();

// 添加地址到黑名单
tokenA.addToBlacklist(address);
```

## 合约地址

部署后的合约地址将在此更新：

### Arbitrum Goerli 测试网
- Token A: `待部署`
- Token B: `待部署`
- TokenExchange: `待部署`

### Arbitrum 主网
- Token A: `待部署`
- Token B: `待部署`
- TokenExchange: `待部署`

## 安全考虑

1. **权限管理**: 所有管理功能都有适当的权限控制
2. **重入保护**: 使用OpenZeppelin的ReentrancyGuard防止重入攻击
3. **整数溢出**: Solidity 0.8.20内置溢出保护
4. **黑名单机制**: 防止向不当地址转账
5. **紧急功能**: 支持紧急暂停和资产提取

## 开发团队

本项目基于PRD需求开发，使用Foundry框架和OpenZeppelin库，确保代码质量和安全性。

## 许可证

MIT License

## 技术支持

如有技术问题，请提交Issue或联系开发团队。
 