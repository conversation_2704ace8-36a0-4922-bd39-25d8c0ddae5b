'use client'

import React, { useEffect, useState } from 'react'
import { useAccount } from 'wagmi'
import Image from 'next/image'

interface Transaction {
  hash: string
  fromAmount: string
  toAmount: string
  timestamp: number
  status: 'success' | 'failed'
}

export function TransactionHistory() {
  const { address, isConnected } = useAccount()
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(false)

  // 模拟数据，稍后连接真实合约
  useEffect(() => {
    if (!isConnected || !address) return
    
    // 模拟加载
    setLoading(true)
    setTimeout(() => {
      setTransactions([
        {
          hash: '0x1234567890abcdef1234567890abcdef12345678',
          fromAmount: '100.0000',
          toAmount: '100000.00',
          timestamp: Date.now() / 1000 - 3600,
          status: 'success'
        },
        {
          hash: '0xabcdef1234567890abcdef1234567890abcdef12',
          fromAmount: '50.0000',
          toAmount: '50000.00',
          timestamp: Date.now() / 1000 - 7200,
          status: 'success'
        }
      ])
      setLoading(false)
    }, 1000)
  }, [isConnected, address])

  if (!isConnected) {
    return (
      <div className="w-full max-w-lg rounded-card bg-card p-6">
        <h3 className="text-xl font-semibold text-primary-text mb-4">交易历史</h3>
        <p className="text-secondary-text text-center py-8">请先连接钱包查看交易历史</p>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="w-full max-w-lg rounded-card bg-card p-6">
        <h3 className="text-xl font-semibold text-primary-text mb-4">交易历史</h3>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-lg rounded-card bg-card p-6">
      <h3 className="text-xl font-semibold text-primary-text mb-4">交易历史</h3>
      
      {transactions.length === 0 ? (
        <p className="text-secondary-text text-center py-8">暂无交易记录</p>
      ) : (
        <div className="space-y-3">
          {transactions.slice(0, 5).map((tx) => (
            <div key={tx.hash} className="rounded-lg bg-main p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${tx.status === 'success' ? 'bg-primary' : 'bg-red-500'}`} />
                  <span className="text-sm font-medium text-primary-text">
                    {tx.status === 'success' ? '兑换成功' : '兑换失败'}
                  </span>
                </div>
                <span className="text-xs text-secondary-text">
                  {new Date(tx.timestamp * 1000).toLocaleString()}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Image src="/assets/tokenA.svg" alt="TokenA" width={20} height={20} />
                  <span className="text-sm text-secondary-text">-{tx.fromAmount} BRT</span>
                </div>
                <div className="text-secondary-text">→</div>
                <div className="flex items-center gap-2">
                  <Image src="/assets/tokenB.svg" alt="TokenB" width={20} height={20} />
                  <span className="text-sm text-primary-text">+{tx.toAmount} BRT(one)</span>
                </div>
              </div>
              
              <div className="mt-2 text-xs text-secondary-text">
                <span className="hover:text-primary transition-colors cursor-pointer">
                  {tx.hash.slice(0, 10)}...{tx.hash.slice(-8)}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
} 