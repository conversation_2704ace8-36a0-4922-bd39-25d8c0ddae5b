'use client'

import React, { useState } from 'react'
import { useAccount } from 'wagmi'
import { formatUnits } from 'viem'
import { useTransactionHistory } from '@/hooks/useTransactionHistory'

export function TransactionHistory() {
  const { address, isConnected } = useAccount()
  const { transactions, isLoading, error, refreshTransactions } = useTransactionHistory()
  const [showAll, setShowAll] = useState(false)
  const [sortBy, setSortBy] = useState<'timestamp' | 'amount'>('timestamp')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [filterAmount, setFilterAmount] = useState('')

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString()
  }

  const truncateAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`
  }

  const truncateHash = (hash: string) => {
    return `${hash.slice(0, 10)}...${hash.slice(-8)}`
  }

  // 过滤和排序交易
  const processedTransactions = React.useMemo(() => {
    let filtered = showAll ?
      transactions :
      transactions.filter(tx => tx.userAddress.toLowerCase() === address?.toLowerCase())

    // 按金额过滤
    if (filterAmount) {
      const filterValue = parseFloat(filterAmount)
      if (!isNaN(filterValue)) {
        filtered = filtered.filter(tx => {
          const amount = parseFloat(formatUnits(BigInt(tx.oldTokenAmount), 18))
          return amount >= filterValue
        })
      }
    }

    // 排序
    filtered.sort((a, b) => {
      if (sortBy === 'timestamp') {
        return sortOrder === 'desc' ? b.timestamp - a.timestamp : a.timestamp - b.timestamp
      } else {
        const aAmount = parseFloat(formatUnits(BigInt(a.oldTokenAmount), 18))
        const bAmount = parseFloat(formatUnits(BigInt(b.oldTokenAmount), 18))
        return sortOrder === 'desc' ? bAmount - aAmount : aAmount - bAmount
      }
    })

    return filtered
  }, [transactions, showAll, address, filterAmount, sortBy, sortOrder])

  if (!isConnected) {
    return (
      <div className="w-full max-w-4xl mx-auto rounded-card bg-card p-6">
        <h2 className="text-xl font-semibold text-primary-text mb-4">Transaction History</h2>
        <p className="text-secondary-text">Please connect your wallet to view transaction history.</p>
      </div>
    )
  }

  return (
    <div className="w-full max-w-4xl mx-auto rounded-card bg-card p-6">
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 gap-4">
        <h2 className="text-xl font-semibold text-primary-text">Transaction History</h2>

        {/* 控制面板 */}
        <div className="flex flex-wrap items-center gap-4">
          {/* 过滤器 */}
          <div className="flex items-center gap-2">
            <label className="text-sm text-secondary-text">Min Amount:</label>
            <input
              type="number"
              placeholder="0.0"
              value={filterAmount}
              onChange={(e) => setFilterAmount(e.target.value)}
              className="w-20 px-2 py-1 text-sm rounded border border-border bg-main text-primary-text"
            />
          </div>

          {/* 排序控制 */}
          <div className="flex items-center gap-2">
            <label className="text-sm text-secondary-text">Sort by:</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'timestamp' | 'amount')}
              className="px-2 py-1 text-sm rounded border border-border bg-main text-primary-text"
            >
              <option value="timestamp">Time</option>
              <option value="amount">Amount</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
              className="px-2 py-1 text-sm rounded border border-border bg-main text-primary-text hover:bg-border transition-colors"
            >
              {sortOrder === 'desc' ? '↓' : '↑'}
            </button>
          </div>

          {/* 显示所有交易开关 */}
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={showAll}
              onChange={(e) => setShowAll(e.target.checked)}
              className="rounded border-border bg-main"
            />
            <span className="text-secondary-text">Show all transactions</span>
          </label>

          {/* 刷新按钮 */}
          <button
            onClick={refreshTransactions}
            disabled={isLoading}
            className="px-4 py-2 bg-primary text-main rounded-lg font-medium hover:opacity-90 disabled:opacity-50 transition-all"
          >
            {isLoading ? 'Loading...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-main rounded-lg p-4">
          <div className="text-sm text-secondary-text">Total Transactions</div>
          <div className="text-xl font-semibold text-primary-text">{processedTransactions.length}</div>
        </div>
        <div className="bg-main rounded-lg p-4">
          <div className="text-sm text-secondary-text">Total BRT Exchanged</div>
          <div className="text-xl font-semibold text-primary-text">
            {processedTransactions.reduce((sum, tx) =>
              sum + parseFloat(formatUnits(BigInt(tx.oldTokenAmount), 18)), 0
            ).toFixed(4)}
          </div>
        </div>
        <div className="bg-main rounded-lg p-4">
          <div className="text-sm text-secondary-text">Total BRT(one) Received</div>
          <div className="text-xl font-semibold text-primary">
            {processedTransactions.reduce((sum, tx) =>
              sum + parseFloat(formatUnits(BigInt(tx.newTokenAmount), 18)), 0
            ).toFixed(4)}
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
          <p className="text-red-400 text-sm">Error: {error}</p>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : processedTransactions.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-secondary-text">
            {filterAmount ? 'No transactions match the filter criteria.' : 'No transactions found.'}
          </p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="text-left py-3 px-4 text-secondary-text font-medium">Transaction</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">User</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">BRT Amount</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">BRT(one) Amount</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">Date</th>
                <th className="text-left py-3 px-4 text-secondary-text font-medium">Block</th>
              </tr>
            </thead>
            <tbody>
              {processedTransactions.map((tx) => (
                <tr key={tx.transactionHash} className="border-b border-border/50 hover:bg-main/50 transition-colors">
                  <td className="py-3 px-4">
                    <a
                      href={`https://test-rpc.bitroot.co/tx/${tx.transactionHash}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline font-mono text-sm"
                    >
                      {truncateHash(tx.transactionHash)}
                    </a>
                  </td>
                  <td className="py-3 px-4">
                    <span className="font-mono text-sm text-primary-text">
                      {truncateAddress(tx.userAddress)}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-primary-text font-medium">
                      {parseFloat(formatUnits(BigInt(tx.oldTokenAmount), 18)).toFixed(4)} BRT
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-primary font-medium">
                      {parseFloat(formatUnits(BigInt(tx.newTokenAmount), 18)).toFixed(4)} BRT(one)
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-secondary-text text-sm">
                      {formatDate(tx.timestamp)}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-secondary-text text-sm font-mono">
                      #{tx.blockNumber}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}